<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存清理和配置验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 缓存清理和配置验证工具</h1>
            <p>解决浏览器缓存导致的配置不生效问题</p>
        </div>

        <div class="section">
            <h3>📋 问题诊断</h3>
            <div class="instructions">
                <strong>发现的问题：</strong><br>
                • 独立测试页面：API调用成功（9秒内返回）<br>
                • 实际系统：API调用失败（显示6秒超时）<br>
                • 根因：浏览器缓存了旧版本的 gemini-caller.js<br>
                • 解决方案：强制清理缓存并验证配置
            </div>
        </div>

        <div class="section">
            <h3>🚀 缓存清理操作</h3>
            <div class="button-group">
                <button class="btn-danger" onclick="clearBrowserCache()">🗑️ 清理浏览器缓存</button>
                <button class="btn-primary" onclick="forceReloadMainPage()">🔄 强制刷新主页面</button>
                <button class="btn-success" onclick="verifyConfiguration()">✅ 验证当前配置</button>
            </div>
            <div id="cacheStatus" class="status info" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>📊 配置验证结果</h3>
            <div id="configResults" class="config-display">点击"验证当前配置"按钮查看结果...</div>
        </div>

        <div class="section">
            <h3>📝 手动清理指令</h3>
            <div class="instructions">
                <strong>如果自动清理无效，请手动执行以下步骤：</strong><br><br>
                
                <strong>Chrome浏览器：</strong><br>
                1. 按 Ctrl+Shift+Delete 打开清理对话框<br>
                2. 选择"时间范围：全部时间"<br>
                3. 勾选"缓存的图片和文件"<br>
                4. 点击"清除数据"<br>
                5. 重新打开主页面<br><br>
                
                <strong>或者使用开发者工具：</strong><br>
                1. 按 F12 打开开发者工具<br>
                2. 右键点击刷新按钮<br>
                3. 选择"清空缓存并硬性重新加载"<br><br>
                
                <strong>验证方法：</strong><br>
                在主页面控制台中查找：<br>
                <code>[INFO] Gemini API调用器已初始化 {baseTimeout: 15000, version: "2.0"}</code>
            </div>
        </div>

        <div class="section">
            <h3>🎯 预期结果</h3>
            <div class="status success">
清理缓存后，主页面应该显示：
• baseTimeout: 15000 (而不是6000)
• version: "2.0" 
• API调用成功，不再出现超时错误
            </div>
        </div>
    </div>

    <script>
        // 清理浏览器缓存
        function clearBrowserCache() {
            const statusDiv = document.getElementById('cacheStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status info';
            statusDiv.textContent = '正在尝试清理缓存...';

            try {
                // 尝试清理各种缓存
                if ('caches' in window) {
                    caches.keys().then(function(names) {
                        for (let name of names) {
                            caches.delete(name);
                        }
                    });
                }

                // 清理localStorage
                if (typeof(Storage) !== "undefined") {
                    localStorage.clear();
                }

                // 清理sessionStorage
                if (typeof(Storage) !== "undefined") {
                    sessionStorage.clear();
                }

                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 缓存清理完成！请手动刷新主页面验证效果。';

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 自动清理失败，请使用手动清理方法。\n错误: ' + error.message;
            }
        }

        // 强制刷新主页面
        function forceReloadMainPage() {
            const statusDiv = document.getElementById('cacheStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status info';
            statusDiv.textContent = '正在强制刷新主页面...';

            try {
                // 打开主页面并强制刷新
                const mainPageUrl = 'file:///C:/Users/<USER>/Downloads/live%201.0%20create%20job%20GMH/index.html';
                const newWindow = window.open(mainPageUrl, '_blank');
                
                setTimeout(() => {
                    if (newWindow) {
                        newWindow.location.reload(true); // 强制从服务器重新加载
                    }
                }, 1000);

                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 主页面已在新窗口中打开并强制刷新。';

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 无法自动打开主页面，请手动打开并按 Ctrl+F5 强制刷新。';
            }
        }

        // 验证当前配置
        function verifyConfiguration() {
            const resultsDiv = document.getElementById('configResults');
            
            const verification = {
                timestamp: new Date().toISOString(),
                expectedConfig: {
                    baseTimeout: 15000,
                    version: "2.0",
                    maxRetries: 2,
                    retryDelays: [1000, 2000]
                },
                instructions: [
                    "1. 打开主页面 (index.html)",
                    "2. 按 F12 打开开发者工具",
                    "3. 查看控制台中的初始化日志",
                    "4. 寻找: [INFO] Gemini API调用器已初始化",
                    "5. 确认显示: baseTimeout: 15000, version: '2.0'",
                    "6. 如果仍显示 baseTimeout: 6000，说明缓存未清理"
                ],
                troubleshooting: [
                    "如果配置仍然错误：",
                    "• 尝试无痕浏览模式打开主页面",
                    "• 或者完全关闭浏览器后重新打开",
                    "• 检查是否有其他标签页在使用旧版本"
                ]
            };

            resultsDiv.textContent = JSON.stringify(verification, null, 2);
        }

        // 页面加载时显示当前状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 缓存清理工具已加载');
            
            // 自动验证配置
            setTimeout(verifyConfiguration, 500);
        });
    </script>
</body>
</html>
