/**
 * 批量操作控件组件
 * 文件: js/pages/multi-order/components/batch-controls.js
 * 角色: 批量操作控件UI组件，提供全选、批量创建等功能
 * 
 * @BATCH_CONTROLS 批量操作控件组件
 * 🏷️ 标签: @OTA_BATCH_CONTROLS_COMPONENT
 * 📝 说明: 模块化的批量操作控件，支持全选、取消全选、批量创建等操作
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 批量操作控件组件类
     * 继承自BaseComponent
     */
    class BatchControls extends window.OTA.Components.BaseComponent {
        constructor(options = {}) {
            super({
                container: options.container,
                autoRender: false,
                enableEvents: true,
                ...options
            });

            // 组件特定配置
            this.config = {
                showSelectAll: options.showSelectAll !== false,
                showDeselectAll: options.showDeselectAll !== false,
                showBatchCreate: options.showBatchCreate !== false,
                showSelectedCount: options.showSelectedCount !== false,
                enableKeyboardShortcuts: options.enableKeyboardShortcuts !== false,
                ...options.config
            };

            // 控件状态
            this.controlState = {
                totalCount: options.totalCount || 0,
                selectedCount: options.selectedCount || 0,
                isProcessing: options.isProcessing || false,
                canCreate: options.canCreate !== false
            };

            // 事件回调
            this.onSelectAll = options.onSelectAll || null;
            this.onDeselectAll = options.onDeselectAll || null;
            this.onBatchCreate = options.onBatchCreate || null;
        }

        /**
         * 初始化状态
         */
        initializeState() {
            this.state.data = {
                ...this.controlState
            };
        }

        /**
         * 渲染组件
         */
        render() {
            if (!this.elements.container) {
                throw new Error('批量控件组件需要容器元素');
            }

            // 生成控件HTML
            const controlsHTML = this.generateControlsHTML();
            
            // 创建控件元素
            const controlsElement = document.createElement('div');
            controlsElement.innerHTML = controlsHTML;
            this.elements.root = controlsElement.firstElementChild;

            // 添加到容器
            this.elements.container.appendChild(this.elements.root);

            // 绑定事件
            this.bindControlEvents();

            // 更新控件状态
            this.updateControlsState();

            // 标记为已渲染
            this.state.isRendered = true;

            this.logger.log('⚙️ 批量操作控件已渲染', 'info');
        }

        /**
         * 生成控件HTML
         * @returns {string} HTML字符串
         */
        generateControlsHTML() {
            return `
                <div class="batch-controls" data-component-id="${this.componentId}">
                    <div class="batch-controls-left">
                        ${this.config.showSelectedCount ? `
                            <div class="selected-count" id="selectedCount-${this.componentId}">
                                已选择 ${this.state.data.selectedCount} 个订单 (共 ${this.state.data.totalCount} 个)
                            </div>
                        ` : ''}
                        
                        ${this.config.showSelectAll ? `
                            <button type="button" 
                                    class="batch-btn select-all-btn" 
                                    id="selectAllBtn-${this.componentId}"
                                    title="全选所有订单 (Ctrl+A)">
                                ✅ 全选
                            </button>
                        ` : ''}
                        
                        ${this.config.showDeselectAll ? `
                            <button type="button" 
                                    class="batch-btn deselect-all-btn" 
                                    id="deselectAllBtn-${this.componentId}"
                                    title="取消选择所有订单 (Ctrl+D)">
                                ❌ 取消全选
                            </button>
                        ` : ''}
                    </div>
                    
                    <div class="batch-controls-right">
                        ${this.config.showBatchCreate ? `
                            <button type="button" 
                                    class="batch-btn primary batch-create-btn" 
                                    id="batchCreateBtn-${this.componentId}"
                                    title="创建选中的订单 (Ctrl+Enter)">
                                <span class="btn-icon">🚀</span>
                                <span class="btn-text">创建选中订单 (${this.state.data.selectedCount})</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        /**
         * 绑定控件事件
         */
        bindControlEvents() {
            if (!this.elements.root) return;

            // 全选按钮
            const selectAllBtn = this.elements.root.querySelector('.select-all-btn');
            if (selectAllBtn) {
                this.addEventListener(selectAllBtn, 'click', () => {
                    this.handleSelectAll();
                });
            }

            // 取消全选按钮
            const deselectAllBtn = this.elements.root.querySelector('.deselect-all-btn');
            if (deselectAllBtn) {
                this.addEventListener(deselectAllBtn, 'click', () => {
                    this.handleDeselectAll();
                });
            }

            // 批量创建按钮
            const batchCreateBtn = this.elements.root.querySelector('.batch-create-btn');
            if (batchCreateBtn) {
                this.addEventListener(batchCreateBtn, 'click', () => {
                    this.handleBatchCreate();
                });
            }

            // 键盘快捷键
            if (this.config.enableKeyboardShortcuts) {
                this.bindKeyboardShortcuts();
            }
        }

        /**
         * 绑定键盘快捷键
         */
        bindKeyboardShortcuts() {
            this.addEventListener(document, 'keydown', (e) => {
                // 检查是否在多订单页面
                if (!this.isInMultiOrderPage()) return;

                if (e.ctrlKey || e.metaKey) {
                    switch (e.key.toLowerCase()) {
                        case 'a':
                            e.preventDefault();
                            this.handleSelectAll();
                            break;
                        case 'd':
                            e.preventDefault();
                            this.handleDeselectAll();
                            break;
                        case 'enter':
                            e.preventDefault();
                            this.handleBatchCreate();
                            break;
                    }
                }
            });
        }

        /**
         * 检查是否在多订单页面
         * @returns {boolean} 是否在多订单页面
         */
        isInMultiOrderPage() {
            return window.location.hash === '#/multi-order' || 
                   document.querySelector('.multi-order-panel:not(.hidden)');
        }

        /**
         * 处理全选
         */
        handleSelectAll() {
            if (this.state.data.isProcessing) return;

            this.logger.log('✅ 执行全选操作', 'info');
            
            if (this.onSelectAll) {
                this.onSelectAll();
            }
        }

        /**
         * 处理取消全选
         */
        handleDeselectAll() {
            if (this.state.data.isProcessing) return;

            this.logger.log('❌ 执行取消全选操作', 'info');
            
            if (this.onDeselectAll) {
                this.onDeselectAll();
            }
        }

        /**
         * 处理批量创建
         */
        handleBatchCreate() {
            if (this.state.data.isProcessing || this.state.data.selectedCount === 0) return;

            this.logger.log(`🚀 执行批量创建操作: ${this.state.data.selectedCount} 个订单`, 'info');
            
            if (this.onBatchCreate) {
                this.onBatchCreate(this.state.data.selectedCount);
            }
        }

        /**
         * 更新控件状态
         * @param {Object} newState - 新状态
         */
        updateState(newState = {}) {
            // 更新内部状态
            this.state.data = {
                ...this.state.data,
                ...newState
            };

            // 更新UI
            this.updateControlsState();
        }

        /**
         * 更新控件UI状态
         */
        updateControlsState() {
            if (!this.elements.root) return;

            const { totalCount, selectedCount, isProcessing, canCreate } = this.state.data;

            // 更新选中数量显示
            const selectedCountElement = this.elements.root.querySelector(`#selectedCount-${this.componentId}`);
            if (selectedCountElement) {
                selectedCountElement.textContent = `已选择 ${selectedCount} 个订单 (共 ${totalCount} 个)`;
            }

            // 更新全选按钮状态
            const selectAllBtn = this.elements.root.querySelector('.select-all-btn');
            if (selectAllBtn) {
                selectAllBtn.disabled = selectedCount === totalCount || isProcessing;
                selectAllBtn.classList.toggle('disabled', selectAllBtn.disabled);
            }

            // 更新取消全选按钮状态
            const deselectAllBtn = this.elements.root.querySelector('.deselect-all-btn');
            if (deselectAllBtn) {
                deselectAllBtn.disabled = selectedCount === 0 || isProcessing;
                deselectAllBtn.classList.toggle('disabled', deselectAllBtn.disabled);
            }

            // 更新批量创建按钮状态
            const batchCreateBtn = this.elements.root.querySelector('.batch-create-btn');
            if (batchCreateBtn) {
                const canBatchCreate = selectedCount > 0 && !isProcessing && canCreate;
                batchCreateBtn.disabled = !canBatchCreate;
                batchCreateBtn.classList.toggle('disabled', !canBatchCreate);

                // 更新按钮文本
                const btnText = batchCreateBtn.querySelector('.btn-text');
                if (btnText) {
                    if (isProcessing) {
                        btnText.textContent = '⏳ 处理中...';
                    } else if (selectedCount === 0) {
                        btnText.textContent = '请选择订单';
                    } else {
                        btnText.textContent = `创建选中订单 (${selectedCount})`;
                    }
                }
            }
        }

        /**
         * 设置处理状态
         * @param {boolean} isProcessing - 是否正在处理
         */
        setProcessing(isProcessing) {
            this.updateState({ isProcessing });
        }

        /**
         * 设置选中数量
         * @param {number} selectedCount - 选中数量
         * @param {number} totalCount - 总数量（可选）
         */
        setSelectedCount(selectedCount, totalCount = null) {
            const newState = { selectedCount };
            if (totalCount !== null) {
                newState.totalCount = totalCount;
            }
            this.updateState(newState);
        }

        /**
         * 设置是否可以创建
         * @param {boolean} canCreate - 是否可以创建
         */
        setCanCreate(canCreate) {
            this.updateState({ canCreate });
        }

        /**
         * 获取当前状态
         * @returns {Object} 当前状态
         */
        getCurrentState() {
            return { ...this.state.data };
        }

        /**
         * 显示成功提示
         * @param {string} message - 提示消息
         */
        showSuccess(message) {
            this.showToast(message, 'success');
        }

        /**
         * 显示错误提示
         * @param {string} message - 错误消息
         */
        showError(message) {
            this.showToast(message, 'error');
        }

        /**
         * 显示提示消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         */
        showToast(message, type = 'info') {
            // 创建临时提示元素
            const toast = document.createElement('div');
            toast.className = `batch-controls-toast toast-${type}`;
            toast.textContent = message;
            
            // 添加到控件容器
            if (this.elements.root) {
                this.elements.root.appendChild(toast);
                
                // 显示动画
                setTimeout(() => toast.classList.add('show'), 10);
                
                // 自动移除
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        }

        /**
         * 启用控件
         */
        onEnable() {
            this.updateState({ canCreate: true });
        }

        /**
         * 禁用控件
         */
        onDisable() {
            this.updateState({ canCreate: false });
        }

        /**
         * 销毁回调
         */
        onDestroy() {
            // 清理回调函数
            this.onSelectAll = null;
            this.onDeselectAll = null;
            this.onBatchCreate = null;
        }
    }

    // 注册组件到组件注册中心
    if (window.OTA?.Components?.registry) {
        window.OTA.Components.registry.register('BatchControls', BatchControls, {
            singleton: false,
            autoCreate: false
        });
    }

    // 暴露到OTA命名空间
    window.OTA.Components.BatchControls = BatchControls;

    console.log('✅ 批量操作控件组件已加载');

})();
