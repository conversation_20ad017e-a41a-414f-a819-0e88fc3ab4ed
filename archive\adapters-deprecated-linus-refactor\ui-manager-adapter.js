/**
 * ============================================================================
 * 🚀 核心业务流程 - UI管理器适配器 (兼容性保证)
 * ============================================================================
 *
 * @fileoverview UI管理器适配器 - 兼容性保证
 * @description 保持向后兼容性，将旧的UI管理API调用适配到新的母子两层架构
 * 
 * @businessFlow UI管理适配
 * 在核心业务流程中的作用：
 * 旧代码调用 → 【当前文件职责】UI API适配和转换
 *     ↓
 * 调用新的母子两层架构 → 返回兼容格式的结果
 *
 * @architecture Adapter Layer (适配器层)
 * - 职责：新旧UI管理架构之间的桥梁
 * - 原则：保持完全的向后兼容性
 * - 接口：提供与原ui-manager.js相同的API
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - 现有代码的uiManager调用
 * 下游依赖：
 * - controllers/business-flow-controller.js (业务流程控制)
 * - controllers/order-management-controller.js (订单管理控制)
 * - 原有的ui-manager.js (UI实现)
 *
 * @localProcessing 本地处理职责
 * - 🟢 UI API调用适配和转换
 * - 🟢 事件处理和状态同步
 * - 🟢 界面更新和交互管理
 * - 🟢 错误处理和用户反馈
 *
 * @remoteProcessing 远程处理职责
 * - 🔴 无远程处理职责 (纯适配器)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-09
 */

(function() {
    'use strict';

    /**
     * @ADAPTER UI管理器适配器
     * 提供与原ui-manager.js兼容的API接口，同时集成新架构
     */
    class UIManagerAdapter {
        constructor() {
            this.logger = window.getLogger(); // 统一日志服务
            // 减法修复：移除立即获取appState，改为延迟获取，避免加载时序问题
            this._appState = null; // 延迟初始化的appState引用

            // 新架构组件引用
            this.businessFlowController = null; // 延迟初始化
            this.orderManagementController = null; // 延迟初始化

            // 原有UI管理器引用 (保持现有UI功能)
            this.originalUIManager = null; // 延迟初始化

            // 适配器状态
            this.isInitialized = false;
            this.initializationPromise = null;

            this.logger.log('🔄 UIManagerAdapter 初始化开始', 'info');
            this.initializeAsync();
        }

        /**
         * 延迟获取AppState实例
         * 减法修复：避免在构造函数中立即获取，解决加载时序问题
         * @returns {Object} AppState实例
         */
        _getAppState() {
            if (!this._appState) {
                this._appState = window.getAppState();
            }
            return this._appState;
        }

        /**
         * 异步初始化组件
         */
        async initializeAsync() {
            if (this.initializationPromise) {
                return this.initializationPromise;
            }

            this.initializationPromise = this._doInitialize();
            return this.initializationPromise;
        }

        async _doInitialize() {
            try {
                // 等待组件加载
                await this._waitForComponents();
                
                // 获取新架构组件
                this.businessFlowController = window.OTA.businessFlowController;
                this.orderManagementController = window.OTA.orderManagementController;
                
                // 获取原有UI管理器 (如果存在)
                this.originalUIManager = window.OTA.originalUIManager || window.OTA.uiManager;
                
                this.isInitialized = true;
                this.logger.log('✅ UIManagerAdapter 初始化完成', 'success');
                
                return true;
            } catch (error) {
                this.logger.log(`❌ UIManagerAdapter 初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        /**
         * 等待组件加载完成
         */
        async _waitForComponents() {
            const maxWaitTime = 10000; // 最大等待10秒
            const checkInterval = 100; // 每100ms检查一次
            let waitTime = 0;

            while (waitTime < maxWaitTime) {
                if (window.OTA?.businessFlowController && 
                    window.OTA?.orderManagementController) {
                    return true;
                }
                
                await new Promise(resolve => setTimeout(resolve, checkInterval));
                waitTime += checkInterval;
            }
            
            throw new Error('新架构控制器加载超时');
        }

        /**
         * 确保适配器已初始化
         */
        async _ensureInitialized() {
            if (!this.isInitialized) {
                await this.initializeAsync();
            }
            
            if (!this.isInitialized) {
                throw new Error('UIManagerAdapter 未能正确初始化');
            }
        }

        // ============================================================================
        // 🔄 兼容性API方法 - 与原ui-manager.js保持一致
        // ============================================================================

        /**
         * 更新订单显示 (兼容性方法)
         * @param {Object} orderData - 订单数据
         */
        async updateOrderDisplay(orderData) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [UI适配器] 更新订单显示...', 'info');
                
                // 如果有原有UI管理器，优先使用
                if (this.originalUIManager && typeof this.originalUIManager.updateOrderDisplay === 'function') {
                    return await this.originalUIManager.updateOrderDisplay(orderData);
                }
                
                // 否则使用新架构的业务流程控制器
                return await this.businessFlowController.updateOrderDisplay(orderData);
                
            } catch (error) {
                this.logger.log(`❌ [UI适配器] 更新订单显示失败: ${error.message}`, 'error');
                throw error;
            }
        }

        /**
         * 显示多订单面板 (兼容性方法)
         * @param {Array} orders - 订单数组
         */
        async showMultiOrderPanel(orders) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [UI适配器] 显示多订单面板...', 'info');
                
                // 如果有原有UI管理器，优先使用
                if (this.originalUIManager && typeof this.originalUIManager.showMultiOrderPanel === 'function') {
                    return await this.originalUIManager.showMultiOrderPanel(orders);
                }
                
                // 否则使用新架构的订单管理控制器
                return await this.orderManagementController.showMultiOrderPanel(orders);
                
            } catch (error) {
                this.logger.log(`❌ [UI适配器] 显示多订单面板失败: ${error.message}`, 'error');
                throw error;
            }
        }

        /**
         * 更新状态显示 (兼容性方法)
         * @param {string} status - 状态信息
         * @param {string} type - 状态类型
         */
        updateStatus(status, type = 'info') {
            try {
                // 如果有原有UI管理器，优先使用
                if (this.originalUIManager && typeof this.originalUIManager.updateStatus === 'function') {
                    return this.originalUIManager.updateStatus(status, type);
                }
                
                // 否则使用基础的状态更新
                this.logger.log(`📊 [UI状态] ${status}`, type);
                
                // 更新状态栏 (如果存在)
                const statusElement = document.getElementById('dataStatus');
                if (statusElement) {
                    statusElement.textContent = status;
                }
                
            } catch (error) {
                this.logger.log(`❌ [UI适配器] 更新状态失败: ${error.message}`, 'error');
            }
        }

        /**
         * 获取适配器状态
         * @returns {Object} 适配器状态信息
         */
        getAdapterStatus() {
            return {
                isInitialized: this.isInitialized,
                hasBusinessFlowController: !!this.businessFlowController,
                hasOrderManagementController: !!this.orderManagementController,
                hasOriginalUIManager: !!this.originalUIManager,
                timestamp: new Date().toISOString()
            };
        }
    }

    // ============================================================================
    // 🌐 全局注册和暴露
    // ============================================================================

    // 等待OTA命名空间准备就绪
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }

    // 创建适配器实例
    const uiManagerAdapter = new UIManagerAdapter();

    // 注册到OTA命名空间
    window.OTA.UIManagerAdapter = UIManagerAdapter;
    window.OTA.uiManagerAdapter = uiManagerAdapter;

    // 兼容性接口 - 保持与旧版本相同的调用方式
    // 注意：不覆盖原有的uiManager，而是提供适配器
    window.OTA.uiManagerCompat = uiManagerAdapter;
    
    // 工厂函数 (兼容性)
    window.getUIManagerAdapter = function() {
        return uiManagerAdapter;
    };

    console.log('✅ UIManagerAdapter 已加载并注册');
})();
