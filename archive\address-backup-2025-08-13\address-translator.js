/**
 * ============================================================================
 * 🚀 核心业务流程 - 地址翻译器 (简化版 - Gemini集成)
 * ============================================================================
 *
 * @fileoverview 地址翻译器 - Gemini AI集成版本
 * @description 使用Gemini AI和精心设计的提示词进行地址翻译和标准化处理
 *
 * @businessFlow 地址翻译和标准化 (简化版)
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * 订单数据解析 → 【当前文件职责】地址翻译和标准化 - Gemini + 本地数据
 *     ↓
 * 标准化订单数据 → 订单管理
 *
 * @architecture Simplified Architecture - Gemini + Local Data
 * - 职责：使用Gemini AI进行地址翻译和酒店名标准化
 * - 原则：简化依赖，专注AI处理
 * - 接口：为流水线协调器提供Gemini处理服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - flow/order-parser.js (订单解析时调用)
 * - flow/knowledge-base.js (查询酒店和机场数据)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 中文地址到英文/马来文翻译
 * - 🟢 酒店名称标准化和匹配
 * - 🟢 机场代码和名称转换
 * - 🟢 地址格式化和清理
 * - 🟢 翻译结果缓存和优化
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地翻译模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有翻译结果格式
 * - 兼容现有的翻译接口
 * - 保持翻译准确率
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程翻译API（严格本地处理）
 * - ✅ 不能依赖其他子层（除知识库查询）
 * - ✅ 必须保持翻译准确率
 * - ✅ 保持现有的翻译逻辑
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 地址翻译器 - 子层实现
     */
    class AddressTranslator {
        constructor() {
            this.logger = this.getLogger();

            // 翻译配置
            this.config = {
                supportedLanguages: ['en', 'ms'],
                defaultTargetLanguages: ['ms', 'en'],
                cacheTimeout: 30 * 60 * 1000, // 30分钟缓存
                minAddressLength: 3,
                maxAddressLength: 200,

                // 🚀 新增：酒店名标准化配置
                hotelStandardization: {
                    enabled: true,
                    useGoogleApis: true,
                    fallbackToLocal: true,
                    cacheTimeout: 7 * 24 * 60 * 60 * 1000 // 7天缓存
                }
            };

            // 翻译缓存
            this.translationCache = new Map();

            // 🚀 新增：酒店名标准化缓存
            this.hotelStandardizationCache = new Map();

            // 🚀 新增：Google Maps 服务引用
            this.googleMapsService = null;
            
            // 常用地址翻译映射
            this.commonTranslations = new Map([
                // 机场相关
                ['吉隆坡国际机场', { en: 'Kuala Lumpur International Airport', ms: 'Lapangan Terbang Antarabangsa Kuala Lumpur' }],
                ['梳邦机场', { en: 'Sultan Abdul Aziz Shah Airport', ms: 'Lapangan Terbang Sultan Abdul Aziz Shah' }],
                ['KLIA', { en: 'KLIA', ms: 'KLIA' }],
                ['KLIA2', { en: 'KLIA2', ms: 'KLIA2' }],
                
                // 常用地点
                ['双子塔', { en: 'Petronas Twin Towers', ms: 'Menara Berkembar Petronas' }],
                ['市中心', { en: 'City Center', ms: 'Pusat Bandar' }],
                ['武吉免登', { en: 'Bukit Bintang', ms: 'Bukit Bintang' }],
                ['中央车站', { en: 'KL Sentral', ms: 'KL Sentral' }],
                
                // 酒店相关
                ['酒店', { en: 'Hotel', ms: 'Hotel' }],
                ['度假村', { en: 'Resort', ms: 'Resort' }],
                ['公寓', { en: 'Apartment', ms: 'Apartmen' }]
            ]);
            
            this.logger.log('地址翻译器已初始化', 'info');

            // 🚀 新增：延迟初始化 Google Maps 服务
            this.initializeGoogleMapsService();
        }

        /**
         * 初始化 Google Maps 服务
         * @INIT
         */
        async initializeGoogleMapsService() {
            try {
                // 等待 Google Maps 服务可用
                if (window.googleMapsService) {
                    this.googleMapsService = window.googleMapsService;
                    await this.googleMapsService.initialize();
                    this.logger.log('Google Maps 服务已连接', 'success');
                } else {
                    // 延迟重试
                    setTimeout(() => this.initializeGoogleMapsService(), 1000);
                }
            } catch (error) {
                this.logger.log('Google Maps 服务初始化失败', 'warn', { error: error.message });
                // 继续使用本地翻译功能
            }
        }

        /**
         * 翻译地址
         * @param {string} address - 需要翻译的地址
         * @param {array} targetLanguages - 目标语言数组
         * @returns {Promise<object>} 翻译结果
         */
        async translateAddress(address, targetLanguages = ['ms', 'en']) {
            try {
                if (!address || typeof address !== 'string') {
                    throw new Error('无效的地址输入');
                }

                const cleanAddress = address.trim();
                if (cleanAddress.length < this.config.minAddressLength) {
                    return { original: address, translations: {} };
                }

                this.logger.log('开始翻译地址', 'info', { 
                    address: cleanAddress,
                    targetLanguages 
                });

                // 检查缓存
                const cacheKey = `${cleanAddress}_${targetLanguages.join('_')}`;
                const cachedResult = this.getCachedTranslation(cacheKey);
                if (cachedResult) {
                    this.logger.log('使用缓存翻译结果', 'info');
                    return cachedResult;
                }

                // 执行翻译
                const translations = {};
                
                for (const lang of targetLanguages) {
                    const translation = await this.translateToLanguage(cleanAddress, lang);
                    if (translation) {
                        translations[lang] = translation;
                    }
                }

                const result = {
                    original: cleanAddress,
                    translations: translations,
                    translatedAt: new Date().toISOString()
                };

                // 缓存结果
                this.setCachedTranslation(cacheKey, result);

                this.logger.log('地址翻译完成', 'success', { 
                    address: cleanAddress,
                    translationCount: Object.keys(translations).length 
                });

                return result;

            } catch (error) {
                this.logger.log('地址翻译失败', 'error', { error: error.message });
                return { 
                    original: address, 
                    translations: {}, 
                    error: error.message 
                };
            }
        }

        /**
         * 翻译到指定语言
         * @param {string} address - 地址
         * @param {string} targetLanguage - 目标语言
         * @returns {Promise<string|null>} 翻译结果
         */
        async translateToLanguage(address, targetLanguage) {
            try {
                // 1. 检查常用翻译映射
                const commonTranslation = this.commonTranslations.get(address);
                if (commonTranslation && commonTranslation[targetLanguage]) {
                    return commonTranslation[targetLanguage];
                }

                // 2. 查询知识库
                const knowledgeBase = window.OTA?.knowledgeBase;
                if (knowledgeBase) {
                    // 查询酒店数据
                    const hotelInfo = knowledgeBase.queryHotel(address);
                    if (hotelInfo && targetLanguage === 'en' && hotelInfo.english) {
                        return hotelInfo.english;
                    }

                    // 查询机场数据
                    const airportInfo = knowledgeBase.queryAirport(address);
                    if (airportInfo) {
                        if (targetLanguage === 'en') {
                            return airportInfo.name;
                        }
                        if (targetLanguage === 'ms' && airportInfo.malay) {
                            return airportInfo.malay;
                        }
                    }
                }

                // 3. 基于规则的翻译
                return this.ruleBasedTranslation(address, targetLanguage);

            } catch (error) {
                this.logger.log('语言翻译失败', 'error', { 
                    address, 
                    targetLanguage, 
                    error: error.message 
                });
                return null;
            }
        }

        /**
         * 基于规则的翻译
         * @param {string} address - 地址
         * @param {string} targetLanguage - 目标语言
         * @returns {string|null} 翻译结果
         */
        ruleBasedTranslation(address, targetLanguage) {
            // 简单的规则翻译
            if (targetLanguage === 'en') {
                // 检查是否已经是英文
                if (/^[a-zA-Z\s\d,.-]+$/.test(address)) {
                    return address; // 已经是英文，直接返回
                }
            }

            // 如果无法翻译，返回原地址
            return address;
        }

        /**
         * 🚀 新增：酒店名标准化处理
         * 使用三层优先级：Places API → Knowledge Graph → Translation API
         * @param {string} hotelName - 原始酒店名（任意语言）
         * @param {object} options - 选项配置
         * @returns {Promise<object>} 标准化结果
         * @SERVICE
         */
        async standardizeHotelName(hotelName, options = {}) {
            try {
                if (!hotelName || typeof hotelName !== 'string') {
                    return {
                        success: false,
                        error: '无效的酒店名输入',
                        originalName: hotelName
                    };
                }

                const cleanName = hotelName.trim();
                if (cleanName.length < 2) {
                    return {
                        success: false,
                        error: '酒店名太短',
                        originalName: hotelName
                    };
                }

                this.logger.log('开始酒店名标准化', 'info', { hotelName: cleanName });

                // 检查缓存
                const cacheKey = `hotel_${cleanName}`;
                const cachedResult = this.getHotelStandardizationCache(cacheKey);
                if (cachedResult) {
                    this.logger.log('使用缓存的酒店名标准化结果', 'info');
                    return cachedResult;
                }

                // 执行三层优先级处理
                const result = await this.executeHotelStandardizationPipeline(cleanName, options);

                // 缓存结果
                if (result.success) {
                    this.setHotelStandardizationCache(cacheKey, result);
                }

                this.logger.log('酒店名标准化完成', 'success', {
                    originalName: cleanName,
                    standardizedName: result.standardizedName,
                    source: result.source,
                    confidence: result.confidence
                });

                return result;

            } catch (error) {
                this.logger.log('酒店名标准化失败', 'error', {
                    hotelName,
                    error: error.message
                });

                return {
                    success: false,
                    error: error.message,
                    originalName: hotelName,
                    standardizedName: hotelName, // 降级返回原名
                    source: 'error_fallback',
                    confidence: 0.0
                };
            }
        }

        /**
         * 🚀 简化版：执行酒店名标准化流水线 (Gemini + 本地数据)
         * @param {string} hotelName - 酒店名
         * @param {object} options - 选项
         * @returns {Promise<object>} 标准化结果
         * @PIPELINE
         */
        async executeHotelStandardizationPipeline(hotelName, options) {
            const pipeline = [
                { name: 'local_data', method: this.standardizeWithLocalData.bind(this) },
                { name: 'gemini_ai', method: this.standardizeWithGemini.bind(this) }
            ];

            for (const step of pipeline) {
                try {
                    this.logger.log(`尝试使用 ${step.name} 标准化酒店名`, 'info');

                    const result = await step.method(hotelName, options);

                    if (result.success && result.confidence >= 0.5) {
                        this.logger.log(`${step.name} 标准化成功`, 'success', {
                            confidence: result.confidence
                        });
                        return result;
                    }

                } catch (error) {
                    this.logger.log(`${step.name} 标准化失败`, 'warn', {
                        error: error.message
                    });
                    continue;
                }
            }

            // 所有方法都失败，返回原始输入
            return {
                success: false,
                originalName: hotelName,
                standardizedName: hotelName,
                source: 'no_standardization',
                confidence: 0.0,
                error: '所有标准化方法都失败'
            };
        }

        /**
         * 🚀 新增：使用 Gemini AI 标准化酒店名
         * @param {string} hotelName - 酒店名
         * @param {object} options - 选项
         * @returns {Promise<object>} 标准化结果
         * @SERVICE
         */
        async standardizeWithGemini(hotelName, options) {
            try {
                // 使用Gemini进行酒店名标准化
                const geminiResult = await this.processWithGemini(hotelName, {
                    ...options,
                    focusOnHotel: true
                });

                if (!geminiResult.success) {
                    return { success: false, error: 'Gemini AI 处理失败' };
                }

                const hotelInfo = geminiResult.data?.hotelInfo;
                if (!hotelInfo || !hotelInfo.detected) {
                    return { success: false, error: 'Gemini 未识别出酒店信息' };
                }

                return {
                    success: true,
                    originalName: hotelName,
                    standardizedName: hotelInfo.standardizedName,
                    source: 'gemini_ai',
                    confidence: hotelInfo.confidence || 0.8,
                    isOfficial: false, // Gemini 结果标记为非官方
                    additionalInfo: {
                        detectedRegion: geminiResult.data?.location?.region,
                        detectedCity: geminiResult.data?.location?.city,
                        geminiConfidence: geminiResult.data?.confidence
                    }
                };

            } catch (error) {
                return {
                    success: false,
                    error: `Gemini AI 错误: ${error.message}`
                };
            }
        }

        /**
         * 异步地址翻译（不阻塞主流程）
         * @param {string} address - 原始地址
         * @param {string} fieldName - 字段名称
         * @param {object} dataObject - 数据对象引用
         */
        async translateAddressAsync(address, fieldName, dataObject) {
            try {
                if (!address || typeof address !== 'string' || address.trim().length < 3) {
                    return; // 地址太短或无效，跳过翻译
                }

                // 检查是否包含中文字符，如果没有则跳过翻译
                if (!/[\u4e00-\u9fa5]/.test(address)) {
                    return; // 不包含中文，跳过翻译
                }

                const translationResult = await this.translateAddress(address);
                
                // 更新数据对象
                if (translationResult.translations.en) {
                    dataObject[`${fieldName}_en`] = translationResult.translations.en;
                }
                if (translationResult.translations.ms) {
                    dataObject[`${fieldName}_ms`] = translationResult.translations.ms;
                }

            } catch (error) {
                this.logger.log('异步地址翻译失败', 'error', {
                    address,
                    fieldName,
                    error: error.message
                });
            }
        }

        /**
         * 🚀 增强：使用 Gemini AI 处理地址 - 增强错误处理和日志记录
         * @param {string} address - 地址文本
         * @param {object} options - 选项
         * @returns {Promise<object>} Gemini 处理结果
         * @SERVICE
         */
        async processWithGemini(address, options = {}) {
            const startTime = Date.now();
            
            try {
                this.logger.log('🚀 开始Gemini地址处理', 'info', {
                    address: address?.substring(0, 50) + '...',
                    addressLength: address?.length,
                    options: options
                });

                // 验证输入
                if (!address || typeof address !== 'string' || address.trim().length === 0) {
                    throw new Error('无效的地址输入');
                }

                // 构建Gemini提示词
                const prompt = this.buildGeminiPrompt(address, options);
                this.logger.log('📝 Gemini提示词已构建', 'info', {
                    promptLength: prompt.length
                });

                // 调用Gemini API
                const geminiResponse = await this.callGeminiAPI(prompt, options);

                if (!geminiResponse.success) {
                    this.logger.log('❌ Gemini API调用失败', 'error', {
                        error: geminiResponse.error,
                        details: geminiResponse.details,
                        address: address?.substring(0, 30) + '...'
                    });

                    return {
                        success: false,
                        error: 'Gemini API 调用失败',
                        details: geminiResponse.error,
                        processingTime: Date.now() - startTime
                    };
                }

                this.logger.log('✅ Gemini API调用成功', 'success', {
                    hasData: !!geminiResponse.data,
                    dataType: typeof geminiResponse.data,
                    processingTimeMs: Date.now() - startTime
                });

                // 解析Gemini响应
                const parsedResult = this.parseGeminiResponse(geminiResponse.data, address);

                if (!parsedResult) {
                    throw new Error('Gemini响应解析失败 - 返回空结果');
                }

                // 候选复核：使用本地知识库对 Gemini 结果进行二次校验与可能的覆盖
                try {
                    const recon = this.reconcileWithLocalCandidates(parsedResult, options);
                    if (recon && recon.used) {
                        this.logger.log('🔧 本地候选复核完成', 'info', {
                            reconciliationType: recon.used,
                            hasName: !!recon.name,
                            confidence: recon.confidence
                        });

                        parsedResult.reconciliation = recon;
                        if (recon.used === 'local_override' || recon.used === 'local_confirm') {
                            parsedResult.hotelInfo = parsedResult.hotelInfo || {};
                            parsedResult.hotelInfo.standardizedName = recon.name || parsedResult.hotelInfo.standardizedName;
                            if (typeof recon.confidence === 'number') {
                                parsedResult.hotelInfo.confidence = recon.confidence;
                            }
                        }
                    }
                } catch (reconcileError) {
                    this.logger.log('⚠️ 候选复核阶段出现问题（已忽略）', 'warn', { 
                        error: reconcileError.message,
                        stack: reconcileError.stack 
                    });
                }

                const totalTime = Date.now() - startTime;
                this.logger.log('✅ Gemini地址处理完成', 'success', {
                    totalProcessingTime: totalTime,
                    hasHotelInfo: !!(parsedResult.hotelInfo?.detected),
                    standardizedAddress: parsedResult.standardizedAddress?.substring(0, 50) + '...'
                });

                return {
                    success: true,
                    data: parsedResult,
                    source: 'gemini_ai',
                    originalAddress: address,
                    processingTime: totalTime
                };

            } catch (error) {
                const totalTime = Date.now() - startTime;
                
                this.logger.log('❌ Gemini处理失败', 'error', {
                    address: address?.substring(0, 50) + '...',
                    error: error.message,
                    stack: error.stack,
                    processingTime: totalTime,
                    availableServices: {
                        geminiCaller: !!window.OTA?.geminiCaller,
                        geminiService: !!window.OTA?.geminiService
                    }
                });

                return {
                    success: false,
                    error: error.message,
                    source: 'gemini_ai',
                    originalAddress: address,
                    processingTime: totalTime,
                    debugInfo: {
                        stack: error.stack,
                        availableServices: {
                            geminiCaller: !!window.OTA?.geminiCaller,
                            geminiService: !!window.OTA?.geminiService
                        }
                    }
                };
            }
        }

        /**
         * 🚀 新增：构建Gemini提示词
         * @param {string} address - 地址文本
         * @param {object} options - 选项
         * @returns {string} 构建的提示词
         * @UTIL
         */
                buildGeminiPrompt(address, options) {
                    // 获取按渠道/地区动态上下文
                    const hotelContext = this.getHotelContextForPrompt(address, options);
                    const meta = this.getContextMeta(address, options);

                        const prompt = `你是一个专业的马来西亚地址标准化助手。请严格遵守以下规则：
1) 不得丢弃或简化关键限定词（例如：Floating Resort、KLIA1、KLIA2、Terminal 1、Terminal 2、Tower 等）。
2) 如果无法确定完整限定词，宁可保留原文片段，也不要简写为通用名称（如仅 “Dragon Inn”）。
3) 返回候选列表 candidates（每项包含 name 与 confidence，按置信度降序）。
4) 回传原始酒店名 rawHotelName（原文，不改写）。

当前渠道：${meta.channel || 'Unknown'}
目标地区：${[meta.region, meta.country].filter(Boolean).join(' / ') || 'Unknown'}

参考酒店数据库（优先匹配，已按地区精简）：
${hotelContext}

输入地址：${address}

仅返回以下 JSON：
{
    "standardizedAddress": "标准化后的英文地址",
    "hotelInfo": {
        "detected": true/false,
        "rawHotelName": "原始酒店名（原文，不改写）",
        "originalName": "原始酒店名（兼容字段，可与 rawHotelName 相同）",
        "standardizedName": "首选标准英文名（如确定）",
        "candidates": [
            { "name": "候选名1", "confidence": 0.0-1.0 },
            { "name": "候选名2", "confidence": 0.0-1.0 }
        ],
        "confidence": 0.0-1.0
    },
    "location": {
        "region": "地区名（如吉隆坡、槟城等）",
        "city": "城市名"
    },
    "confidence": 0.0-1.0
}`;

                        return prompt;
                }

        /**
         * 🚀 新增：获取酒店上下文信息用于提示词
         * @returns {string} 酒店上下文文本
         * @UTIL
         */
        getHotelContextForPrompt(address, options = {}) {
            let context = '';

            const filterByRegion = (list, region) => {
                if (!region) return list;
                const r = String(region).toLowerCase();
                return list.filter(h => String(h.region || '').toLowerCase().includes(r) || String(h.english || '').toLowerCase().includes(r));
            };

            // 从精简酒店数据获取上下文
            if (window.essentialHotelData?.loaded) {
                const highPriorityHotels = window.essentialHotelData.getHotelsByPriority('high') || [];
                const mediumPriorityHotels = window.essentialHotelData.getHotelsByPriority('medium') || [];

                const meta = this.getContextMeta(address, options);
                const regionFilteredHigh = filterByRegion(highPriorityHotels, meta.region || meta.country);
                const regionFilteredMedium = filterByRegion(mediumPriorityHotels, meta.region || meta.country);

                const highList = (regionFilteredHigh.length ? regionFilteredHigh : highPriorityHotels).slice(0, 20);
                const medList = (regionFilteredMedium.length ? regionFilteredMedium : mediumPriorityHotels).slice(0, 15);

                context += '高优先级酒店：\n';
                highList.forEach(hotel => {
                    context += `- ${hotel.chinese} → ${hotel.english} (${hotel.region})\n`;
                });

                if (medList.length) {
                    context += '\n中等优先级酒店：\n';
                    medList.forEach(hotel => {
                        context += `- ${hotel.chinese} → ${hotel.english} (${hotel.region})\n`;
                    });
                }
            }

            return context || '暂无参考酒店数据';
        }

        /**
         * 动态上下文元信息（当前渠道/可能地区）
         */
        getContextMeta(address, options = {}) {
            const meta = { channel: '', region: '', country: '' };

            try {
                // 渠道优先从 OTA 管理器/装饰器获取
                meta.channel = (window.OTA?.otaManager?.getCurrentChannelName?.() 
                    || window.OTA?.otaManagerDecorator?.getCurrentChannelName?.() 
                    || window.OTA?.adapters?.otaManagerDecorator?.getCurrentChannelName?.() 
                    || options.channel 
                    || '').trim();
            } catch (e) { /* ignore */ }

            // 地区/国家：优先 options 指定
            if (options.region) meta.region = String(options.region).trim();
            if (options.country) meta.country = String(options.country).trim();

            // 简单从地址中猜测（仅在未提供时）
            if (!meta.region && typeof address === 'string') {
                const guess = this.guessRegionFromAddress(address);
                if (guess.region) meta.region = guess.region;
                if (guess.country) meta.country = meta.country || guess.country;
            }

            return meta;
        }

        guessRegionFromAddress(address = '') {
            const text = String(address).toLowerCase();
            const knownCountries = ['malaysia','singapore','thailand','indonesia','japan','korea','china','taiwan','hong kong','macau','vietnam','philippines','australia','new zealand','united states','usa','uk','united kingdom'];
            for (const c of knownCountries) {
                if (text.includes(c)) return { country: c.toUpperCase() };
            }
            // 取逗号后的最后一个片段作为区域猜测（保守）
            const parts = text.split(',').map(s => s.trim()).filter(Boolean);
            const region = parts.length ? parts[parts.length - 1] : '';
            return { region };
        }

        // =============================
        // 候选复核（本地纠偏）
        // =============================
        getGenericTokens() {
            return new Set([
                'hotel','inn','resort','airport','international','terminal','tower','mall','station','city','center','downtown','residence','apartment','building',
                '酒店','客栈','旅馆','度假村','机场','国际','航站楼','车站','城市','中心','塔','公寓','大厦'
            ]);
        }

        getQualifierPhrases() {
            // 通用限定词，适用于国际场景（不局限于马来西亚）
            return ['floating resort','terminal 1','terminal 2','t1','t2','tower','residence','marina','harbour','harbor'];
        }

        normalizeNameTokens(s = '') {
            const t = String(s).toLowerCase().replace(/[()\-.,]/g, ' ').replace(/\s+/g, ' ').trim();
            const tokens = t.split(' ').filter(Boolean);
            const generic = this.getGenericTokens();
            return tokens.filter(tok => !generic.has(tok)).join(' ');
        }

        hasQualifierPhrase(s = '') {
            const t = String(s).toLowerCase();
            return this.getQualifierPhrases().some(q => t.includes(q));
        }

        scoreNameSimilarity(a = '', b = '') {
            const A = new Set(this.normalizeNameTokens(a).split(' ').filter(Boolean));
            const B = new Set(this.normalizeNameTokens(b).split(' ').filter(Boolean));
            if (A.size === 0 || B.size === 0) return 0;
            let inter = 0;
            A.forEach(x => { if (B.has(x)) inter++; });
            const jaccard = inter / (A.size + B.size - inter);
            const contain = (a && b && (a.toLowerCase().includes(b.toLowerCase()) || b.toLowerCase().includes(a.toLowerCase()))) ? 0.1 : 0;
            return Math.min(1, jaccard + contain);
        }

        reconcileWithLocalCandidates(parsedResult, options = {}) {
            if (!parsedResult || !parsedResult.hotelInfo) return null;

            const kb = window.OTA?.knowledgeBase;
            if (!kb || typeof kb.queryHotel !== 'function') return { used: 'skip_no_kb' };

            const hi = parsedResult.hotelInfo;
            const candidateNames = [];
            if (hi.standardizedName) candidateNames.push(hi.standardizedName);
            if (hi.rawHotelName) candidateNames.push(hi.rawHotelName);
            if (hi.originalName) candidateNames.push(hi.originalName);
            if (Array.isArray(hi.candidates)) hi.candidates.forEach(c => c?.name && candidateNames.push(c.name));

            // 去重
            const seen = new Set();
            const names = candidateNames.filter(n => {
                const k = String(n).toLowerCase();
                if (seen.has(k)) return false;
                seen.add(k);
                return true;
            });

            let best = null;
            for (const name of names) {
                try {
                    const info = kb.queryHotel(name);
                    if (info && info.english) {
                        // 评分：相似度 + 限定词奖励
                        const sim = this.scoreNameSimilarity(name, info.english);
                        const bonus = this.hasQualifierPhrase(info.english) && !this.hasQualifierPhrase(name) ? -0.15 : 0;
                        const score = Math.max(0, sim + bonus);
                        if (!best || score > best.score) {
                            best = { score, info, sourceName: name };
                        }
                    }
                } catch (e) {
                    // 忽略单项异常
                }
            }

            const gemC = Number(hi.confidence || 0);
            if (best && best.info) {
                // 若本地得分显著更高（≥ +0.1），使用本地覆盖
                if (best.score >= gemC + 0.1) {
                    return {
                        used: 'local_override',
                        name: best.info.english,
                        item: best.info,
                        confidence: Math.max(best.score, gemC)
                    };
                }
                // 否则若得分很高（≥0.9），视为确认
                if (best.score >= 0.9) {
                    return {
                        used: 'local_confirm',
                        name: best.info.english,
                        item: best.info,
                        confidence: Math.max(best.score, gemC)
                    };
                }
            }

            return { used: 'gemini', confidence: gemC };
        }

        /**
         * 🚀 修复：调用Gemini API - 与主要GeminiCaller保持一致
         * @param {string} prompt - 提示词
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         * @UTIL
         */
        async callGeminiAPI(prompt, options) {
            try {
                this.logger.log('🔍 开始Gemini API调用', 'info', {
                    promptLength: prompt.length,
                    options: options
                });

                // 使用与订单解析相同的GeminiCaller实例
                if (window.OTA?.geminiCaller) {
                    // 调用正确的方法名 callAPI，并使用 'text' 类型
                    const geminiResult = await window.OTA.geminiCaller.callAPI(prompt, 'text', {
                        temperature: 0.1, // 低温度确保一致性
                        isRealtime: false,
                        ...options
                    });

                    this.logger.log('🔍 Gemini API调用成功', 'success', {
                        hasResult: !!geminiResult
                    });

                    // 标准化返回格式
                    return {
                        success: true,
                        data: geminiResult,
                        source: 'gemini_caller_unified'
                    };
                }

                // 检查是否有其他Gemini服务实例
                if (window.OTA?.geminiService && window.OTA.geminiService.generateContent) {
                    const result = await window.OTA.geminiService.generateContent(prompt, {
                        temperature: 0.1,
                        maxTokens: 1000,
                        ...options
                    });

                    return {
                        success: true,
                        data: result,
                        source: 'gemini_service'
                    };
                }

                this.logger.log('❌ Gemini服务不可用', 'error', {
                    availableServices: {
                        geminiCaller: !!window.OTA?.geminiCaller,
                        geminiService: !!window.OTA?.geminiService
                    }
                });

                throw new Error('Gemini服务不可用 - 请检查GeminiCaller是否已正确初始化');

            } catch (error) {
                this.logger.log('❌ Gemini API调用失败', 'error', {
                    error: error.message,
                    stack: error.stack
                });

                return {
                    success: false,
                    error: error.message,
                    details: error.stack
                };
            }
        }

        /**
         * 🚀 修复：解析Gemini响应 - 处理GeminiCaller返回的标准格式
         * @param {object|string} response - Gemini响应数据（从GeminiCaller返回）
         * @param {string} originalAddress - 原始地址
         * @returns {object} 解析后的结果
         * @UTIL
         */
    parseGeminiResponse(response, originalAddress) {
            try {
                this.logger.log('🔍 开始解析Gemini响应', 'info', {
                    responseType: typeof response,
                    isObject: typeof response === 'object'
                });

                let jsonResponse;

                // 处理从GeminiCaller返回的标准格式
                if (typeof response === 'object' && response !== null) {
                    // GeminiCaller返回的是已解析的对象
                    if (response.candidates && Array.isArray(response.candidates) && response.candidates[0]) {
                        const textContent = response.candidates[0].content?.parts?.[0]?.text;
                        if (textContent) {
                            // 解析嵌套的JSON文本
                            const cleanText = textContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
                            jsonResponse = JSON.parse(cleanText);
                        } else {
                            throw new Error('GeminiCaller响应中没有找到text内容');
                        }
                    } else {
                        // 直接是JSON对象
                        jsonResponse = response;
                    }
                } else if (typeof response === 'string') {
                    // 响应是字符串，需要解析
                    let cleanResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();

                    try {
                        jsonResponse = JSON.parse(cleanResponse);
                    } catch (parseError) {
                        // 如果JSON解析失败，尝试提取JSON部分
                        const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
                        if (jsonMatch) {
                            jsonResponse = JSON.parse(jsonMatch[0]);
                        } else {
                            throw new Error('无法解析Gemini响应为JSON');
                        }
                    }
                } else {
                    throw new Error('无效的Gemini响应格式');
                }

                this.logger.log('🔍 Gemini响应解析成功', 'success', {
                    hasStandardizedAddress: !!jsonResponse.standardizedAddress,
                    hasHotelInfo: !!jsonResponse.hotelInfo,
                    hotelDetected: jsonResponse.hotelInfo?.detected
                });

                // 验证和标准化响应结构
                const candidates = Array.isArray(jsonResponse.hotelInfo?.candidates)
                    ? jsonResponse.hotelInfo.candidates
                        .filter(c => c && typeof c.name === 'string')
                        .map(c => ({ name: String(c.name), confidence: Math.max(0, Math.min(1, Number(c.confidence || 0))) }))
                    : [];

                const result = {
                    standardizedAddress: jsonResponse.standardizedAddress || originalAddress,
                    hotelInfo: {
                        detected: jsonResponse.hotelInfo?.detected || false,
                        rawHotelName: jsonResponse.hotelInfo?.rawHotelName || jsonResponse.hotelInfo?.originalName || '',
                        originalName: jsonResponse.hotelInfo?.originalName || jsonResponse.hotelInfo?.rawHotelName || '',
                        standardizedName: jsonResponse.hotelInfo?.standardizedName || '',
                        candidates,
                        confidence: Math.min(1.0, Math.max(0.0, jsonResponse.hotelInfo?.confidence || 0.0))
                    },
                    location: {
                        region: jsonResponse.location?.region || '',
                        city: jsonResponse.location?.city || ''
                    },
                    confidence: Math.min(1.0, Math.max(0.0, jsonResponse.confidence || 0.5))
                };

                return result;

            } catch (error) {
                this.logger.log('Gemini响应解析失败', 'warn', {
                    response,
                    error: error.message
                });

                // 返回降级结果
                return {
                    standardizedAddress: originalAddress,
                    hotelInfo: {
                        detected: false,
                        originalName: '',
                        standardizedName: '',
                        confidence: 0.0
                    },
                    location: {
                        region: '',
                        city: ''
                    },
                    confidence: 0.0
                };
            }
        }

        /**
         * 🚀 新增：使用本地数据标准化酒店名
         * @param {string} hotelName - 酒店名
         * @param {object} options - 选项
         * @returns {Promise<object>} 标准化结果
         * @SERVICE
         */
        async standardizeWithLocalData(hotelName, options) {
            try {
                // 1. 检查常用翻译映射
                const commonTranslation = this.commonTranslations.get(hotelName);
                if (commonTranslation && commonTranslation.en) {
                    return {
                        success: true,
                        originalName: hotelName,
                        standardizedName: commonTranslation.en,
                        source: 'local_common_data',
                        confidence: 0.80,
                        isOfficial: false
                    };
                }

                // 2. 查询知识库
                const knowledgeBase = window.OTA?.knowledgeBase;
                if (knowledgeBase) {
                    const hotelInfo = knowledgeBase.queryHotel(hotelName);
                    if (hotelInfo && hotelInfo.english) {
                        return {
                            success: true,
                            originalName: hotelName,
                            standardizedName: hotelInfo.english,
                            source: 'local_knowledge_base',
                            confidence: 0.75,
                            isOfficial: false,
                            region: hotelInfo.region,
                            matchType: hotelInfo.matchType
                        };
                    }
                }

                // 3. 基于规则的处理
                const ruleResult = this.ruleBasedTranslation(hotelName, 'en');
                if (ruleResult && ruleResult !== hotelName) {
                    return {
                        success: true,
                        originalName: hotelName,
                        standardizedName: ruleResult,
                        source: 'local_rules',
                        confidence: 0.50,
                        isOfficial: false
                    };
                }

                return { success: false, error: '本地数据无匹配结果' };

            } catch (error) {
                return {
                    success: false,
                    error: `本地数据处理错误: ${error.message}`
                };
            }
        }

        /**
         * 获取缓存翻译结果
         * @param {string} cacheKey - 缓存键
         * @returns {object|null} 缓存结果
         */
        getCachedTranslation(cacheKey) {
            const cached = this.translationCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
                return cached.result;
            }
            return null;
        }

        /**
         * 设置缓存翻译结果
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 翻译结果
         */
        setCachedTranslation(cacheKey, result) {
            this.translationCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });

            // 清理过期缓存
            this.cleanExpiredCache();
        }

        /**
         * 清理过期缓存
         */
        cleanExpiredCache() {
            const now = Date.now();
            for (const [key, cached] of this.translationCache.entries()) {
                if (now - cached.timestamp >= this.config.cacheTimeout) {
                    this.translationCache.delete(key);
                }
            }
        }

        /**
         * 🚀 新增：获取酒店名标准化缓存
         * @param {string} cacheKey - 缓存键
         * @returns {object|null} 缓存结果
         * @UTIL
         */
        getHotelStandardizationCache(cacheKey) {
            const cached = this.hotelStandardizationCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.hotelStandardization.cacheTimeout) {
                return cached.result;
            }
            return null;
        }

        /**
         * 🚀 新增：设置酒店名标准化缓存
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 标准化结果
         * @UTIL
         */
        setHotelStandardizationCache(cacheKey, result) {
            this.hotelStandardizationCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });

            // 清理过期的酒店标准化缓存
            this.cleanExpiredHotelCache();
        }

        /**
         * 🚀 新增：清理过期的酒店标准化缓存
         * @UTIL
         */
        cleanExpiredHotelCache() {
            const now = Date.now();
            const timeout = this.config.hotelStandardization.cacheTimeout;

            for (const [key, cached] of this.hotelStandardizationCache.entries()) {
                if (now - cached.timestamp >= timeout) {
                    this.hotelStandardizationCache.delete(key);
                }
            }
        }

        /**
         * 🚀 新增：流水线支持 - 处理地址中的酒店名标准化
         * @param {string} address - 包含酒店名的地址
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         * @PIPELINE
         */
        async processHotelNameStandardization(address, options = {}) {
            try {
                if (!address || typeof address !== 'string') {
                    return {
                        success: false,
                        error: '无效的地址输入',
                        originalAddress: address
                    };
                }

                // 检测地址中是否包含酒店名
                const hotelKeywords = ['酒店', '宾馆', '旅馆', '度假村', 'hotel', 'resort', 'inn'];
                const containsHotel = hotelKeywords.some(keyword =>
                    address.toLowerCase().includes(keyword.toLowerCase())
                );

                if (!containsHotel) {
                    return {
                        success: false,
                        error: '地址中未检测到酒店名',
                        originalAddress: address,
                        processedAddress: address
                    };
                }

                this.logger.log('检测到酒店地址，开始标准化处理', 'info', { address });

                // 提取可能的酒店名
                const extractedHotelName = this.extractHotelNameFromAddress(address);
                if (!extractedHotelName) {
                    return {
                        success: false,
                        error: '无法从地址中提取酒店名',
                        originalAddress: address,
                        processedAddress: address
                    };
                }

                // 标准化酒店名
                const standardizationResult = await this.standardizeHotelName(extractedHotelName, options);

                if (standardizationResult.success) {
                    // 替换地址中的酒店名
                    const processedAddress = address.replace(
                        extractedHotelName,
                        standardizationResult.standardizedName
                    );

                    return {
                        success: true,
                        originalAddress: address,
                        processedAddress: processedAddress,
                        hotelStandardization: standardizationResult
                    };
                } else {
                    return {
                        success: false,
                        error: '酒店名标准化失败',
                        originalAddress: address,
                        processedAddress: address,
                        hotelStandardization: standardizationResult
                    };
                }

            } catch (error) {
                this.logger.log('酒店名标准化处理失败', 'error', {
                    address,
                    error: error.message
                });

                return {
                    success: false,
                    error: error.message,
                    originalAddress: address,
                    processedAddress: address
                };
            }
        }

        /**
         * 🚀 新增：从地址中提取酒店名
         * @param {string} address - 地址
         * @returns {string|null} 提取的酒店名
         * @UTIL
         */
        extractHotelNameFromAddress(address) {
            // 简单的酒店名提取逻辑
            // 可以根据需要扩展更复杂的提取规则

            const hotelPatterns = [
                /(.+?)(酒店|宾馆|旅馆|度假村)/,
                /(.+?)(hotel|resort|inn)/i
            ];

            for (const pattern of hotelPatterns) {
                const match = address.match(pattern);
                if (match && match[1]) {
                    const hotelName = match[1].trim() + match[2];
                    if (hotelName.length >= 2) {
                        return hotelName;
                    }
                }
            }

            return null;
        }

        /**
         * 获取翻译统计信息
         * @returns {object} 统计信息
         */
        getTranslationStats() {
            return {
                cacheSize: this.translationCache.size,
                hotelStandardizationCacheSize: this.hotelStandardizationCache.size,
                commonTranslations: this.commonTranslations.size,
                supportedLanguages: this.config.supportedLanguages,
                hotelStandardizationEnabled: this.config.hotelStandardization.enabled,
                geminiServiceConnected: !!(window.OTA?.geminiService || window.OTA?.geminiCaller),
                version: '3.0.0' // 🚀 简化版本 - Gemini集成
            };
        }

        /**
         * 🚀 新增：获取酒店名标准化统计信息
         * @returns {object} 酒店标准化统计信息
         */
        getHotelStandardizationStats() {
            const stats = {
                cacheSize: this.hotelStandardizationCache.size,
                enabled: this.config.hotelStandardization.enabled,
                googleMapsServiceAvailable: !!this.googleMapsService,
                cacheTimeout: this.config.hotelStandardization.cacheTimeout,
                supportedSources: ['places_api', 'knowledge_graph', 'translation_api', 'local_data']
            };

            // 如果 Google Maps 服务可用，添加其统计信息
            if (this.googleMapsService) {
                try {
                    stats.googleMapsMetrics = this.googleMapsService.getMetrics();
                } catch (error) {
                    stats.googleMapsMetricsError = error.message;
                }
            }

            return stats;
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const addressTranslator = new AddressTranslator();

    // 导出到全局作用域
    window.AddressTranslator = AddressTranslator;
    window.OTA.AddressTranslator = AddressTranslator;
    window.OTA.addressTranslator = addressTranslator;

    // 向后兼容性别名 - 保持与旧接口的兼容性
    window.OTA.addressTranslationService = addressTranslator;

    console.log('✅ AddressTranslator (简化版 - Gemini集成) 已加载', {
        version: '3.0.0',
        architecture: 'Gemini + 本地数据库',
        features: ['Gemini AI处理', '本地数据标准化', '智能提示词']
    });

})();
