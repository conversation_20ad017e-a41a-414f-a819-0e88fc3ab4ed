<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无感知刷新测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .form-test {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .form-test input, .form-test textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        
        .config-panel {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .config-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-item label {
            min-width: 200px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 无感知刷新功能测试</h1>
            <p>测试新的智能版本更新系统</p>
        </div>

        <!-- 当前状态 -->
        <div class="test-section">
            <h3>📊 当前状态</h3>
            <div id="currentStatus" class="status info">
                正在检查系统状态...
            </div>
            <div class="log" id="statusLog"></div>
        </div>

        <!-- 配置面板 -->
        <div class="test-section">
            <h3>⚙️ 无感知更新配置</h3>
            <div class="config-panel">
                <div class="config-item">
                    <label>启用无感知更新:</label>
                    <input type="checkbox" id="enableSilentUpdate" checked>
                </div>
                <div class="config-item">
                    <label>页面隐藏时更新:</label>
                    <input type="checkbox" id="updateOnHidden" checked>
                </div>
                <div class="config-item">
                    <label>用户空闲时更新:</label>
                    <input type="checkbox" id="updateOnIdle" checked>
                </div>
                <div class="config-item">
                    <label>空闲阈值 (分钟):</label>
                    <input type="number" id="idleTimeout" value="5" min="1" max="60">
                </div>
                <div class="config-item">
                    <label>最大等待时间 (分钟):</label>
                    <input type="number" id="maxWaitTime" value="30" min="5" max="120">
                </div>
                <div class="config-item">
                    <label>保护表单数据:</label>
                    <input type="checkbox" id="preserveFormData" checked>
                </div>
                <div class="config-item">
                    <label>显示最小化通知:</label>
                    <input type="checkbox" id="showNotification" checked>
                </div>
                <button onclick="applyConfig()">应用配置</button>
            </div>
        </div>

        <!-- 表单数据测试 -->
        <div class="test-section">
            <h3>📝 表单数据保护测试</h3>
            <div class="form-test">
                <p>填写以下表单来测试数据保护功能：</p>
                <input type="text" id="testInput1" placeholder="测试输入1">
                <input type="text" id="testInput2" placeholder="测试输入2">
                <textarea id="testTextarea" placeholder="测试文本区域" rows="3"></textarea>
                <button onclick="checkFormData()">检查表单数据</button>
                <button onclick="clearFormData()">清空表单</button>
            </div>
        </div>

        <!-- 测试控制 -->
        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <button onclick="simulateVersionUpdate()">模拟版本更新</button>
            <button onclick="simulatePageHidden()">模拟页面隐藏</button>
            <button onclick="simulateUserIdle()">模拟用户空闲</button>
            <button onclick="checkUpdateStatus()">检查更新状态</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <!-- 实时日志 -->
        <div class="test-section">
            <h3>📋 实时日志</h3>
            <div class="log" id="testLog"></div>
        </div>
    </div>

    <script>
        // 日志记录
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const statusElement = document.getElementById('statusLog');
            
            const logEntry = `[${timestamp}] ${message}`;
            
            logElement.innerHTML += logEntry + '\n';
            statusElement.innerHTML += logEntry + '\n';
            
            logElement.scrollTop = logElement.scrollHeight;
            statusElement.scrollTop = statusElement.scrollHeight;
            
            console.log(`[SilentUpdateTest] ${message}`);
        }

        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('currentStatus');
            
            // 检查 Runtime Version Watcher
            if (typeof window.BUILD_INFO !== 'undefined') {
                log(`✅ BUILD_INFO 已加载: v${window.BUILD_INFO.version}`);
                statusDiv.className = 'status success';
                statusDiv.textContent = `系统正常 - 版本: ${window.BUILD_INFO.version}`;
            } else {
                log('❌ BUILD_INFO 未找到');
                statusDiv.className = 'status error';
                statusDiv.textContent = '系统异常 - BUILD_INFO 未找到';
            }
            
            // 检查 localStorage 中的版本信息
            const lastHash = localStorage.getItem('ota_last_build_hash');
            if (lastHash) {
                log(`📦 上次构建哈希: ${lastHash.substring(0, 16)}...`);
            }
            
            // 检查用户活动状态
            log('🔍 开始监控用户活动状态...');
        }

        // 应用配置
        function applyConfig() {
            const config = {
                enabled: document.getElementById('enableSilentUpdate').checked,
                updateOnHidden: document.getElementById('updateOnHidden').checked,
                updateOnIdle: document.getElementById('updateOnIdle').checked,
                idleTimeout: parseInt(document.getElementById('idleTimeout').value) * 60000,
                maxWaitTime: parseInt(document.getElementById('maxWaitTime').value) * 60000,
                preserveFormData: document.getElementById('preserveFormData').checked,
                showNotification: document.getElementById('showNotification').checked
            };
            
            // 保存配置到 localStorage
            localStorage.setItem('silent_update_config', JSON.stringify(config));
            log('⚙️ 配置已保存: ' + JSON.stringify(config, null, 2));
        }

        // 检查表单数据
        function checkFormData() {
            const input1 = document.getElementById('testInput1').value;
            const input2 = document.getElementById('testInput2').value;
            const textarea = document.getElementById('testTextarea').value;
            
            const hasData = input1 || input2 || textarea;
            
            if (hasData) {
                log('📝 检测到表单数据，无感知更新将被延迟');
            } else {
                log('📝 无表单数据，可以立即执行无感知更新');
            }
            
            return hasData;
        }

        // 清空表单数据
        function clearFormData() {
            document.getElementById('testInput1').value = '';
            document.getElementById('testInput2').value = '';
            document.getElementById('testTextarea').value = '';
            log('🗑️ 表单数据已清空');
        }

        // 模拟版本更新
        function simulateVersionUpdate() {
            log('🔄 模拟版本更新...');
            
            // 修改 localStorage 中的哈希值来触发更新检测
            const currentHash = localStorage.getItem('ota_last_build_hash');
            const newHash = 'simulated_' + Date.now().toString(36);
            
            localStorage.setItem('ota_last_build_hash_backup', currentHash);
            localStorage.removeItem('ota_last_build_hash');
            
            log(`📦 模拟新版本哈希: ${newHash}`);
            log('⏳ 等待版本检查器检测到变化...');
        }

        // 模拟页面隐藏
        function simulatePageHidden() {
            log('👁️ 模拟页面隐藏事件...');
            
            // 触发 visibilitychange 事件
            Object.defineProperty(document, 'hidden', {
                value: true,
                writable: true
            });
            
            const event = new Event('visibilitychange');
            document.dispatchEvent(event);
            
            log('📱 页面隐藏事件已触发');
            
            // 3秒后恢复
            setTimeout(() => {
                Object.defineProperty(document, 'hidden', {
                    value: false,
                    writable: true
                });
                document.dispatchEvent(new Event('visibilitychange'));
                log('👁️ 页面重新可见');
            }, 3000);
        }

        // 模拟用户空闲
        function simulateUserIdle() {
            log('😴 模拟用户空闲状态...');
            log('⏰ 在实际环境中，用户需要停止操作5分钟才会被认为是空闲状态');
        }

        // 检查更新状态
        function checkUpdateStatus() {
            const lastHash = localStorage.getItem('ota_last_build_hash');
            const backupHash = localStorage.getItem('ota_last_build_hash_backup');
            
            log('📊 当前更新状态:');
            log(`  - 当前哈希: ${lastHash || '未设置'}`);
            log(`  - 备份哈希: ${backupHash || '无备份'}`);
            
            if (window.BUILD_INFO) {
                log(`  - 构建版本: ${window.BUILD_INFO.version}`);
                log(`  - 构建哈希: ${window.BUILD_INFO.buildHash.substring(0, 16)}...`);
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('testLog').innerHTML = '';
            document.getElementById('statusLog').innerHTML = '';
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            log('🚀 无感知刷新测试页面已加载');
            checkSystemStatus();
            
            // 加载保存的配置
            const savedConfig = localStorage.getItem('silent_update_config');
            if (savedConfig) {
                try {
                    const config = JSON.parse(savedConfig);
                    document.getElementById('enableSilentUpdate').checked = config.enabled;
                    document.getElementById('updateOnHidden').checked = config.updateOnHidden;
                    document.getElementById('updateOnIdle').checked = config.updateOnIdle;
                    document.getElementById('idleTimeout').value = config.idleTimeout / 60000;
                    document.getElementById('maxWaitTime').value = config.maxWaitTime / 60000;
                    document.getElementById('preserveFormData').checked = config.preserveFormData;
                    document.getElementById('showNotification').checked = config.showNotification;
                    log('⚙️ 已加载保存的配置');
                } catch (e) {
                    log('⚠️ 配置加载失败: ' + e.message);
                }
            }
        });

        // 监听版本更新事件
        window.addEventListener('storage', (e) => {
            if (e.key === 'ota_last_build_hash') {
                log('🔄 检测到版本哈希变化');
                log(`  - 旧值: ${e.oldValue || '无'}`);
                log(`  - 新值: ${e.newValue || '无'}`);
            }
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                log('👁️ 页面已隐藏');
            } else {
                log('👁️ 页面重新可见');
            }
        });
    </script>
</body>
</html>
