/**
 * ============================================================================
 * 🚀 核心业务流程 - Gemini服务适配器 (兼容性保证)
 * ============================================================================
 *
 * @fileoverview Gemini服务适配器 - 兼容性保证
 * @description 保持向后兼容性，将旧的API调用适配到新的母子两层架构
 * 
 * @businessFlow 向后兼容性适配
 * 在核心业务流程中的作用：
 * 旧代码调用 → 【当前文件职责】API适配和转换
 *     ↓
 * 调用新的母子两层架构 → 返回兼容格式的结果
 *
 * @architecture Adapter Layer (适配器层)
 * - 职责：新旧架构之间的桥梁
 * - 原则：保持完全的向后兼容性
 * - 接口：提供与原gemini-service.js相同的API
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - 现有代码的geminiService调用
 * 下游依赖：
 * - controllers/business-flow-controller.js (新架构)
 * - flow/order-parser.js (订单解析)
 * - flow/knowledge-base.js (知识库)
 * - flow/simple-address-processor.js (地址翻译)
 *
 * @localProcessing 本地处理职责
 * - 🟢 API调用适配和转换
 * - 🟢 数据格式兼容性处理
 * - 🟢 错误处理和降级方案
 * - 🟢 性能监控和统计
 *
 * @remoteProcessing 远程处理职责
 * - 🔴 通过新架构调用Gemini API
 * - 🔴 委托给子层进行实际处理
 *
 * @compatibility 兼容性保证（核心功能）
 * - ✅ 保持window.OTA.geminiService接口
 * - ✅ 保持parseOrder()方法签名和返回格式
 * - ✅ 保持analyzeImage()方法签名和返回格式
 * - ✅ 保持detectAndSplitMultiOrdersWithVerification()方法
 * - ✅ 保持所有现有的公共方法
 *
 * @refactoringConstraints 重构约束
 * - 🔒 严禁修改现有API接口
 * - 🔒 必须保持100%向后兼容
 * - 🔒 不能破坏现有的调用方式
 * - 🔒 保持相同的错误处理机制
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * Gemini服务适配器 - 向后兼容性保证
     */
    class GeminiServiceAdapter {
        constructor() {
            this.logger = this.getLogger();
            // 控制台输出开关（默认开启，便于观察Gemini返回值）
            this.consoleLogEnabled = true;
            // 完整输出（pretty JSON），默认开启以“完整显示”
            this.consoleVerbose = true;

            // 获取新架构的组件
            this.businessFlowController = null;
            this.orderParser = null;
            this.knowledgeBase = null;
            this.addressTranslator = null;

            // 添加状态管理
            this.state = {
                isAnalyzing: false,
                lastAnalysisTime: null,
                analysisCount: 0,
                errorCount: 0
            };

            // 延迟初始化，避免循环依赖
            this.initializeComponents();

            // 初始化调试命名空间
            window.OTA = window.OTA || {};
            window.OTA.debug = window.OTA.debug || {};
            window.OTA.debug.geminiLogs = window.OTA.debug.geminiLogs || [];
            window.OTA.debug.getGeminiLogs = () => window.OTA.debug.geminiLogs;
            window.OTA.debug.clearGeminiLogs = () => { window.OTA.debug.geminiLogs.length = 0; };

            this.logger.log('Gemini服务适配器已初始化', 'info');
        }

        /**
         * 统一标准化（有全局层则用全局层；否则使用本地极简映射作为兜底）
         * 保证：始终返回包含API标准键的对象；未知值设为null；不依赖拦截器安装时序。
         */
        standardizeWithFallback(input, context = 'gemini-adapter') {
            try {
                const layer = window.OTA?.globalFieldStandardizationLayer;
                if (layer && typeof layer.standardizeToApiFields === 'function') {
                    return layer.standardizeToApiFields(input, context);
                }
            } catch (_) { /* 忽略全局层异常，走本地兜底 */ }

            // 本地极简映射（关键字段足够支撑表单/接口；尽量保持精简）
            const MAP = {
                // 客户
                customerName: 'customer_name', customer_name: 'customer_name',
                customerContact: 'customer_contact', customer_contact: 'customer_contact',
                customerEmail: 'customer_email', customer_email: 'customer_email',
                // 参考号/航班
                otaReferenceNumber: 'ota_reference_number', ota_reference_number: 'ota_reference_number',
                flightInfo: 'flight_info', flight_info: 'flight_info', flight_number: 'flight_info',
                // 时间
                pickupDate: 'date', pickup_date: 'date', date: 'date',
                pickupTime: 'time', pickup_time: 'time', time: 'time',
                // 地点
                pickup: 'pickup', pickupLocation: 'pickup', pickup_location: 'pickup',
                dropoff: 'destination', dropoffLocation: 'destination', dropoff_location: 'destination', destination: 'destination',
                // 车型/子类/区域
                carTypeId: 'car_type_id', car_type_id: 'car_type_id',
                subCategoryId: 'sub_category_id', sub_category_id: 'sub_category_id',
                drivingRegionId: 'driving_region_id', driving_region_id: 'driving_region_id', drivingRegion: 'driving_region_id',
                // 人数/行李/语言
                passengerCount: 'passenger_number', passenger_number: 'passenger_number',
                luggageCount: 'luggage_number', luggage_number: 'luggage_number',
                languagesIdArray: 'languages_id_array', languages_id_array: 'languages_id_array',
                // 需求/选项
                extraRequirement: 'extra_requirement', extra_requirement: 'extra_requirement',
                babyChair: 'baby_chair', baby_chair: 'baby_chair',
                tourGuide: 'tour_guide', tour_guide: 'tour_guide',
                meetAndGreet: 'meet_and_greet', meet_and_greet: 'meet_and_greet',
                // 价格/货币
                otaPrice: 'ota_price', ota_price: 'ota_price', price: 'ota_price', amount: 'ota_price',
                currency: 'currency',
                // 系统
                rawText: 'raw_text', raw_text: 'raw_text', ota: 'ota'
            };
            const SKELETON = {
                customer_name: null,
                customer_contact: null,
                customer_email: null,
                ota_reference_number: null,
                flight_info: null,
                date: null,
                time: null,
                pickup: null,
                destination: null,
                car_type_id: null,
                sub_category_id: null,
                driving_region_id: null,
                passenger_number: null,
                luggage_number: null,
                languages_id_array: null,
                extra_requirement: null,
                baby_chair: null,
                tour_guide: null,
                meet_and_greet: null,
                ota_price: null,
                currency: null,
                incharge_by_backend_user_id: null,
                ota: null,
                raw_text: null
            };
            if (!input || typeof input !== 'object') return { ...SKELETON };
            const out = { ...SKELETON };
            for (const [key, val] of Object.entries(input)) {
                const target = MAP[key] || null;
                if (target) {
                    out[target] = val === undefined ? null : val;
                }
            }
            return out;
        }

        /**
         * 标准化数组/对象/带orders容器的通用函数
         */
        standardizeResultUniversal(res, context = 'gemini-adapter') {
            if (!res) return res;
            if (Array.isArray(res)) return res.map(o => this.standardizeWithFallback(o, context));
            if (typeof res === 'object') {
                if (Array.isArray(res.orders)) {
                    const clone = { ...res };
                    clone.orders = res.orders.map(o => this.standardizeWithFallback(o, context));
                    return clone;
                }
                return this.standardizeWithFallback(res, context);
            }
            return res;
        }

        /**
         * 设置是否在控制台输出Gemini返回数据
         * @param {boolean} enabled 
         */
        setConsoleLogEnabled(enabled) {
            this.consoleLogEnabled = !!enabled;
        }

        /**
         * 控制台打印帮助
         */
        logToConsole(title, payload, extra = undefined) {
            if (!this.consoleLogEnabled) return;
            try {
                // 使用分组便于折叠查看
                const useGroup = typeof console.group === 'function';
                if (useGroup) console.group(title);
                if (extra !== undefined) console.log('meta:', extra);
                // 原始对象（可交互展开）
                console.log('data (object):', payload);
                // 完整JSON字符串（便于复制/完整查看）
                if (this.consoleVerbose) {
                    const json = this.safeStringify(payload, 2);
                    if (json) console.log('data (json):\n' + json);
                }
                if (useGroup) console.groupEnd();
            } catch (_) { /* 忽略打印异常 */ }
        }

        /**
         * 安全序列化（处理循环引用/BigInt/函数）
         */
        safeStringify(obj, space = 2) {
            const seen = new WeakSet();
            const replacer = (_key, value) => {
                if (typeof value === 'bigint') return value.toString();
                if (typeof value === 'function') return undefined;
                if (typeof value === 'symbol') return value.toString();
                if (typeof value === 'object' && value !== null) {
                    if (seen.has(value)) return '[Circular]';
                    seen.add(value);
                }
                return value;
            };
            try { return JSON.stringify(obj, replacer, space); } catch { return null; }
        }

        /**
         * 设置是否打印完整JSON
         */
        setVerboseConsole(enabled) {
            this.consoleVerbose = !!enabled;
        }

        /**
         * 存储调试日志到 window.OTA.debug.geminiLogs
         */
        debugStore(type, payload, meta = undefined) {
            try {
                const entry = {
                    ts: new Date().toISOString(),
                    type,
                    meta: meta || null,
                    data: payload
                };
                window.OTA?.debug?.geminiLogs?.push(entry);
                // 同时挂载最近一次结果
                window.OTA.debug.geminiLast = entry;
            } catch (_) { /* 忽略存储异常 */ }
        }

        /**
         * 初始化组件
         */
        initializeComponents() {
            try {
                // 延迟获取组件，确保它们已经加载
                setTimeout(() => {
                    this.businessFlowController = window.OTA?.businessFlowController;
                    this.orderParser = window.OTA?.orderParser;
                    this.knowledgeBase = window.OTA?.knowledgeBase;
                    this.addressTranslator = window.OTA?.addressTranslator;
                    
                    if (this.businessFlowController) {
                        this.logger.log('新架构组件连接成功', 'success');
                    } else {
                        this.logger.log('新架构组件未找到，使用降级方案', 'warning');
                    }
                }, 100);
            } catch (error) {
                this.logger.log('组件初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 解析订单文本 - 兼容原API
         * 🚀 架构修复：支持接收渠道检测结果
         * @param {string} text - 订单文本
         * @param {boolean|object} isRealtimeOrOptions - 是否实时解析或选项对象
         * @returns {Promise<object|null>} 解析结果
         */
        async parseOrder(text, isRealtimeOrOptions = false) {
            try {
                // 🚀 架构修复：支持新的参数格式
                let isRealtime = false;
                let options = {};

                if (typeof isRealtimeOrOptions === 'boolean') {
                    isRealtime = isRealtimeOrOptions;
                } else if (typeof isRealtimeOrOptions === 'object' && isRealtimeOrOptions !== null) {
                    isRealtime = isRealtimeOrOptions.isRealtime || false;
                    options = isRealtimeOrOptions;
                }

                this.logger.log('适配器：解析订单文本', 'info', {
                    textLength: text?.length || 0,
                    isRealtime,
                    hasChannelInfo: !!options.channelDetectionResult
                });

                if (!text || text.length < (isRealtime ? 5 : 10)) {
                    return null;
                }

                // 更新状态
                this.state.isAnalyzing = true;
                this.state.lastAnalysisTime = Date.now();
                this.state.analysisCount++;

                // 🚀 架构修复：使用新架构，传递渠道检测结果
                if (this.businessFlowController) {
                    const processOptions = {
                        isRealtime,
                        ...options  // 包含channelDetectionResult等信息
                    };
                    const result = await this.businessFlowController.processInput(text, 'text', processOptions);

                    console.log('[GeminiServiceAdapter] 🔧 业务流控制器原始结果:', result);

                    // 🔧 修复：实时分析模式返回数组格式，非实时模式返回单个对象
                    let finalResult = null;
                    if (isRealtime) {
                        // 实时分析模式：返回数组格式
                        if (result.type === 'single-order') {
                            finalResult = this.standardizeResultUniversal([result.order], 'gemini-adapter-output');
                        } else if (result.type === 'multi-order') {
                            finalResult = this.standardizeResultUniversal(result.orders || [], 'gemini-adapter-output');
                        } else {
                            finalResult = []; // 空数组
                        }
                    } else {
                        // 非实时模式：返回单个对象（保持兼容性）
                        if (result.type === 'single-order') {
                            finalResult = this.standardizeResultUniversal(result.order, 'gemini-adapter-output');
                        } else if (result.type === 'multi-order' && result.orders.length > 0) {
                            finalResult = this.standardizeResultUniversal(result.orders[0], 'gemini-adapter-output');
                        }
                    }

                    // 控制台显示Gemini（新架构聚合）返回的数据 + 缓存
                    this.logToConsole('🤖 Gemini parseOrder 返回', finalResult, { isRealtime });
                    this.debugStore('parseOrder', finalResult, { isRealtime });

                    // 重置分析状态
                    this.state.isAnalyzing = false;
                    return finalResult;
                } else {
                    // 降级方案：返回基础解析结果
                    this.logger.log('使用降级方案解析订单', 'warning');
                    const result = this.fallbackParseOrder(text);
                    this.state.isAnalyzing = false;

                    // 🔧 修复：根据模式返回正确格式（并标准化）
                    if (isRealtime) {
                        const out = result ? this.standardizeResultUniversal([result], 'gemini-adapter-fallback') : [];
                        this.logToConsole('🤖 Gemini parseOrder (fallback) 返回', out, { isRealtime });
                        this.debugStore('parseOrder_fallback', out, { isRealtime });
                        return out; // 实时模式返回数组
                    } else {
                        const out = result ? this.standardizeResultUniversal(result, 'gemini-adapter-fallback') : result;
                        this.logToConsole('🤖 Gemini parseOrder (fallback) 返回', out, { isRealtime });
                        this.debugStore('parseOrder_fallback', out, { isRealtime });
                        return out; // 非实时模式返回单个对象
                    }
                }

            } catch (error) {
                this.state.isAnalyzing = false;
                this.state.errorCount++;
                this.logger.log('订单解析适配失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 分析图片 - 兼容原API
         * @param {string} base64Image - Base64图片数据
         * @returns {Promise<object>} 分析结果
         */
        async analyzeImage(base64Image) {
            try {
                this.logger.log('适配器：分析图片', 'info');

                if (!base64Image || typeof base64Image !== 'string') {
                    throw new Error('无效的图片数据');
                }

                // 使用新架构
                if (this.businessFlowController) {
                    const result = await this.businessFlowController.processInput(base64Image, 'image');
                    
                    // 适配返回格式
                    let final = null;
                    if (result.type === 'single-order') {
                        final = this.standardizeResultUniversal(result.order, 'gemini-analyzeImage');
                    } else if (result.type === 'multi-order' && result.orders.length > 0) {
                        final = this.standardizeResultUniversal(result.orders[0], 'gemini-analyzeImage'); // 返回第一个订单保持兼容性
                    }
                    this.logToConsole('🖼️ Gemini analyzeImage 返回', final);
                    this.debugStore('analyzeImage', final);
                    return final;
                } else {
                    // 降级方案
                    this.logger.log('使用降级方案分析图片', 'warning');
                    throw new Error('图片分析功能需要新架构支持');
                }

            } catch (error) {
                this.logger.log('图片分析适配失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 检测和分割多订单 - 兼容原API
         * @param {string} text - 订单文本
         * @param {object} options - 选项
         * @returns {Promise<object>} 检测结果
         */
        async detectAndSplitMultiOrdersWithVerification(text, options = {}) {
            try {
                this.logger.log('适配器：检测和分割多订单', 'info', { 
                    textLength: text?.length || 0 
                });

                // 使用新架构
                if (this.businessFlowController) {
                    const result = await this.businessFlowController.processInput(text, 'text', { 
                        ...options, 
                        detectMultiOrder: true 
                    });
                    
                    // 适配返回格式
                    const adapted = {
                        isMultiOrder: result.type === 'multi-order',
                        orders: this.standardizeResultUniversal(result.orders || [result.order], 'gemini-detect-multi'),
                        orderCount: result.orders ? result.orders.length : 1,
                        confidence: result.confidence || 0.8
                    };
                    this.logToConsole('📦 Gemini detectAndSplitMultiOrders 返回', adapted, { textLength: text?.length || 0 });
                    this.debugStore('detectAndSplitMultiOrders', adapted, { textLength: text?.length || 0 });
                    return adapted;
                } else {
                    // 本地多订单检测逻辑已移除：严格依赖Gemini结果
                    const out = {
                        isMultiOrder: false,
                        orders: [],
                        orderCount: 0,
                        confidence: 0,
                        note: 'Local multi-order detection removed. Requires new architecture to use Gemini.'
                    };
                    this.logger.log('多订单检测需要新架构支持（本地检测已移除）', 'warning');
                    this.logToConsole('📦 Gemini detectAndSplitMultiOrders (no-legacy)', out, { textLength: text?.length || 0 });
                    this.debugStore('detectAndSplitMultiOrders_no_legacy', out, { textLength: text?.length || 0 });
                    return out;
                }

            } catch (error) {
                this.logger.log('多订单检测适配失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 翻译地址 - 兼容原API
         * @param {string} address - 地址
         * @param {array} targetLanguages - 目标语言
         * @returns {Promise<object>} 翻译结果
         */
        async translateAddress(address, targetLanguages = ['ms', 'en']) {
            try {
                if (this.addressTranslator) {
                    return await this.addressTranslator.translateAddress(address, targetLanguages);
                } else {
                    // 降级方案
                    return { original: address, translations: {} };
                }
            } catch (error) {
                this.logger.log('地址翻译适配失败', 'error', { error: error.message });
                return { original: address, translations: {}, error: error.message };
            }
        }

        // ========================================
        // 降级方案
        // ========================================

        /**
         * 降级方案：基础订单解析
         * @param {string} text - 订单文本
         * @returns {object|null} 基础解析结果
         */
        fallbackParseOrder(text) {
            // 非常基础的解析逻辑
            const lines = text.split('\n').filter(line => line.trim());
            const order = {
                customer_name: null,
                pickup_location: null,
                dropoff_location: null,
                pickup_time: null,
                ota_price: null,
                confidence: 0.5,
                source: 'fallback'
            };

            // 简单的关键词匹配
            for (const line of lines) {
                if (line.includes('姓名') || line.includes('客户')) {
                    order.customer_name = line.split(/[：:]/)[1]?.trim();
                }
                if (line.includes('接机') || line.includes('出发')) {
                    order.pickup_location = line.split(/[：:]/)[1]?.trim();
                }
                if (line.includes('目的地') || line.includes('到达')) {
                    order.dropoff_location = line.split(/[：:]/)[1]?.trim();
                }
                if (line.includes('时间')) {
                    order.pickup_time = line.split(/[：:]/)[1]?.trim();
                }
                if (line.includes('价格') || line.includes('金额')) {
                    const priceMatch = line.match(/\d+(\.\d+)?/);
                    if (priceMatch) {
                        order.ota_price = parseFloat(priceMatch[0]);
                    }
                }
            }

            return order.customer_name ? order : null;
        }

    // 注意：本地多订单检测逻辑已删除，必须通过新架构委托Gemini完成

        /**
         * 获取服务状态 - 兼容原API
         * @returns {object} 服务状态
         */
        getServiceStatus() {
            return {
                version: '2.0.0-adapter',
                architecture: 'mother-child',
                components: {
                    businessFlowController: !!this.businessFlowController,
                    orderParser: !!this.orderParser,
                    knowledgeBase: !!this.knowledgeBase,
                    addressTranslator: !!this.addressTranslator
                },
                compatibility: 'full-backward-compatible'
            };
        }

        /**
         * 检查服务是否可用 - 兼容原API
         * @returns {boolean} 服务是否可用
         */
        isAvailable() {
            try {
                // 检查核心组件是否可用
                const hasBusinessFlowController = !!this.businessFlowController;
                const hasOrderParser = !!this.orderParser;

                // 至少需要有一个核心组件可用
                return hasBusinessFlowController || hasOrderParser;
            } catch (error) {
                this.logger.log('检查服务可用性失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 获取服务状态 - 兼容原API (getStatus别名)
         * @returns {object} 服务状态
         */
        getStatus() {
            return {
                isAvailable: this.isAvailable(),
                isAnalyzing: this.state.isAnalyzing || false,
                lastAnalysisTime: this.state.lastAnalysisTime || null,
                version: '2.0.0-adapter',
                components: {
                    businessFlowController: !!this.businessFlowController,
                    orderParser: !!this.orderParser,
                    knowledgeBase: !!this.knowledgeBase,
                    addressTranslator: !!this.addressTranslator
                }
            };
        }

        /**
         * 检查实时分析是否启用 - 兼容原API
         * @returns {boolean} 实时分析是否启用
         */
        isRealtimeEnabled() {
            try {
                // 检查实时分析相关组件是否可用
                return this.isAvailable() && !this.state.isAnalyzing;
            } catch (error) {
                this.logger.log('检查实时分析状态失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建适配器实例
    const geminiServiceAdapter = new GeminiServiceAdapter();

    // 导出到全局作用域，替换原有的geminiService
    window.GeminiServiceAdapter = GeminiServiceAdapter;
    window.OTA.GeminiServiceAdapter = GeminiServiceAdapter;
    
    // 重要：保持向后兼容性
    window.OTA.geminiService = geminiServiceAdapter;
    window.geminiService = geminiServiceAdapter;

    // 提供获取服务的函数
    window.getGeminiService = function() {
        return geminiServiceAdapter;
    };

    console.log('✅ GeminiServiceAdapter (兼容性适配器) 已加载');

})();
