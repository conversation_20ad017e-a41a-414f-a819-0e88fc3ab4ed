/**
 * 多订单页面响应式样式
 * 文件: js/pages/multi-order/styles/responsive.css
 * 角色: 多订单页面的响应式设计和移动端适配
 * 
 * @MULTI_ORDER_RESPONSIVE_STYLES 多订单响应式样式
 * 🏷️ 标签: @OTA_MULTI_ORDER_RESPONSIVE_STYLES
 * 📝 说明: 适配不同屏幕尺寸的样式定义
 * <AUTHOR>
 * @version 2.0.0
 */

/* ================================
   桌面端优化 (1200px+)
   ================================ */

@media (min-width: 1200px) {
    .multi-order-content {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .progress-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-lg);
    }
    
    .summary-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-lg);
    }
    
    .order-card {
        padding: var(--spacing-lg);
    }
    
    .batch-controls {
        padding: var(--spacing-lg);
    }
}

/* ================================
   平板端适配 (768px - 1199px)
   ================================ */

@media (min-width: 768px) and (max-width: 1199px) {
    .multi-order-content {
        padding: var(--spacing-md);
    }
    
    .progress-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .order-card-header {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .batch-controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .batch-controls-left,
    .batch-controls-right {
        width: 100%;
        justify-content: center;
    }
}

/* ================================
   移动端适配 (最大 767px)
   ================================ */

@media (max-width: 767px) {
    /* 页面容器调整 */
    .multi-order-panel {
        padding: var(--spacing-sm);
        margin: 0;
    }
    
    .multi-order-content {
        padding: var(--spacing-sm);
        border-radius: 8px;
    }
    
    .multi-order-header {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .multi-order-header h3 {
        font-size: 1.2rem;
        margin: 0;
    }
    
    .header-controls {
        width: 100%;
        justify-content: center;
        gap: var(--spacing-sm);
    }
    
    /* 订单卡片移动端优化 */
    .order-card {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        border-radius: 8px;
    }
    
    .order-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
        padding-bottom: var(--spacing-xs);
    }
    
    .order-selector {
        width: 100%;
        justify-content: space-between;
    }
    
    .order-title {
        flex: 1;
    }
    
    .order-number {
        font-size: 0.95rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 2px 8px;
    }
    
    /* 订单摘要移动端优化 */
    .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
        padding: var(--spacing-xs) 0;
    }
    
    .summary-label {
        font-size: 0.8rem;
        min-width: auto;
        color: var(--text-secondary);
    }
    
    .summary-value {
        font-size: 0.85rem;
        text-align: left;
        font-weight: 500;
    }
    
    /* 批量控制移动端优化 */
    .batch-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .batch-controls-left,
    .batch-controls-right {
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .selected-count {
        text-align: center;
        width: 100%;
        padding: var(--spacing-sm);
        font-size: 0.85rem;
    }
    
    .batch-btn {
        width: 100%;
        padding: var(--spacing-sm);
        font-size: 0.9rem;
        text-align: center;
    }
    
    /* 进度展示移动端优化 */
    .progress-section {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm) 0;
    }
    
    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .progress-header h4 {
        font-size: 1rem;
    }
    
    .progress-summary {
        font-size: 0.8rem;
    }
    
    .progress-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        padding: var(--spacing-sm);
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .progress-bar {
        height: 20px;
    }
    
    .progress-text {
        font-size: 0.8rem;
        min-width: 50px;
    }
    
    /* 订单状态列表移动端优化 */
    .order-status-list {
        max-height: 250px;
    }
    
    .order-status-item {
        padding: var(--spacing-sm);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .order-status-icon {
        align-self: flex-start;
    }
    
    .order-status-content {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .order-status-title {
        font-size: 0.9rem;
    }
    
    .order-status-message {
        font-size: 0.75rem;
    }
    
    /* 结果展示移动端优化 */
    .results-section {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm) 0;
    }
    
    .results-header h4 {
        font-size: 1rem;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
    
    .summary-stat {
        padding: var(--spacing-sm);
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .summary-message {
        font-size: 0.9rem;
        padding: var(--spacing-sm);
    }
    
    .success-orders h5,
    .failed-orders h5 {
        font-size: 0.9rem;
    }
    
    .result-item {
        padding: var(--spacing-sm);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .result-item-info {
        width: 100%;
    }
    
    .result-item-title {
        font-size: 0.9rem;
    }
    
    .result-item-details {
        font-size: 0.75rem;
    }
    
    .result-item-status {
        align-self: flex-end;
        font-size: 0.75rem;
    }
    
    .results-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .results-actions .btn {
        width: 100%;
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}

/* ================================
   小屏幕移动端 (最大 480px)
   ================================ */

@media (max-width: 480px) {
    .multi-order-panel {
        padding: var(--spacing-xs);
    }
    
    .multi-order-content {
        padding: var(--spacing-xs);
    }
    
    .multi-order-header {
        padding: var(--spacing-xs);
    }
    
    .multi-order-header h3 {
        font-size: 1.1rem;
    }
    
    .order-card {
        padding: var(--spacing-xs);
    }
    
    .batch-controls {
        padding: var(--spacing-xs);
    }
    
    .progress-section,
    .results-section {
        padding: var(--spacing-xs);
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }
    
    .progress-stats {
        gap: var(--spacing-xs);
    }
    
    .stat-item {
        padding: var(--spacing-xs);
    }
    
    .stat-number {
        font-size: 1.3rem;
    }
    
    .order-status-list {
        max-height: 200px;
    }
    
    .success-list,
    .failed-list {
        max-height: 150px;
    }
}

/* ================================
   横屏模式优化
   ================================ */

@media (max-width: 767px) and (orientation: landscape) {
    .progress-stats {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .summary-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .order-status-list {
        max-height: 150px;
    }
    
    .success-list,
    .failed-list {
        max-height: 120px;
    }
}

/* ================================
   触摸设备优化
   ================================ */

@media (hover: none) and (pointer: coarse) {
    .order-card {
        padding: var(--spacing-md);
    }
    
    .order-checkbox {
        width: 20px;
        height: 20px;
    }
    
    .batch-btn {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .btn {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .order-card:hover {
        transform: none;
    }
    
    .order-card:active {
        transform: scale(0.98);
    }
}
