<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试"Data need to be refined"错误修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 "Data need to be refined" 错误修复测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证字段映射修复是否能解决"Data need to be refined"API错误</p>
            <ul>
                <li>✅ 修复字段名映射：统一使用 incharge_by_backend_user_id</li>
                <li>✅ 统一languages_id_array处理，默认使用对象格式</li>
                <li>✅ 移除冲突的字段转换逻辑</li>
                <li>✅ 添加详细的调试日志</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试用例</h3>
            <button class="button" onclick="testFieldMapping()">测试字段映射</button>
            <button class="button" onclick="testProblemOrder()">测试问题订单数据</button>
            <button class="button" onclick="testLanguageArray()">测试语言数组处理</button>
            <button class="button danger" onclick="clearLogs()">清空日志</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="logOutput" class="log-output"></div>
        </div>
    </div>

    <script>
        // 模拟日志记录
        function log(message, level = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function clearLogs() {
            document.getElementById('logOutput').textContent = '';
            document.getElementById('status').style.display = 'none';
        }

        // 测试字段映射
        function testFieldMapping() {
            log('🔧 开始测试字段映射修复...');
            
            const testData = {
                customer_name: '郑钰萍',
                customer_contact: '18606827678',
                pickup: 'Singapore Changi Airport Terminal 3',
                destination: 'The EDITION Singapore',
                date: '2025-08-19',
                time: '16:55',
                incharge_by_backend_user_id: 310, // 统一使用snake_case格式
                sub_category_id: 2,
                car_type_id: 32,
                driving_region_id: 5,
                ota: 'Fliggy',
                ota_price: 63,
                ota_reference_number: '2879865228867709189',
                passenger_number: 1
            };

            log('原始数据: ' + JSON.stringify(testData, null, 2));

            // 模拟unified-field-mapper处理
            if (window.OTA && window.OTA.unifiedFieldMapper) {
                try {
                    const processedData = window.OTA.unifiedFieldMapper.processData(testData);
                    log('✅ 字段映射处理完成');
                    log('处理后数据: ' + JSON.stringify(processedData, null, 2));
                    
                    // 检查关键字段
                    const hasCorrectField = processedData.incharge_by_backend_user_id && typeof processedData.incharge_by_backend_user_id === 'number';
                    const hasCorrectLanguageFormat = processedData.languages_id_array && 
                        typeof processedData.languages_id_array === 'object' &&
                        !Array.isArray(processedData.languages_id_array);
                    
                    if (hasCorrectField && hasCorrectLanguageFormat) {
                        showStatus('✅ 字段映射测试通过！', 'success');
                        log('✅ 验证通过：incharge_by_backend_user_id = ' + processedData.incharge_by_backend_user_id);
                        log('✅ 验证通过：languages_id_array格式正确 = ' + JSON.stringify(processedData.languages_id_array));
                    } else {
                        showStatus('❌ 字段映射测试失败', 'error');
                        log('❌ 验证失败：字段映射不正确');
                    }
                } catch (error) {
                    log('❌ 处理过程中出错: ' + error.message);
                    showStatus('❌ 处理出错: ' + error.message, 'error');
                }
            } else {
                log('❌ UnifiedFieldMapper未加载');
                showStatus('❌ UnifiedFieldMapper未加载', 'error');
            }
        }

        // 测试问题订单数据
        function testProblemOrder() {
            log('🎯 开始测试问题订单数据处理...');
            
            const problemData = {
                customer_name: '郑钰萍',
                customer_contact: '18606827678',
                pickup: 'Singapore Changi Airport Terminal 3',
                destination: 'The EDITION Singapore',
                date: '2025-08-19',
                time: '16:55',
                flight_info: 'MU6049',
                incharge_by_backend_user_id: 310,
                sub_category_id: '2',
                car_type_id: '32',
                driving_region_id: '5',
                ota: 'Fliggy',
                ota_price: '63',
                ota_reference_number: '2879865228867709189',
                passenger_number: '1',
                currency: 'MYR'
            };

            log('问题订单数据: ' + JSON.stringify(problemData, null, 2));

            if (window.OTA && window.OTA.unifiedFieldMapper) {
                try {
                    const processedData = window.OTA.unifiedFieldMapper.processData(problemData);
                    log('✅ 问题订单处理完成');
                    log('处理结果: ' + JSON.stringify(processedData, null, 2));
                    
                    // 验证关键修复点
                    const keyTests = [
                        {
                            name: '字段名映射修复',
                            test: processedData.incharge_by_backend_user_id === 310,
                            message: `incharge_by_backend_user_id = ${processedData.incharge_by_backend_user_id}`
                        },
                        {
                            name: 'languages_id_array格式',
                            test: processedData.languages_id_array && 
                                  typeof processedData.languages_id_array === 'object' &&
                                  !Array.isArray(processedData.languages_id_array),
                            message: `languages_id_array = ${JSON.stringify(processedData.languages_id_array)}`
                        }
                    ];
                    
                    let allTestsPassed = true;
                    keyTests.forEach(test => {
                        if (test.test) {
                            log(`✅ ${test.name}: ${test.message}`);
                        } else {
                            log(`❌ ${test.name}: ${test.message}`);
                            allTestsPassed = false;
                        }
                    });
                    
                    // 验证必填字段
                    const requiredFields = [
                        'pickup', 'destination', 'date', 'time',
                        'customer_name', 'ota_reference_number', 'ota_price',
                        'sub_category_id', 'car_type_id', 'driving_region_id',
                        'incharge_by_backend_user_id'
                    ];
                    
                    const missingFields = requiredFields.filter(field => 
                        !processedData.hasOwnProperty(field) || 
                        processedData[field] === null || 
                        processedData[field] === undefined || 
                        processedData[field] === ''
                    );
                    
                    if (missingFields.length === 0 && allTestsPassed) {
                        showStatus('✅ 问题订单修复成功！所有关键问题已解决', 'success');
                        log('✅ 所有测试通过，"Data need to be refined"错误应已修复');
                    } else {
                        const issues = [];
                        if (missingFields.length > 0) {
                            issues.push(`缺失字段: ${missingFields.join(', ')}`);
                        }
                        if (!allTestsPassed) {
                            issues.push('关键修复点测试失败');
                        }
                        showStatus(`⚠️ ${issues.join('; ')}`, 'warning');
                        log('⚠️ 部分测试失败: ' + issues.join('; '));
                    }
                } catch (error) {
                    log('❌ 问题订单处理出错: ' + error.message);
                    showStatus('❌ 处理出错: ' + error.message, 'error');
                }
            } else {
                log('❌ UnifiedFieldMapper未加载');
                showStatus('❌ UnifiedFieldMapper未加载', 'error');
            }
        }

        // 测试语言数组处理
        function testLanguageArray() {
            log('🌐 开始测试语言数组处理...');
            
            const testCases = [
                { name: '默认英文', data: {} },
                { name: '中文客户', data: { customer_name: '张三' } },
                { name: '数组格式', data: { languages_id_array: [2, 4] } },
                { name: '对象格式', data: { languages_id_array: {"0": "4"} } }
            ];

            testCases.forEach((testCase, index) => {
                log(`测试用例 ${index + 1}: ${testCase.name}`);
                
                if (window.OTA && window.OTA.unifiedFieldMapper) {
                    try {
                        const result = window.OTA.unifiedFieldMapper.processData(testCase.data);
                        log(`结果: languages_id_array = ${JSON.stringify(result.languages_id_array)}`);
                        
                        const isCorrectFormat = result.languages_id_array && 
                            typeof result.languages_id_array === 'object' &&
                            !Array.isArray(result.languages_id_array);
                        
                        if (isCorrectFormat) {
                            log('✅ 格式正确');
                        } else {
                            log('❌ 格式错误');
                        }
                    } catch (error) {
                        log('❌ 测试出错: ' + error.message);
                    }
                } else {
                    log('❌ UnifiedFieldMapper未加载');
                }
                log('---');
            });
            
            showStatus('✅ 语言数组测试完成', 'success');
        }

        // 页面加载完成后的检查
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始检查依赖...');
            
            if (window.OTA && window.OTA.unifiedFieldMapper) {
                log('✅ UnifiedFieldMapper已加载');
                showStatus('✅ 测试环境就绪', 'success');
            } else {
                log('❌ UnifiedFieldMapper未加载，请确保相关脚本已加载');
                showStatus('❌ 依赖未加载，请检查页面配置', 'error');
            }
        });
    </script>

    <!-- 加载依赖脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/services/unified-field-mapper.js"></script>
</body>
</html>