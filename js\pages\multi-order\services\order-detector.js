/**
 * 订单检测服务
 * 文件: js/pages/multi-order/services/order-detector.js
 * 角色: 新的独立订单检测服务，迁移自multi-order-detector.js
 * 
 * @ORDER_DETECTOR 订单检测服务
 * 🏷️ 标签: @OTA_ORDER_DETECTOR_V2
 * 📝 说明: 负责AI检测和文本分析逻辑，识别多订单内容
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Services = window.OTA.Services || {};

(function() {
    'use strict';

    /**
     * 订单检测服务类
     * 提供AI检测和传统检测功能
     */
    class OrderDetector {
        constructor(config = {}) {
            this.config = {
                minInputLength: config.minInputLength || 50,
                confidenceThreshold: config.confidenceThreshold || 0.7,
                debounceDelay: config.debounceDelay || 1200,
                maxRetries: config.maxRetries || 3,
                timeout: config.timeout || 30000,
                ...config
            };
            
            this.logger = this.getLogger();
            this.logger.log('🔍 订单检测服务已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 统一多订单检测入口
         * @param {string} text - 输入文本
         * @param {Object} options - 检测选项
         * @returns {Promise<Object>} 检测结果
         */
        async detectMultiOrder(text, options = {}) {
            const { forceDetection = false, source = 'auto' } = options;

            // 基本验证
            if (!forceDetection && (!text || text.trim().length < this.config.minInputLength)) {
                return {
                    isMultiOrder: false,
                    orderCount: 0,
                    confidence: 0,
                    reason: '文本长度不足',
                    orders: [],
                    analysis: null
                };
            }

            this.logger.log(`🔍 开始多订单检测，文本长度: ${text.length}字符`, 'info');

            try {
                // 优先使用AI检测
                const aiResult = await this.detectMultiOrderAI(text);
                
                if (aiResult.isMultiOrder && aiResult.confidence > this.config.confidenceThreshold) {
                    this.logger.log(`✅ AI检测确认多订单: ${aiResult.orderCount}个订单`, 'success');
                    return aiResult;
                }

                // 按AI结果判定为单订单
                this.logger.log(`📋 按AI结果判定为单订单`, 'info');
                return {
                    isMultiOrder: false,
                    orderCount: aiResult.orders?.length || 1,
                    confidence: aiResult.confidence,
                    reason: `仅AI检测结果可用，未确认多订单`,
                    orders: aiResult.orders || [],
                    analysis: aiResult.analysis
                };

            } catch (error) {
                this.logger.logError('多订单检测失败', error);
                return {
                    isMultiOrder: false,
                    orderCount: 0,
                    confidence: 0,
                    reason: `检测失败: ${error.message}`,
                    orders: [],
                    analysis: null
                };
            }
        }

        /**
         * AI多订单检测（调用Gemini服务）
         * @param {string} text - 输入文本
         * @returns {Promise<Object>} AI检测结果
         */
        async detectMultiOrderAI(text) {
            try {
                const geminiService = this.getGeminiService();
                if (!geminiService) {
                    throw new Error('Gemini AI服务不可用');
                }

                // 获取Gemini配置
                const geminiConfig = this.getGeminiConfig();
                
                // 调用Gemini服务进行检测和解析
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, geminiConfig);
                
                // 确保返回的订单数据格式正确
                if (result.orders && Array.isArray(result.orders)) {
                    result.orders = result.orders.map(order => this.normalizeOrderFields(order));
                }

                return {
                    isMultiOrder: result.isMultiOrder || false,
                    orderCount: result.orderCount || (result.orders ? result.orders.length : 1),
                    confidence: result.confidence || 0,
                    reason: result.analysis || 'Gemini AI分析',
                    orders: result.orders || [],
                    analysis: result.analysis,
                    rawResult: result
                };

            } catch (error) {
                this.logger.logError('AI多订单检测失败', error);
                return {
                    isMultiOrder: false,
                    orderCount: 1,
                    confidence: 0,
                    reason: `AI检测失败: ${error.message}`,
                    orders: [],
                    analysis: null
                };
            }
        }

        /**
         * 智能分割订单文本
         * @param {string} text - 输入文本
         * @returns {Promise<Array>} 分割后的订单对象数组
         */
        async smartSplitOrderText(text) {
            if (!text || typeof text !== 'string') {
                return [text || ''];
            }

            try {
                const geminiService = this.getGeminiService();
                if (!geminiService) {
                    throw new Error('Gemini AI服务不可用');
                }
                
                // 使用Gemini AI进行智能分割
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(text, this.getGeminiConfig());
                
                // 标准化订单字段
                const normalizedOrders = (result.orders || []).map(order => this.normalizeOrderFields(order));
                
                this.logger.log(`📋 智能分割完成: ${normalizedOrders.length} 个订单`, 'success');
                
                return normalizedOrders.length > 0 ? normalizedOrders : [text];
                
            } catch (error) {
                this.logger.logError('智能分割失败，返回原文本', error);
                return [text];
            }
        }

        /**
         * 标准化订单字段
         * @param {Object} order - 原始订单对象
         * @returns {Object} 标准化后的订单对象
         */
        normalizeOrderFields(order) {
            if (!order || typeof order !== 'object') {
                return order;
            }

            // 获取字段映射配置
            const fieldMapping = this.getFieldMapping();
            const normalizedOrder = {};

            // 应用字段映射
            Object.entries(fieldMapping).forEach(([frontendField, aiFields]) => {
                if (Array.isArray(aiFields)) {
                    // 尝试多个可能的AI字段
                    for (const aiField of aiFields) {
                        if (order[aiField] !== undefined && order[aiField] !== null && order[aiField] !== '') {
                            normalizedOrder[frontendField] = order[aiField];
                            break;
                        }
                    }
                } else if (order[aiFields] !== undefined) {
                    normalizedOrder[frontendField] = order[aiFields];
                }
            });

            // 保留原始字段作为备份
            normalizedOrder._original = order;

            return normalizedOrder;
        }

        /**
         * 获取字段映射配置
         * @returns {Object} 字段映射配置
         */
        getFieldMapping() {
            // 从原有的FieldMappingConfig获取映射，或使用默认映射
            if (window.FieldMappingConfig?.AI_TO_FRONTEND) {
                return window.FieldMappingConfig.AI_TO_FRONTEND;
            }

            // 默认字段映射
            return {
                customer_name: ['customer_name', 'customerName', 'name', 'passenger_name'],
                customer_phone: ['customer_phone', 'customerPhone', 'phone', 'contact_phone'],
                pickup_location: ['pickup_location', 'pickupLocation', 'from', 'departure'],
                dropoff_location: ['dropoff_location', 'dropoffLocation', 'to', 'destination'],
                pickup_time: ['pickup_time', 'pickupTime', 'time', 'departure_time'],
                total_price: ['total_price', 'totalPrice', 'price', 'cost', 'amount'],
                notes: ['notes', 'remarks', 'comment', 'description']
            };
        }

        /**
         * 获取Gemini服务实例
         * @returns {Object} Gemini服务实例
         */
        getGeminiService() {
            return window.getGeminiService?.() || window.OTA?.geminiService || null;
        }

        /**
         * 获取Gemini配置
         * @returns {Object} Gemini配置对象
         */
        getGeminiConfig() {
            return {
                confidenceThreshold: this.config.confidenceThreshold,
                maxRetries: this.config.maxRetries,
                timeout: this.config.timeout
            };
        }

        /**
         * 验证检测结果
         * @param {Object} result - 检测结果
         * @returns {boolean} 是否有效
         */
        validateDetectionResult(result) {
            if (!result || typeof result !== 'object') {
                return false;
            }

            const requiredFields = ['isMultiOrder', 'orderCount', 'confidence', 'orders'];
            return requiredFields.every(field => result.hasOwnProperty(field));
        }

        /**
         * 获取检测统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                config: this.config,
                lastDetectionTime: this.lastDetectionTime || null,
                totalDetections: this.totalDetections || 0,
                successfulDetections: this.successfulDetections || 0
            };
        }

        /**
         * 重置配置
         * @param {Object} newConfig - 新配置
         */
        updateConfig(newConfig) {
            this.config = {
                ...this.config,
                ...newConfig
            };
            this.logger.log('🔧 检测服务配置已更新', 'info');
        }

        /**
         * 销毁服务
         */
        destroy() {
            // 清理资源
            this.config = null;
            this.logger.log('🗑️ 订单检测服务已销毁', 'info');
        }
    }

    // 创建全局订单检测服务实例
    const orderDetector = new OrderDetector();

    // 暴露到OTA命名空间
    window.OTA.Services.OrderDetector = OrderDetector;
    window.OTA.Services.orderDetector = orderDetector;

    // 向后兼容
    window.orderDetector = orderDetector;

    console.log('✅ 订单检测服务已加载');

})();
