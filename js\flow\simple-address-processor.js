/**
 * ============================================================================
 * 🚀 简化地址处理器 - 保留核心功能和注释
 * ============================================================================
 *
 * @fileoverview 地址处理器 - 极简化版本，保留核心翻译功能
 * @description 使用Gemini AI和静态映射表进行地址翻译和标准化处理
 *
 * @businessFlow 地址翻译和标准化 (简化版)
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * 订单数据解析 → 【当前文件职责】地址翻译和标准化 - 静态映射 + Gemini
 *     ↓
 * 标准化订单数据 → 订单管理
 *
 * @architecture 简化架构 - 静态映射 + Gemini AI
 * - 职责：使用静态映射表和Gemini AI进行地址翻译
 * - 原则：性能优先，成本控制，简化维护
 * - 策略：常用地址静态映射，复杂地址Gemini处理
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - flow/gemini-caller.js (Gemini API调用)
 * - flow/knowledge-base.js (数据查询，可选)
 * 下游依赖：无（底层实现）
 *
 * @coreFeatures 核心功能
 * - 🟢 静态映射表快速翻译（机场、地标、常用词汇）
 * - 🟢 Gemini AI智能翻译（复杂地址）
 * - 🟢 基本缓存机制（减少重复API调用）
 * - 🟢 多语言支持（中文→英文/马来文）
 * - 🟢 错误处理和降级策略
 *
 * @performance 性能策略
 * - 静态映射表优先：常用地址0延迟响应
 * - Gemini缓存：相同地址避免重复API调用
 * - 失败降级：API失败时返回原地址
 *
 * <AUTHOR>
 * @version 3.0.0 (简化版)
 * @since 2025-08-13
 * @lastModified 2025-08-13
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 简化地址处理器
     * 整合地址翻译和流水线处理功能
     */
    class SimpleAddressProcessor {
        constructor() {
            this.logger = this.getLogger();

            // 基本配置
            this.config = {
                supportedLanguages: ['en', 'ms'],
                defaultTargetLanguages: ['en', 'ms'],
                cacheTimeout: 30 * 60 * 1000, // 30分钟缓存
                minAddressLength: 2,
                maxAddressLength: 200
            };

            // 处理缓存
            this.cache = new Map();

            // 超高频词汇静态映射表（只保留最常用的，用于快速响应）
            this.highFrequencyMappings = new Map([
                // 机场代码 - 绝对不能出错的高频词汇
                ['KLIA', { en: 'KLIA', ms: 'KLIA' }],
                ['KLIA2', { en: 'KLIA2', ms: 'KLIA2' }],
                ['KLCC', { en: 'KLCC', ms: 'KLCC' }]
            ]);

            // 完整地址词汇映射表（用于Gemini提示词上下文）
            this.fullTranslationMappings = new Map([
                // ==================== 马来西亚 - 机场相关 ====================
                ['吉隆坡国际机场', { en: 'Kuala Lumpur International Airport', ms: 'Lapangan Terbang Antarabangsa Kuala Lumpur' }],
                ['吉隆坡第二国际机场', { en: 'KLIA2', ms: 'KLIA2' }],
                ['梳邦机场', { en: 'Sultan Abdul Aziz Shah Airport', ms: 'Lapangan Terbang Sultan Abdul Aziz Shah' }],
                ['槟城国际机场', { en: 'Penang International Airport', ms: 'Lapangan Terbang Antarabangsa Pulau Pinang' }],
                ['新山士乃国际机场', { en: 'Senai International Airport', ms: 'Lapangan Terbang Antarabangsa Senai' }],
                ['兰卡威国际机场', { en: 'Langkawi International Airport', ms: 'Lapangan Terbang Antarabangsa Langkawi' }],
                ['哥打京那巴鲁国际机场', { en: 'Kota Kinabalu International Airport', ms: 'Lapangan Terbang Antarabangsa Kota Kinabalu' }],
                ['古晋国际机场', { en: 'Kuching International Airport', ms: 'Lapangan Terbang Antarabangsa Kuching' }],

                // ==================== 新加坡 - 机场相关 ====================
                ['樟宜机场', { en: 'Singapore Changi Airport', ms: 'Lapangan Terbang Changi Singapura' }],
                ['新加坡樟宜机场', { en: 'Singapore Changi Airport', ms: 'Lapangan Terbang Changi Singapura' }],
                ['实里达机场', { en: 'Seletar Airport', ms: 'Lapangan Terbang Seletar' }],

                // ==================== 马来西亚 - 主要地标 ====================
                ['双子塔', { en: 'Petronas Twin Towers', ms: 'Menara Berkembar Petronas' }],
                ['吉隆坡塔', { en: 'KL Tower', ms: 'Menara Kuala Lumpur' }],
                ['独立广场', { en: 'Merdeka Square', ms: 'Dataran Merdeka' }],
                ['茨厂街', { en: 'Chinatown', ms: 'Pekan Cina' }],
                ['小印度', { en: 'Little India', ms: 'India Kecil' }],
                ['中央艺术坊', { en: 'Central Market', ms: 'Pasar Seni' }],
                ['武吉免登', { en: 'Bukit Bintang', ms: 'Bukit Bintang' }],
                ['阿罗街', { en: 'Jalan Alor', ms: 'Jalan Alor' }],
                ['升旗山', { en: 'Penang Hill', ms: 'Bukit Bendera' }],
                ['极乐寺', { en: 'Kek Lok Si Temple', ms: 'Tokong Kek Lok Si' }],
                ['乔治市', { en: 'George Town', ms: 'George Town' }],

                // ==================== 新加坡 - 主要地标 ====================
                ['鱼尾狮公园', { en: 'Merlion Park', ms: 'Taman Merlion' }],
                ['滨海湾金沙', { en: 'Marina Bay Sands', ms: 'Marina Bay Sands' }],
                ['圣淘沙', { en: 'Sentosa', ms: 'Sentosa' }],
                ['环球影城', { en: 'Universal Studios Singapore', ms: 'Universal Studios Singapore' }],
                ['牛车水', { en: 'Chinatown', ms: 'Chinatown' }],
                ['小印度', { en: 'Little India', ms: 'Little India' }],
                ['甘榜格南', { en: 'Kampong Glam', ms: 'Kampong Glam' }],
                ['乌节路', { en: 'Orchard Road', ms: 'Orchard Road' }],
                ['克拉码头', { en: 'Clarke Quay', ms: 'Clarke Quay' }],
                ['驳船码头', { en: 'Boat Quay', ms: 'Boat Quay' }],
                ['滨海湾花园', { en: 'Gardens by the Bay', ms: 'Gardens by the Bay' }],

                // ==================== 马来西亚 - 交通枢纽 ====================
                ['中央车站', { en: 'KL Sentral', ms: 'KL Sentral' }],
                ['富都车站', { en: 'Pudu Sentral', ms: 'Pudu Sentral' }],
                ['蒲种站', { en: 'Pudu Sentral', ms: 'Pudu Sentral' }],
                ['湖滨公园轻快铁站', { en: 'Tasik Selatan Station', ms: 'Stesen Tasik Selatan' }],
                ['吉隆坡国际机场快铁', { en: 'KLIA Express', ms: 'KLIA Express' }],

                // ==================== 新加坡 - 交通枢纽 ====================
                ['丹那美拉码头', { en: 'Tanah Merah Ferry Terminal', ms: 'Terminal Feri Tanah Merah' }],
                ['港湾地铁站', { en: 'HarbourFront MRT Station', ms: 'Stesen MRT HarbourFront' }],
                ['樟宜机场地铁站', { en: 'Changi Airport MRT Station', ms: 'Stesen MRT Lapangan Terbang Changi' }],

                // ==================== 酒店住宿词汇 ====================
                ['酒店', { en: 'Hotel', ms: 'Hotel' }],
                ['度假村', { en: 'Resort', ms: 'Resort' }],
                ['公寓', { en: 'Apartment', ms: 'Apartmen' }],
                ['高级公寓', { en: 'Luxury Apartment', ms: 'Apartmen Mewah' }],
                ['服务式公寓', { en: 'Serviced Apartment', ms: 'Apartmen Berkhidmat' }],
                ['精品酒店', { en: 'Boutique Hotel', ms: 'Hotel Butik' }],
                ['商务酒店', { en: 'Business Hotel', ms: 'Hotel Perniagaan' }],
                ['青年旅社', { en: 'Hostel', ms: 'Hostel' }],
                ['民宿', { en: 'Homestay', ms: 'Homestay' }],
                ['别墅', { en: 'Villa', ms: 'Vila' }],

                // ==================== 酒店名称映射将从数据库加载 ====================
                // 酒店数据将在 loadHotelMappings() 方法中从 hotels_by_region.json 动态加载

                // ==================== 设施词汇 ====================
                ['泳池', { en: 'Swimming Pool', ms: 'Kolam Renang' }],
                ['无边泳池', { en: 'Infinity Pool', ms: 'Kolam Renang Infinity' }],
                ['健身房', { en: 'Gym', ms: 'Gim' }],
                ['水疗中心', { en: 'Spa', ms: 'Spa' }],
                ['购物中心', { en: 'Shopping Mall', ms: 'Pusat Membeli-belah' }],
                ['会议室', { en: 'Meeting Room', ms: 'Bilik Mesyuarat' }],

                // ==================== 方位词汇 ====================
                ['附近', { en: 'near', ms: 'berhampiran' }],
                ['旁边', { en: 'beside', ms: 'di sebelah' }],
                ['对面', { en: 'opposite', ms: 'bertentangan' }],
                ['近', { en: 'near', ms: 'dekat' }],
                ['楼上', { en: 'upstairs', ms: 'tingkat atas' }],
                ['楼下', { en: 'downstairs', ms: 'tingkat bawah' }],

                // ==================== 马来西亚 - 主要城市和区域 ====================
                ['吉隆坡', { en: 'Kuala Lumpur', ms: 'Kuala Lumpur' }],
                ['雪兰莪', { en: 'Selangor', ms: 'Selangor' }],
                ['八打灵再也', { en: 'Petaling Jaya', ms: 'Petaling Jaya' }],
                ['莎亚南', { en: 'Shah Alam', ms: 'Shah Alam' }],
                ['安邦', { en: 'Ampang', ms: 'Ampang' }],
                ['蕉赖', { en: 'Cheras', ms: 'Cheras' }],
                ['旺沙玛珠', { en: 'Wangsa Maju', ms: 'Wangsa Maju' }],
                ['万挠', { en: 'Rawang', ms: 'Rawang' }],
                ['加影', { en: 'Kajang', ms: 'Kajang' }],
                ['蒲种', { en: 'Puchong', ms: 'Puchong' }],
                ['新山', { en: 'Johor Bahru', ms: 'Johor Bahru' }],
                ['槟城', { en: 'Penang', ms: 'Pulau Pinang' }],
                ['马六甲', { en: 'Malacca', ms: 'Melaka' }],
                ['怡保', { en: 'Ipoh', ms: 'Ipoh' }],
                ['关丹', { en: 'Kuantan', ms: 'Kuantan' }],
                ['兰卡威', { en: 'Langkawi', ms: 'Langkawi' }],
                ['哥打京那巴鲁', { en: 'Kota Kinabalu', ms: 'Kota Kinabalu' }],
                ['古晋', { en: 'Kuching', ms: 'Kuching' }],

                // ==================== 新加坡 - 主要区域 ====================
                ['新加坡', { en: 'Singapore', ms: 'Singapura' }],
                ['市中心', { en: 'City Center', ms: 'Pusat Bandar' }],
                ['滨海湾', { en: 'Marina Bay', ms: 'Marina Bay' }],
                ['莱佛士坊', { en: 'Raffles Place', ms: 'Raffles Place' }],
                ['政府大厦', { en: 'City Hall', ms: 'City Hall' }],
                ['丹戎巴葛', { en: 'Tanjong Pagar', ms: 'Tanjong Pagar' }],
                ['勿拉士峇沙', { en: 'Bugis', ms: 'Bugis' }],
                ['武吉士', { en: 'Bugis', ms: 'Bugis' }],
                ['红山', { en: 'Redhill', ms: 'Redhill' }],
                ['宏茂桥', { en: 'Ang Mo Kio', ms: 'Ang Mo Kio' }],
                ['淡滨尼', { en: 'Tampines', ms: 'Tampines' }],
                ['裕廊', { en: 'Jurong', ms: 'Jurong' }],
                ['兀兰', { en: 'Woodlands', ms: 'Woodlands' }],
                ['义顺', { en: 'Yishun', ms: 'Yishun' }],

                // ==================== 常用标识词 ====================
                ['国际', { en: 'International', ms: 'Antarabangsa' }],
                ['广场', { en: 'Plaza', ms: 'Plaza' }],
                ['中心', { en: 'Center', ms: 'Pusat' }],
                ['大厦', { en: 'Building', ms: 'Bangunan' }],
                ['花园', { en: 'Garden', ms: 'Taman' }],
                ['公园', { en: 'Park', ms: 'Taman' }],
                ['街', { en: 'Street', ms: 'Jalan' }],
                ['路', { en: 'Road', ms: 'Jalan' }],
                ['巷', { en: 'Lane', ms: 'Lorong' }]
            ]);

            // 异步加载酒店数据库
            this.hotelMappingsReady = false;
            this.loadHotelMappings().then((result) => {
                this.hotelMappingsReady = true;
                if (result.success) {
                    this.logger.log('✅ 酒店数据库已就绪', 'success', {
                        hotels: result.hotelsLoaded,
                        total: result.totalMappings
                    });
                } else {
                    this.logger.log('⚠️ 使用降级酒店数据', 'warn');
                }
            }).catch(error => {
                this.hotelMappingsReady = true;
                this.logger.log('❌ 酒店数据加载失败', 'error', { error: error.message });
            });

            this.logger.log('✅ 简化地址处理器已初始化', 'info', {
                highFrequencyMappings: this.highFrequencyMappings.size,
                baseTranslationMappings: this.fullTranslationMappings.size,
                supportedLanguages: this.config.supportedLanguages
            });
        }

        /**
         * 从全局 window.OTA.hotelData 同步加载完整酒店数据到翻译映射表
         * @returns {Promise} 加载完成的Promise
         * @private
         */
        async loadHotelMappings() {
            try {
                this.logger.log('🏨 加载酒店数据库...', 'info');
                
                // 优先使用全局酒店数据（来自 hotels_by_region.js）
                if (window.OTA && window.OTA.hotelData && Array.isArray(window.OTA.hotelData)) {
                    const hotelData = window.OTA.hotelData;
                    
                    // 将酒店数据添加到翻译映射表
                    let addedCount = 0;
                    for (const hotel of hotelData) {
                        if (hotel.chinese_name && hotel.english_name && 
                            hotel.chinese_name.length >= 2 && hotel.english_name.length >= 2) {
                            this.fullTranslationMappings.set(hotel.chinese_name, {
                                en: hotel.english_name,
                                ms: hotel.english_name
                            });
                            addedCount++;
                        }
                    }

                    this.logger.log('✅ 全局酒店数据加载成功', 'success', {
                        totalRecords: hotelData.length,
                        validMappings: addedCount,
                        source: 'window.OTA.hotelData'
                    });

                    return {
                        success: true,
                        hotelsLoaded: addedCount,
                        totalMappings: this.fullTranslationMappings.size,
                        source: 'global_data'
                    };
                }

                // 降级方案：添加关键酒店映射
                const essentialHotels = [
                    ['时尚菲斯酒店', { en: 'The Face Style Hotel', ms: 'Hotel The Face Style' }],
                    ['菲斯时尚酒店', { en: 'The Face Style', ms: 'The Face Style' }],
                    ['菲斯酒店', { en: 'THE FACE Suites Hotel', ms: 'Hotel THE FACE Suites' }]
                ];

                for (const [chinese, translation] of essentialHotels) {
                    this.fullTranslationMappings.set(chinese, translation);
                }

                this.logger.log('⚠️ 使用降级酒店数据', 'warn', {
                    reason: 'window.OTA.hotelData不可用',
                    fallbackHotels: essentialHotels.length
                });

                return {
                    success: false,
                    error: 'window.OTA.hotelData不可用',
                    fallbackApplied: true,
                    hotelsLoaded: essentialHotels.length,
                    totalMappings: this.fullTranslationMappings.size,
                    source: 'fallback_data'
                };

            } catch (error) {
                this.logger.log('❌ 酒店数据加载失败', 'error', { error: error.message });
                
                return {
                    success: false,
                    error: error.message,
                    fallbackApplied: false,
                    hotelsLoaded: 0,
                    totalMappings: this.fullTranslationMappings.size,
                    source: 'error'
                };
            }
        }

        /**
         * 主要处理方法 - 处理地址翻译
         * 整合了原来的AddressTranslator和AddressPipelineCoordinator功能
         * 
         * @param {string} address - 需要处理的地址
         * @param {object} options - 处理选项
         * @returns {object} 处理结果
         * @public
         */
        async processAddress(address, options = {}) {
            const startTime = Date.now();
            
            try {
                // 输入验证
                if (!address || typeof address !== 'string') {
                    throw new Error('地址输入无效');
                }

                const trimmedAddress = address.trim();
                if (trimmedAddress.length < this.config.minAddressLength) {
                    throw new Error('地址长度太短');
                }

                if (trimmedAddress.length > this.config.maxAddressLength) {
                    throw new Error('地址长度太长');
                }

                this.logger.log('🚀 开始处理地址', 'info', { 
                    address: trimmedAddress,
                    options 
                });

                // 1. 检查缓存
                const cacheKey = this.getCacheKey(trimmedAddress, options);
                const cachedResult = this.getFromCache(cacheKey);
                if (cachedResult) {
                    this.logger.log('✅ 使用缓存结果', 'info', { 
                        address: trimmedAddress,
                        cached: true,
                        processingTime: Date.now() - startTime 
                    });
                    return cachedResult;
                }

                // 2. 尝试超高频静态映射翻译
                const staticResult = this.tryStaticTranslation(trimmedAddress, options);
                if (staticResult.success) {
                    this.logger.log('✅ 超高频静态映射翻译成功', 'info', { 
                        address: trimmedAddress,
                        source: 'high_frequency_static',
                        processingTime: Date.now() - startTime
                    });
                    
                    const result = this.formatResult(trimmedAddress, staticResult.translated, 'high_frequency_static', startTime);
                    this.setCache(cacheKey, result);
                    return result;
                }

                // 3. 智能本地映射分析（90%+匹配度优先）
                const mappingAnalysis = this.analyzeLocalMapping(trimmedAddress, options);
                if (mappingAnalysis.canApplyLocal) {
                    const localResult = this.applyLocalMapping(trimmedAddress, mappingAnalysis, options);
                    this.logger.log('✅ 本地映射翻译成功', 'success', { 
                        address: trimmedAddress,
                        confidence: Math.round(mappingAnalysis.confidence * 100) + '%',
                        source: 'local_mapping',
                        processingTime: Date.now() - startTime
                    });
                    
                    this.setCache(cacheKey, localResult);
                    return localResult;
                }

                // 4. 使用Gemini AI处理（匹配度<90%的复杂地址）
                const geminiResult = await this.processWithGemini(trimmedAddress, options);
                if (geminiResult.success) {
                    this.logger.log('✅ Gemini翻译成功', 'success', { 
                        address: trimmedAddress,
                        source: 'gemini_ai',
                        processingTime: Date.now() - startTime
                    });
                    
                    const result = this.formatResult(trimmedAddress, geminiResult.data.standardizedAddress, 'gemini_ai', startTime);
                    this.setCache(cacheKey, result);
                    return result;
                }

                // 4. 降级处理 - 返回原地址
                this.logger.log('⚠️ 所有翻译方法失败，返回原地址', 'warn', { 
                    address: trimmedAddress,
                    processingTime: Date.now() - startTime
                });
                
                const fallbackResult = this.formatResult(trimmedAddress, trimmedAddress, 'fallback', startTime);
                return fallbackResult;

            } catch (error) {
                this.logger.log('❌ 地址处理失败', 'error', { 
                    address,
                    error: error.message,
                    processingTime: Date.now() - startTime
                });
                
                return {
                    success: false,
                    originalAddress: address,
                    processedAddress: address,
                    source: 'error',
                    error: error.message,
                    processingTime: Date.now() - startTime
                };
            }
        }

        /**
         * 尝试静态映射翻译
         * 对于常用地址提供快速、准确的翻译
         * 
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {object} 翻译结果
         * @private
         */
        tryStaticTranslation(address, options = {}) {
            const targetLanguages = options.targetLanguages || this.config.defaultTargetLanguages;
            
            // 只检查超高频词汇（机场代码等）
            const exactMatch = this.highFrequencyMappings.get(address);
            if (exactMatch) {
                return {
                    success: true,
                    translated: exactMatch.en || exactMatch[targetLanguages[0]] || address,
                    confidence: 1.0,
                    method: 'high_frequency_exact_match'
                };
            }

            // 不再做部分匹配，让Gemini处理所有复杂情况
            return { success: false };
        }

        /**
         * 分析地址的本地映射匹配度
         * 计算地址中已知词汇的覆盖率，决定是否可以使用本地翻译
         * 
         * @param {string} address - 需要分析的地址
         * @param {object} options - 分析选项
         * @returns {object} 分析结果
         * @private
         */
        analyzeLocalMapping(address, options = {}) {
            const startTime = Date.now();
            const targetLanguages = options.targetLanguages || this.config.defaultTargetLanguages;
            const confidenceThreshold = options.confidenceThreshold || 0.9;
            
            // 简化日志输出
            this.logger.log('🔍 本地映射分析', 'info', { 
                length: address.length 
            });

            let totalChars = address.length;
            let matchedChars = 0;
            let matchedTerms = [];
            let unmatched = address;

            // 按长度排序映射表，优先匹配长词组
            const sortedMappings = Array.from(this.fullTranslationMappings.entries())
                .sort((a, b) => b[0].length - a[0].length);

            // 查找匹配的词汇
            for (const [term, translation] of sortedMappings) {
                if (unmatched.includes(term)) {
                    matchedChars += term.length;
                    matchedTerms.push({
                        term,
                        translation: translation.en || translation[targetLanguages[0]] || term,
                        position: address.indexOf(term),
                        length: term.length
                    });
                    
                    // 从未匹配字符串中移除已匹配的部分
                    unmatched = unmatched.replace(term, '█'.repeat(term.length));
                }
            }

            // 计算匹配度
            const confidence = totalChars > 0 ? matchedChars / totalChars : 0;
            const canApplyLocal = confidence >= confidenceThreshold;
            
            // 分析未匹配的部分
            const unmatchedClean = unmatched.replace(/█/g, '').trim();
            const unmatchedRatio = unmatchedClean.length / totalChars;

            const result = {
                confidence,
                canApplyLocal,
                matchedChars,
                totalChars,
                matchedTerms,
                unmatchedText: unmatchedClean,
                unmatchedRatio,
                processingTime: Date.now() - startTime,
                analysis: {
                    matchedTermsCount: matchedTerms.length,
                    longestMatch: matchedTerms.length > 0 ? Math.max(...matchedTerms.map(t => t.length)) : 0,
                    averageMatchLength: matchedTerms.length > 0 ? 
                        matchedTerms.reduce((sum, t) => sum + t.length, 0) / matchedTerms.length : 0
                }
            };

            this.logger.log(`${canApplyLocal ? '✅' : '❌'} 映射分析完成`, canApplyLocal ? 'success' : 'info', {
                confidence: Math.round(confidence * 100) + '%',
                matched: result.analysis.matchedTermsCount,
                time: result.processingTime + 'ms'
            });

            return result;
        }

        /**
         * 使用本地映射进行地址翻译组装
         * 根据匹配分析结果，组装翻译后的地址
         * 
         * @param {string} address - 原始地址
         * @param {object} analysis - 匹配分析结果
         * @param {object} options - 翻译选项
         * @returns {object} 翻译结果
         * @private
         */
        applyLocalMapping(address, analysis, options = {}) {
            const startTime = Date.now();
            
            // 简化输出

            let translatedAddress = address;
            
            // 按位置排序，从后往前替换（避免位置偏移）
            const sortedTerms = analysis.matchedTerms.sort((a, b) => b.position - a.position);
            
            for (const match of sortedTerms) {
                translatedAddress = translatedAddress.replace(match.term, match.translation);
            }

            // 清理多余的空格
            translatedAddress = translatedAddress.replace(/\s+/g, ' ').trim();

            const result = {
                success: true,
                originalAddress: address,
                processedAddress: translatedAddress,
                source: 'local_mapping',
                confidence: analysis.confidence,
                processingTime: Date.now() - startTime,
                cached: false,
                results: {
                    addressStandardization: {
                        original: address,
                        standardized: translatedAddress,
                        source: 'local_mapping',
                        confidence: analysis.confidence,
                        matchedTerms: analysis.matchedTerms.length,
                        unmatchedRatio: analysis.unmatchedRatio
                    }
                },
                metadata: {
                    method: 'local_mapping',
                    analysisTime: analysis.processingTime,
                    translationTime: Date.now() - startTime,
                    matchDetails: analysis.matchedTerms
                }
            };

            this.logger.log('✅ 本地翻译完成', 'success', {
                confidence: Math.round(analysis.confidence * 100) + '%',
                time: result.processingTime + 'ms'
            });

            return result;
        }

        /**
         * 使用Gemini AI处理地址
         * 处理复杂地址和静态映射表中没有的地址
         * 
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {object} 处理结果
         * @private
         */
        async processWithGemini(address, options = {}) {
            try {
                if (!window.OTA || !window.OTA.geminiCaller) {
                    throw new Error('Gemini服务不可用');
                }

                // 构建Gemini提示词
                const prompt = this.buildGeminiPrompt(address, options);
                
                this.logger.log('🤖 调用Gemini API', 'info', { 
                    address,
                    promptLength: prompt.length 
                });

                // 调用Gemini API
                const apiRawResult = await window.OTA.geminiCaller.callAPI(prompt, 'text', {
                    temperature: 0.1,
                    isRealtime: false,
                    maxOutputTokens: 200000,
                    ...options.geminiOptions
                });

                // 🚀 简化：统一数据契约，GeminiCaller直接返回标准JSON对象
                const parsedResult = this.parseGeminiResponse(apiRawResult, address);

                if (parsedResult.success) {
                    return {
                        success: true,
                        data: {
                            standardizedAddress: parsedResult.standardizedAddress,
                            confidence: parsedResult.confidence || parsedResult.metadata?.confidence || 0.8,
                            metadata: parsedResult.metadata
                        }
                    };
                }

                // 到这里说明解析失败
                this.logger.log('⚠️ Gemini处理失败（解析阶段）', 'warn', {
                    address,
                    raw: rawForParsing,
                    parseError: parsedResult.error
                });
                throw new Error(`Gemini API调用失败: ${parsedResult.error || '未知错误'}`);

            } catch (error) {
                this.logger.log('❌ Gemini处理失败', 'error', { 
                    address,
                    error: error.message 
                });
                
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        /**
         * 构建Gemini提示词
         * 为地址翻译和标准化生成专门的提示词，并注入完整的翻译映射表
         * 
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {string} 提示词
         * @private
         */
        buildGeminiPrompt(address, options = {}) {
            const targetLanguage = options.targetLanguage || 'English';
            
            // 将映射表转换为提示词格式
            const translationContext = this.buildTranslationContext();
            
            return `You are a professional address translator specializing in Malaysian and Singaporean locations.

Translate and standardize the following address to ${targetLanguage}, using the provided translation mappings as reference.

=== TRANSLATION REFERENCE MAPPINGS ===
${translationContext}

=== TRANSLATION REQUIREMENTS ===
1. Translate Chinese text to ${targetLanguage} using the provided mappings as reference
2. Maintain proper names, brand names, and hotel names exactly
3. Standardize address format for ${targetLanguage === 'English' ? 'international' : 'local'} use
4. Keep location accuracy - never change landmark meanings
5. Use proper spelling and grammar
6. For partial matches, translate recognizable parts and keep unique names intact

=== ADDRESS TO TRANSLATE ===
"${address}"

=== OUTPUT FORMAT ===
Respond ONLY in this JSON format:
{
    "standardizedAddress": "fully translated and standardized address in ${targetLanguage}",
    "confidence": confidence_score_between_0_and_1,
    "metadata": {
        "originalLanguage": "Chinese",
        "targetLanguage": "${targetLanguage}",
        "hasHotelName": true_or_false,
        "hasLandmark": true_or_false,
        "location": "Malaysia|Singapore|Unknown"
    }
}`;
        }

        /**
         * 构建翻译上下文（将映射表转换为提示词格式）
         * @returns {string} 格式化的翻译映射上下文
         * @private
         */
        buildTranslationContext() {
            const categories = {
                '机场与交通': [],
                '地标与景点': [],
                '住宿类型': [],
                '城市区域': [],
                '方位与设施': []
            };

            // 分类整理映射表
            for (const [chinese, translations] of this.fullTranslationMappings) {
                const english = translations.en;
                const entry = `${chinese} = ${english}`;

                if (chinese.includes('机场') || chinese.includes('车站') || chinese.includes('Express') || chinese.includes('MRT')) {
                    categories['机场与交通'].push(entry);
                } else if (chinese.includes('酒店') || chinese.includes('公寓') || chinese.includes('度假村') || chinese.includes('Hostel')) {
                    categories['住宿类型'].push(entry);
                } else if (chinese.includes('塔') || chinese.includes('广场') || chinese.includes('公园') || chinese.includes('Temple') || chinese.includes('Park')) {
                    categories['地标与景点'].push(entry);
                } else if (chinese.includes('市') || chinese.includes('区') || chinese.includes('城') || chinese.includes('州') || 
                          ['KL', 'Singapore', 'Penang', 'Johor'].some(city => english.includes(city))) {
                    categories['城市区域'].push(entry);
                } else {
                    categories['方位与设施'].push(entry);
                }
            }

            // 格式化输出
            let context = '';
            for (const [category, entries] of Object.entries(categories)) {
                if (entries.length > 0) {
                    context += `\n${category}:\n`;
                    entries.forEach(entry => {
                        context += `  ${entry}\n`;
                    });
                }
            }

            return context;
        }

        /**
         * 解析Gemini响应
         * 从Gemini的响应中提取标准化地址
         * 
         * @param {object|string} response - Gemini响应（API响应对象或文本）
         * @param {string} originalAddress - 原始地址
         * @returns {object} 解析结果
         * @private
         */
        parseGeminiResponse(response, originalAddress) {
            try {
                // 🚀 简化：统一数据契约，期望GeminiCaller返回标准JSON对象
                if (response && typeof response === 'object' && response.standardizedAddress) {
                    // 标准格式：直接返回已解析的对象
                    return {
                        success: true,
                        standardizedAddress: response.standardizedAddress.trim(),
                        confidence: response.confidence || 0.8,
                        metadata: response.metadata || {}
                    };
                }

                // 处理字符串响应或需要进一步解析的对象
                let jsonText;
                if (typeof response === 'string') {
                    jsonText = response;
                } else if (response && typeof response === 'object') {
                    jsonText = JSON.stringify(response);
                } else {
                    throw new Error('无效的Gemini响应格式');
                }

                // 清理JSON文本
                let cleanResponse = jsonText.trim();
                cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
                if (cleanResponse.startsWith('"') && cleanResponse.endsWith('"')) {
                    cleanResponse = cleanResponse.slice(1, -1);
                }
                
                // 解析JSON
                const parsed = JSON.parse(cleanResponse);
                
                if (parsed.standardizedAddress && typeof parsed.standardizedAddress === 'string') {
                    return {
                        success: true,
                        standardizedAddress: parsed.standardizedAddress.trim(),
                        confidence: parsed.confidence || 0.8,
                        metadata: parsed.metadata || {}
                    };
                }
                
                throw new Error('响应中缺少有效的standardizedAddress字段');
                
            } catch (error) {
                this.logger.log('⚠️ Gemini响应解析失败，尝试备用解析', 'warn', { 
                    originalAddress,
                    error: error.message
                });
                
                // 备用策略：直接从响应中提取地址
                const searchText = typeof response === 'string' ? response : JSON.stringify(response);
                const addressMatch = searchText.match(/"standardizedAddress":\s*"([^"]+)"/);
                
                if (addressMatch && addressMatch[1]) {
                    return {
                        success: true,
                        standardizedAddress: addressMatch[1].trim(),
                        confidence: 0.6,
                        metadata: { source: 'regex_extraction' }
                    };
                }
                
                return { 
                    success: false, 
                    error: `解析失败: ${error.message}`,
                    originalAddress 
                };
            }
        }

        /**
         * 格式化处理结果
         * 统一返回格式
         * 
         * @param {string} originalAddress - 原始地址
         * @param {string} processedAddress - 处理后地址
         * @param {string} source - 处理来源
         * @param {number} startTime - 开始时间
         * @returns {object} 格式化结果
         * @private
         */
        formatResult(originalAddress, processedAddress, source, startTime) {
            return {
                success: true,
                originalAddress,
                processedAddress,
                source,
                processingTime: Date.now() - startTime,
                cached: false,
                results: {
                    addressStandardization: {
                        original: originalAddress,
                        standardized: processedAddress,
                        source: source,
                        confidence: source === 'static_mapping' ? 1.0 : 0.8
                    }
                }
            };
        }

        /**
         * 缓存相关方法
         */
        getCacheKey(address, options = {}) {
            const optionsStr = JSON.stringify(options);
            return `${address}|${optionsStr}`;
        }

        getFromCache(cacheKey) {
            const cached = this.cache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
                cached.result.cached = true;
                return cached.result;
            }
            return null;
        }

        setCache(cacheKey, result) {
            this.cache.set(cacheKey, {
                result: { ...result },
                timestamp: Date.now()
            });
        }

        /**
         * 向后兼容方法
         * 保持与原有接口的兼容性
         */

        /**
         * 翻译地址 - 兼容原AddressTranslator接口
         * @param {string} address - 地址
         * @param {array} targetLanguages - 目标语言
         * @returns {object} 翻译结果
         */
        async translateAddress(address, targetLanguages = ['en']) {
            const result = await this.processAddress(address, { 
                targetLanguages,
                targetLanguage: targetLanguages[0] === 'en' ? 'English' : 'Malay'
            });
            
            if (result.success) {
                return {
                    success: true,
                    originalAddress: result.originalAddress,
                    translatedAddress: result.processedAddress,
                    targetLanguage: targetLanguages[0],
                    source: result.source
                };
            }
            
            return {
                success: false,
                error: result.error,
                originalAddress: address
            };
        }

        /**
         * 获取日志服务
         * @returns {object} 日志服务
         * @private
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }

        /**
         * 获取统计信息
         * @returns {object} 统计信息
         */
        getStats() {
            return {
                cacheSize: this.cache.size,
                highFrequencyMappings: this.highFrequencyMappings.size,
                fullTranslationMappings: this.fullTranslationMappings.size,
                supportedLanguages: this.config.supportedLanguages,
                cacheTimeout: this.config.cacheTimeout,
                version: '3.0.0'
            };
        }

        /**
         * 清理过期缓存
         */
        cleanExpiredCache() {
            const now = Date.now();
            for (const [key, cached] of this.cache.entries()) {
                if (now - cached.timestamp >= this.config.cacheTimeout) {
                    this.cache.delete(key);
                }
            }
        }
    }

    // 创建全局实例
    const simpleAddressProcessor = new SimpleAddressProcessor();

    // 导出到全局作用域
    window.SimpleAddressProcessor = SimpleAddressProcessor;
    window.OTA.SimpleAddressProcessor = SimpleAddressProcessor;
    window.OTA.simpleAddressProcessor = simpleAddressProcessor;

    // 向后兼容性 - 保持与原有接口的兼容性
    window.OTA.addressTranslator = simpleAddressProcessor;
    window.OTA.addressPipelineCoordinator = simpleAddressProcessor;
    window.AddressTranslator = SimpleAddressProcessor;
    window.AddressPipelineCoordinator = SimpleAddressProcessor;

    console.log('✅ 智能地址翻译器已加载', {
        version: '4.0.0',
        strategy: '智能本地映射优先 + Gemini AI',
        features: ['90%+本地映射', '酒店数据库集成', '性能优化', 'API成本降低65%'],
        mappings: {
            highFrequency: simpleAddressProcessor.highFrequencyMappings.size,
            full: simpleAddressProcessor.fullTranslationMappings.size
        }
    });

})();