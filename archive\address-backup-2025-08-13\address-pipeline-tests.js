/**
 * 地址处理流水线测试用例 (简化版)
 * 验证简化流水线的功能正确性和与现有系统的兼容性
 * 架构：Gemini + 精心设计的提示词 + 本地数据库
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @version 2.0.0 (简化架构)
 */

(function() {
    'use strict';

    /**
     * 地址处理流水线测试套件
     * @UTIL
     */
    class AddressPipelineTestSuite {
        constructor() {
            this.testResults = [];
            this.logger = window.logger || console;
            this.totalTests = 0;
            this.passedTests = 0;
            this.failedTests = 0;
        }

        /**
         * 运行所有测试
         * @returns {Promise<object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('🧪 开始运行地址处理流水线测试套件', 'info');
            
            try {
                // 基础组件测试
                await this.testBasicComponents();
                
                // 配置测试 (简化版)
                await this.testConfiguration();
                
                // 地址翻译器测试
                await this.testAddressTranslator();
                
                // 知识库测试
                await this.testKnowledgeBase();
                
                // 流水线协调器测试
                await this.testPipelineCoordinator();
                
                // 表单管理器集成测试
                await this.testFormManagerIntegration();
                
                // 端到端测试
                await this.testEndToEnd();
                
                // 性能测试
                await this.testPerformance();

                // 兼容性测试
                await this.testCompatibility();

                return this.generateTestReport();
                
            } catch (error) {
                this.logger.log('测试套件执行失败', 'error', { error: error.message });
                return this.generateErrorReport(error);
            }
        }

        /**
         * 测试基础组件 (简化版)
         */
        async testBasicComponents() {
            this.logger.log('🔧 测试基础组件 (简化版)', 'info');

            // 测试服务实例
            await this.runTest('服务实例创建', () => {
                this.assert(window.addressPipelineCoordinator, '流水线协调器实例应该存在');
                this.assert(window.OTA?.addressTranslator, '地址翻译器应该存在');
            });

            // 测试精简酒店数据
            await this.runTest('精简酒店数据加载', () => {
                this.assert(window.essentialHotelData, '精简酒店数据应该已加载');
                this.assert(window.essentialHotelData.loaded, '精简酒店数据应该标记为已加载');
                this.assert(window.essentialHotelData.totalHotels > 0, '应该包含酒店数据');
                
                // 测试查询功能
                const testResult = window.essentialHotelData.queryHotel('香格里拉酒店');
                this.assert(testResult, '应该能查询到香格里拉酒店');
                this.assert(testResult.english, '应该返回英文名称');
            });
        }

        /**
         * 测试配置 (简化版)
         */
        async testConfiguration() {
            this.logger.log('⚙️ 测试配置 (简化版)', 'info');

            await this.runTest('Gemini服务可用性', () => {
                const hasGeminiService = !!(window.OTA?.geminiService || window.OTA?.geminiCaller);
                this.assert(hasGeminiService, 'Gemini服务应该可用');
            });

            await this.runTest('精简数据配置', () => {
                this.assert(window.essentialHotelData?.loaded, '应该使用精简启动数据');
                this.assert(window.essentialHotelData?.totalHotels > 0, '精简数据应该包含酒店');
            });
        }



        /**
         * 测试地址翻译器 (简化版)
         */
        async testAddressTranslator() {
            this.logger.log('🔤 测试地址翻译器 (简化版)', 'info');

            const translator = window.OTA?.addressTranslator;

            await this.runTest('翻译器存在性', () => {
                this.assert(translator, '地址翻译器应该存在');
                this.assert(typeof translator.standardizeHotelName === 'function', '酒店名标准化方法应该存在');
                this.assert(typeof translator.processWithGemini === 'function', 'Gemini处理方法应该存在');
            });

            if (translator) {
                await this.runTest('本地数据标准化', async () => {
                    const result = await translator.standardizeWithLocalData('香格里拉酒店');

                    if (result.success) {
                        this.assert(result.standardizedName, '应该返回标准化名称');
                        this.assert(result.source, '应该标明数据源');
                        this.assert(typeof result.confidence === 'number', '应该返回置信度');
                    }
                    // 如果失败也是正常的，因为可能没有匹配的本地数据
                });

                await this.runTest('Gemini提示词构建', () => {
                    if (typeof translator.buildGeminiPrompt === 'function') {
                        const prompt = translator.buildGeminiPrompt('吉隆坡香格里拉酒店', {});
                        this.assert(prompt.includes('香格里拉'), '提示词应该包含酒店名');
                        this.assert(prompt.includes('JSON'), '提示词应该要求JSON格式');
                    }
                });

                await this.runTest('酒店名提取', () => {
                    const address1 = '吉隆坡香格里拉酒店';
                    const extracted1 = translator.extractHotelNameFromAddress(address1);

                    if (extracted1) {
                        this.assert(extracted1.includes('香格里拉'), '应该能提取酒店名');
                    }
                });
            }
        }

        /**
         * 测试知识库 (简化版)
         */
        async testKnowledgeBase() {
            this.logger.log('📚 测试知识库 (简化版)', 'info');

            const knowledgeBase = window.OTA?.knowledgeBase;

            await this.runTest('知识库存在性', () => {
                this.assert(knowledgeBase, '知识库应该存在');
                this.assert(typeof knowledgeBase.queryHotel === 'function', '酒店查询方法应该存在');
            });

            if (knowledgeBase) {
                await this.runTest('酒店查询功能', () => {
                    const result = knowledgeBase.queryHotel('香格里拉酒店');

                    if (result) {
                        this.assert(result.english, '查询结果应该包含英文名');
                        this.assert(result.source, '查询结果应该标明来源');
                    }
                });

                await this.runTest('简化架构验证', () => {
                    const status = knowledgeBase.getKnowledgeBaseStatus();
                    this.assert(typeof status === 'object', '知识库状态应该是对象');
                    this.assert(status.architecture === 'simplified_local_data', '应该使用简化架构');
                });
            }
        }

        /**
         * 测试流水线协调器 (简化版)
         */
        async testPipelineCoordinator() {
            this.logger.log('🔄 测试流水线协调器 (简化版)', 'info');

            const coordinator = window.addressPipelineCoordinator;

            await this.runTest('协调器存在性', () => {
                this.assert(coordinator, '流水线协调器应该存在');
                this.assert(typeof coordinator.processAddress === 'function', '地址处理方法应该存在');
                this.assert(typeof coordinator.executeGeminiProcessing === 'function', 'Gemini处理方法应该存在');
            });

            if (coordinator) {
                await this.runTest('协调器初始化', async () => {
                    await coordinator.initialize();
                    this.assert(coordinator.initialized, '协调器应该已初始化');
                });

                await this.runTest('简化架构验证', () => {
                    const stats = coordinator.getStats();
                    this.assert(stats.stepStats.local_mapping, '应该有本地映射统计');
                    this.assert(stats.stepStats.gemini_processing, '应该有Gemini处理统计');
                    this.assert(!stats.stepStats.maps_api_query, '不应该有Google Maps API统计');
                });

                await this.runTest('地址输入标准化', () => {
                    const result1 = coordinator.normalizeAddressInput('吉隆坡国际机场');
                    this.assert(result1.success, '字符串输入应该标准化成功');
                    this.assert(result1.address === '吉隆坡国际机场', '地址应该正确');

                    const result2 = coordinator.normalizeAddressInput({ pickup: '香格里拉酒店' });
                    this.assert(result2.success, '对象输入应该标准化成功');
                    this.assert(result2.address === '香格里拉酒店', '从对象提取的地址应该正确');
                });
            }
        }

        /**
         * 测试表单管理器集成
         */
        async testFormManagerIntegration() {
            this.logger.log('📝 测试表单管理器集成', 'info');

            const formManager = window.OTA?.managers?.formManager;
            
            await this.runTest('表单管理器存在性', () => {
                this.assert(formManager, '表单管理器应该存在');
                this.assert(typeof formManager.triggerAddressPipeline === 'function', '地址流水线触发方法应该存在');
            });

            if (formManager) {
                await this.runTest('地址提取功能', () => {
                    const testData = {
                        pickup: '吉隆坡国际机场',
                        destination: '香格里拉酒店'
                    };
                    
                    const addresses = formManager.extractAddressesFromData(testData);
                    this.assert(addresses.length === 2, '应该提取到2个地址');
                    this.assert(addresses[0].type === 'pickup', '第一个应该是接送地址');
                    this.assert(addresses[1].type === 'dropoff', '第二个应该是目的地地址');
                });

                await this.runTest('流水线触发条件', () => {
                    const shouldTrigger1 = formManager.shouldTriggerAddressPipeline(
                        { pickup: '吉隆坡国际机场' }, 
                        {}
                    );
                    this.assert(shouldTrigger1, '有地址数据时应该触发流水线');

                    const shouldTrigger2 = formManager.shouldTriggerAddressPipeline(
                        { pickup: '吉隆坡国际机场' }, 
                        { isRealtime: true }
                    );
                    this.assert(!shouldTrigger2, '实时模式下不应该触发流水线');
                });
            }
        }

        /**
         * 端到端测试
         */
        async testEndToEnd() {
            this.logger.log('🔗 端到端测试', 'info');

            // 这里只做基础的端到端测试，避免实际的API调用
            await this.runTest('完整流程模拟', async () => {
                const testData = {
                    pickup: '香格里拉酒店',
                    destination: '吉隆坡国际机场'
                };

                // 模拟表单填充触发流水线
                const formManager = window.OTA?.managers?.formManager;
                if (formManager) {
                    const addresses = formManager.extractAddressesFromData(testData);
                    this.assert(addresses.length > 0, '应该能提取地址');
                    
                    const shouldTrigger = formManager.shouldTriggerAddressPipeline(testData, {});
                    this.assert(shouldTrigger, '应该触发流水线处理');
                }
            });
        }

        /**
         * 性能测试 (简化版)
         */
        async testPerformance() {
            this.logger.log('⚡ 性能测试 (简化版)', 'info');

            await this.runTest('简化架构性能验证', () => {
                // 验证精简数据已加载
                this.assert(window.essentialHotelData?.loaded, '精简数据应该已加载');

                // 验证Google Maps API相关文件未加载
                this.assert(!window.googleMapsService, 'Google Maps服务应该已移除');
                this.assert(!window.GoogleMapsApiConfig, 'Google Maps配置应该已移除');

                // 验证Gemini服务可用
                const hasGeminiService = !!(window.OTA?.geminiService || window.OTA?.geminiCaller);
                this.assert(hasGeminiService, 'Gemini服务应该可用');
            });
        }

        /**
         * 兼容性测试 (简化版)
         */
        async testCompatibility() {
            this.logger.log('🔗 兼容性测试 (简化版)', 'info');

            await this.runTest('与现有系统集成', () => {
                // 检查全局命名空间
                this.assert(window.OTA, 'OTA命名空间应该存在');
                this.assert(window.OTA.addressTranslator, '地址翻译器应该在OTA命名空间中');
                this.assert(window.OTA.knowledgeBase, '知识库应该在OTA命名空间中');

                // 检查向后兼容性
                this.assert(window.OTA.addressTranslationService, '向后兼容性别名应该存在');
            });

            await this.runTest('表单管理器集成', () => {
                const formManager = window.OTA?.formManager;
                if (formManager) {
                    this.assert(typeof formManager.triggerAddressPipeline === 'function',
                        '表单管理器应该有地址流水线触发方法');
                    this.assert(typeof formManager.getAddressPipelineCoordinator === 'function',
                        '表单管理器应该有协调器获取方法');
                }
            });

            await this.runTest('简化架构验证', () => {
                // 验证Google Maps相关组件已移除
                this.assert(!window.googleMapsService, 'Google Maps服务应该已移除');
                this.assert(!window.GoogleMapsApiConfig, 'Google Maps配置应该已移除');

                // 验证Gemini集成
                const translator = window.OTA?.addressTranslator;
                if (translator) {
                    this.assert(typeof translator.processWithGemini === 'function',
                        '地址翻译器应该有Gemini处理方法');
                }
            });
        }

        /**
         * 运行单个测试
         * @param {string} testName - 测试名称
         * @param {Function} testFunction - 测试函数
         */
        async runTest(testName, testFunction) {
            this.totalTests++;
            
            try {
                await testFunction();
                this.passedTests++;
                this.testResults.push({
                    name: testName,
                    status: 'PASSED',
                    timestamp: new Date().toISOString()
                });
                
                this.logger.log(`✅ ${testName}`, 'success');
                
            } catch (error) {
                this.failedTests++;
                this.testResults.push({
                    name: testName,
                    status: 'FAILED',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                this.logger.log(`❌ ${testName}: ${error.message}`, 'error');
            }
        }

        /**
         * 断言函数
         * @param {*} condition - 条件
         * @param {string} message - 错误消息
         */
        assert(condition, message) {
            if (!condition) {
                throw new Error(message || '断言失败');
            }
        }

        /**
         * 生成测试报告
         * @returns {object} 测试报告
         */
        generateTestReport() {
            const report = {
                summary: {
                    totalTests: this.totalTests,
                    passedTests: this.passedTests,
                    failedTests: this.failedTests,
                    successRate: this.totalTests > 0 ? (this.passedTests / this.totalTests * 100).toFixed(2) + '%' : '0%'
                },
                results: this.testResults,
                timestamp: new Date().toISOString(),
                environment: {
                    userAgent: navigator.userAgent,
                    url: window.location.href
                }
            };

            this.logger.log('🧪 测试套件完成', 'info', report.summary);
            
            return report;
        }

        /**
         * 生成错误报告
         * @param {Error} error - 错误
         * @returns {object} 错误报告
         */
        generateErrorReport(error) {
            return {
                summary: {
                    totalTests: this.totalTests,
                    passedTests: this.passedTests,
                    failedTests: this.failedTests,
                    error: error.message
                },
                results: this.testResults,
                timestamp: new Date().toISOString()
            };
        }
    }

    // ===========================================
    // 🌍 全局注册测试套件
    // ===========================================
    
    // 创建测试套件实例
    const addressPipelineTestSuite = new AddressPipelineTestSuite();
    
    // 注册到全局作用域
    window.AddressPipelineTestSuite = AddressPipelineTestSuite;
    window.addressPipelineTestSuite = addressPipelineTestSuite;
    
    // 便捷的测试运行函数
    window.runAddressPipelineTests = () => addressPipelineTestSuite.runAllTests();

    /**
     * 🚀 系统兼容性验证函数
     * 快速检查地址处理流水线与现有系统的兼容性
     * @returns {object} 验证结果
     */
    window.validateAddressPipelineCompatibility = function() {
        const results = {
            compatible: true,
            issues: [],
            warnings: [],
            recommendations: []
        };

        try {
            // 检查核心依赖
            if (!window.GoogleMapsApiConfig) {
                results.issues.push('缺少Google Maps API配置');
                results.compatible = false;
            }

            if (!window.googleMapsService) {
                results.issues.push('缺少Google Maps服务实例');
                results.compatible = false;
            }

            if (!window.addressPipelineCoordinator) {
                results.issues.push('缺少地址处理流水线协调器');
                results.compatible = false;
            }

            // 检查现有系统集成
            if (!window.OTA?.addressTranslator) {
                results.warnings.push('地址翻译器不可用，部分功能可能受限');
            }

            if (!window.OTA?.knowledgeBase) {
                results.warnings.push('知识库不可用，本地数据查询可能受限');
            }

            if (!window.OTA?.managers?.formManager) {
                results.warnings.push('表单管理器不可用，自动触发功能可能受限');
            }

            // 检查数据源
            if (!window.essentialHotelData?.loaded) {
                results.issues.push('精简酒店数据未加载');
                results.compatible = false;
            }

            // 检查性能优化
            if (window.completeHotelData?.loaded) {
                results.warnings.push('完整酒店数据在启动时已加载，可能影响性能');
                results.recommendations.push('考虑启用懒加载机制');
            }

            // 检查API配置
            if (window.GoogleMapsApiConfig) {
                const validation = window.GoogleMapsApiConfig.validate();
                if (!validation.valid) {
                    results.issues.push(`API配置验证失败: ${validation.issues.join(', ')}`);
                    results.compatible = false;
                }
            }

            // 生成建议
            if (results.compatible) {
                results.recommendations.push('系统兼容性良好，可以正常使用地址处理流水线');

                if (results.warnings.length === 0) {
                    results.recommendations.push('所有组件都可用，建议运行完整测试套件验证功能');
                }
            } else {
                results.recommendations.push('请解决兼容性问题后再使用地址处理流水线');
            }

            console.log('🔍 地址处理流水线兼容性检查完成', {
                compatible: results.compatible,
                issues: results.issues.length,
                warnings: results.warnings.length
            });

            return results;

        } catch (error) {
            results.compatible = false;
            results.issues.push(`兼容性检查失败: ${error.message}`);
            return results;
        }
    };

    /**
     * 🚀 快速健康检查函数
     * 检查地址处理流水线的基本健康状态
     * @returns {object} 健康检查结果
     */
    window.checkAddressPipelineHealth = async function() {
        const health = {
            status: 'healthy',
            components: {},
            performance: {},
            timestamp: new Date().toISOString()
        };

        try {
            // 检查Google Maps服务
            if (window.googleMapsService) {
                try {
                    await window.googleMapsService.initialize();
                    const metrics = window.googleMapsService.getMetrics();
                    health.components.googleMapsService = {
                        status: 'healthy',
                        initialized: window.googleMapsService.initialized,
                        metrics: metrics
                    };
                } catch (error) {
                    health.components.googleMapsService = {
                        status: 'unhealthy',
                        error: error.message
                    };
                    health.status = 'degraded';
                }
            } else {
                health.components.googleMapsService = { status: 'missing' };
                health.status = 'unhealthy';
            }

            // 检查流水线协调器
            if (window.addressPipelineCoordinator) {
                try {
                    await window.addressPipelineCoordinator.initialize();
                    const stats = window.addressPipelineCoordinator.getStats();
                    health.components.pipelineCoordinator = {
                        status: 'healthy',
                        initialized: window.addressPipelineCoordinator.initialized,
                        stats: stats
                    };
                } catch (error) {
                    health.components.pipelineCoordinator = {
                        status: 'unhealthy',
                        error: error.message
                    };
                    health.status = 'degraded';
                }
            } else {
                health.components.pipelineCoordinator = { status: 'missing' };
                health.status = 'unhealthy';
            }

            // 检查知识库
            if (window.OTA?.knowledgeBase) {
                const kbStatus = window.OTA.knowledgeBase.getKnowledgeBaseStatus();
                health.components.knowledgeBase = {
                    status: kbStatus.hotel.loaded ? 'healthy' : 'degraded',
                    hotelData: kbStatus.hotel
                };
            } else {
                health.components.knowledgeBase = { status: 'missing' };
            }

            // 性能指标
            health.performance = {
                essentialDataLoaded: !!window.essentialHotelData?.loaded,
                completeDataLoaded: !!window.completeHotelData?.loaded,
                cacheEnabled: window.GoogleMapsApiConfig?.caching?.places?.enabled || false
            };

            console.log('💊 地址处理流水线健康检查完成', {
                status: health.status,
                components: Object.keys(health.components).length
            });

            return health;

        } catch (error) {
            health.status = 'unhealthy';
            health.error = error.message;
            return health;
        }
    };

    console.log('✅ 地址处理流水线测试套件已加载', {
        version: '1.0.0',
        usage: '调用 runAddressPipelineTests() 运行所有测试',
        utilities: [
            'validateAddressPipelineCompatibility() - 兼容性检查',
            'checkAddressPipelineHealth() - 健康检查'
        ]
    });

})();
