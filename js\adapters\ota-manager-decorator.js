// build-stamp: version 2.0.3 (ota-manager-decorator)
/**
 * OTA统一管理器 - 完全合并版本
 * 
 * 设计目标：
 * - 将OTAManager和装饰器合并为单一文件
 * - 提供ApplicationBootstrap兼容接口
 * - 简化架构，减少文件依赖
 * 
 * <AUTHOR>
 * @version 3.0.0 (Unified)
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 简化的OTAManager类 - 内联到装饰器中
     * 不再继承BaseManager，使用组合模式
     */
    class SimplifiedOTAManager {
        constructor() {
            this.name = 'OTAManager';
            this.initialized = false;
            this.currentChannel = null;
            
            console.log('SimplifiedOTAManager created');
        }

        /**
         * 初始化管理器
         */
        async initialize() {
            try {
                console.log('Initializing SimplifiedOTAManager...');
                
                // 简化初始化：只设置基本状态
                this.currentChannel = 'default';
                this.initialized = true;
                
                console.log('SimplifiedOTAManager initialized successfully');
                
                return this;
                
            } catch (error) {
                console.error('Failed to initialize SimplifiedOTAManager:', error);
                throw error;
            }
        }

        /**
         * 处理通道检测结果
         */
        handleChannelDetection(detectionResult) {
            const { channel, confidence } = detectionResult;
            
            console.log(`Channel detected: ${channel} (confidence: ${confidence})`);
            
            // 简化处理：仅更新当前通道状态
            this.currentChannel = channel || 'default';
        }

        /**
         * 获取当前渠道名称
         */
        getCurrentChannelName() {
            return this.currentChannel;
        }

        /**
         * 获取状态信息
         */
        getStatus() {
            return {
                initialized: this.initialized,
                currentChannel: this.currentChannel,
                version: '3.0.0-unified',
                note: 'Business logic handled by flow/ directory'
            };
        }

        /**
         * 销毁管理器
         */
        destroy() {
            console.log('Destroying SimplifiedOTAManager...');
            
            this.currentChannel = null;
            this.initialized = false;
            
            console.log('SimplifiedOTAManager destroyed');
        }
    }

    /**
     * 统一的OTA管理器装饰器
     * 合并了原有的OTAManager和装饰器功能
     */
    class UnifiedOTAManager {
        constructor() {
            // 内部使用简化的OTAManager
            this.otaManager = new SimplifiedOTAManager();
            this.initialized = false;
            
            console.log('🔧 Unified OTAManager已创建');
        }

        /**
         * 标准init方法 - ApplicationBootstrap兼容接口
         */
        async init() {
            if (this.initialized) {
                return this;
            }

            try {
                // 初始化内部管理器
                await this.otaManager.initialize();
                
                this.initialized = true;
                console.log('✅ Unified OTAManager initialized');
                
                return this;
                
            } catch (error) {
                console.error('❌ Unified OTAManager initialization failed:', error);
                throw error;
            }
        }

        /**
         * 向后兼容的initialize方法
         */
        async initialize() {
            return this.init();
        }

        /**
         * 获取管理器名称
         */
        getName() {
            return 'OTAManager';
        }

        /**
         * 检查初始化状态
         */
        isInitialized() {
            return this.initialized;
        }

        /**
         * 代理核心方法到内部管理器
         */
        handleChannelDetection(...args) {
            return this.otaManager.handleChannelDetection(...args);
        }

        getCurrentChannelName() {
            return this.otaManager.getCurrentChannelName();
        }

        getStatus() {
            const baseStatus = this.otaManager.getStatus();
            return {
                ...baseStatus,
                unified: true,
                wrapperVersion: '3.0.0'
            };
        }

        /**
         * 销毁管理器
         */
        destroy() {
            try {
                if (this.otaManager) {
                    this.otaManager.destroy();
                }
                
                this.initialized = false;
                this.otaManager = null;
                
                console.log('✅ Unified OTAManager已销毁');
                
            } catch (error) {
                console.error('❌ 销毁Unified OTAManager时出错:', error);
            }
        }
    }

    // 工厂函数：创建统一的OTA管理器
    function createUnifiedOTAManager() {
        try {
            const manager = new UnifiedOTAManager();
            console.log('✅ Unified OTAManager实例创建完成');
            return manager;
            
        } catch (error) {
            console.error('❌ Unified OTAManager实例创建失败:', error);
            throw error;
        }
    }

    // 暴露到全局作用域
    window.OTA.UnifiedOTAManager = UnifiedOTAManager;
    window.OTA.createOTAManager = createUnifiedOTAManager;
    
    // ApplicationBootstrap兼容：提供getOTAManager函数
    window.getOTAManager = createUnifiedOTAManager;

    // 向后兼容：保持原有的OTAManager类可用
    window.OTAManager = SimplifiedOTAManager;

    console.log('✅ 统一版OTA管理器已加载');

})();