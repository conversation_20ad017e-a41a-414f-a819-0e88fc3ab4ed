/**
 * 表单管理器模块
 * 负责表单数据的填充、收集、验证和处理
 * 支持多种字段类型和智能默认值设置
 *
 * @FIELD_FORMAT: camelCase - 待转换为 snake_case
 * @FIELDS_USED: customerName, customerContact, customerEmail, flightInfo, otaPrice, otaReferenceNumber,
 *               pickupDate, pickupTime, passengerCount, luggageCount, subCategoryId, carTypeId,
 *               drivingRegionId, extraRequirement, dropoff, driverFee, currency, remark
 * @DOM_SELECTORS: getElementById('customerName'), getElementById('customerContact'), etc.
 * @FIELD_MAPPING: fieldMapping object (lines 698-723), frontendFields array (lines 1310-1317)
 *
 * 🧹 架构简化 (2025-08-14):
 * - 统一字段映射管理（移除外部配置依赖）
 * - 修复 arrival_time/departure_time 映射问题
 * - 优化时间字段处理优先级
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 表单管理器类
     * 负责表单相关的所有操作
     */
    class FormManager {
        constructor(elements) {
            this.elements = elements;
            this.languagesDropdown = null;
        }

        /**
         * 初始化表单管理器
         * 🎬 增强：集成动画管理器
         */
        init() {
            // 🎬 初始化动画管理器集成
            this.initializeAnimationManager();

            this.setupRealtimeValidation();
            this.initializePriceConversionListeners();
            this.setDefaultValues();
            this.initAutoResizeTextarea(); // 初始化自适应高度文本框

            // 确保系统数据已加载并填充表单选项
            setTimeout(() => {
                this.populateFormOptions();
                // 修复：将自定义组件的初始化移到这里，确保在标准组件之后加载
                this.initCustomComponents();
                // 初始化权限控制（智能检测用户状态）
                this.initializePermissions();
            }, 100);

            getLogger().log('表单管理器初始化完成', 'success');
        }

        /**
         * 初始化动画管理器集成
         * 🎬 新增：集成动画管理器，提供字段填充动画支持
         * 🔧 修复：添加延迟重试机制，确保动画管理器可用
         */
        initializeAnimationManager() {
            try {
                // 获取动画管理器实例
                this.animationManager = window.OTA?.animationManager || window.animationManager;

                if (this.animationManager) {
                    getLogger().log('✅ 表单管理器动画集成成功', 'success');
                } else {
                    // 🔧 修复：延迟重试，动画管理器可能还在初始化
                    setTimeout(() => {
                        this.animationManager = window.OTA?.animationManager || window.animationManager;
                        if (this.animationManager) {
                            getLogger().log('✅ 表单管理器动画集成成功（延迟）', 'success');
                        } else {
                            getLogger().log('⚠️ 动画管理器不可用，表单填充将使用降级方案', 'warning');
                        }
                    }, 1000);
                }
            } catch (error) {
                getLogger().log('表单管理器动画集成失败，使用降级方案', 'warning', { error: error.message });
                this.animationManager = null;
            }
        }

        /**
         * 初始化自定义UI组件，如多选下拉菜单
         */
        initCustomComponents() {
            // 启动新的语言下拉菜单初始化流程
            this.initLanguagesDropdown();
        }

        /**
         * 初始化价格字段权限控制
         */
        initializePriceFieldPermissions() {
            try {
                // 优先使用新的权限管理器
                let permissions;
                if (window.permissionManager) {
                    permissions = window.permissionManager.checkPriceFieldPermissions();
                } else {
                    // 降级方案：使用APIService
                    const apiService = getApiService();
                    permissions = apiService.checkPriceFieldPermissions();
                }

                getLogger().log('🔐 开始应用价格字段权限控制', 'info', permissions);

                // 获取价格相关元素
                const priceInfoPanel = document.querySelector('[data-panel="price-info"]');
                const otaPriceGroup = document.getElementById('ota_price_group');
                const driverFeeGroup = document.getElementById('driver_fee_group');

                // 判断用户是否有任何价格权限
                const hasAnyPricePermission = permissions.canViewOtaPrice || permissions.canViewDriverFee;

                // 控制整个价格信息面板的显示
                if (priceInfoPanel) {
                    const panelDisplayValue = hasAnyPricePermission ? 'block' : 'none';
                    priceInfoPanel.style.display = panelDisplayValue;
                    getLogger().log(`💰 价格信息面板显示状态: ${panelDisplayValue}`, 'info');
                } else {
                    getLogger().log('未找到价格信息面板元素', 'warning');
                }

                // 根据权限显示/隐藏具体字段（仅在面板显示时有意义）
                if (hasAnyPricePermission) {
                    if (otaPriceGroup) {
                        const displayValue = permissions.canViewOtaPrice ? 'flex' : 'none';
                        otaPriceGroup.style.display = displayValue;
                        getLogger().log(`OTA价格字段显示状态: ${displayValue}`, 'info');
                    } else {
                        getLogger().log('未找到otaPriceGroup元素', 'warning');
                    }

                    if (driverFeeGroup) {
                        const displayValue = permissions.canViewDriverFee ? 'flex' : 'none';
                        driverFeeGroup.style.display = displayValue;
                        getLogger().log(`司机费用字段显示状态: ${displayValue}`, 'info');
                    } else {
                        getLogger().log('未找到driverFeeGroup元素', 'warning');
                    }
                } else {
                    // 没有任何价格权限时，确保字段也被隐藏（防御性编程）
                    if (otaPriceGroup) otaPriceGroup.style.display = 'none';
                    if (driverFeeGroup) driverFeeGroup.style.display = 'none';
                    getLogger().log('🔒 所有价格字段已隐藏（无权限）', 'info');
                }

                getLogger().log('✅ 价格字段权限控制应用完成', 'success', {
                    canViewOtaPrice: permissions.canViewOtaPrice,
                    canViewDriverFee: permissions.canViewDriverFee,
                    hasAnyPricePermission,
                    priceInfoPanelVisible: hasAnyPricePermission
                });

            } catch (error) {
                getLogger().logError('初始化价格字段权限控制失败', error);
            }
        }

        /**
         * 初始化语言选项权限控制
         */
        initializeLanguagePermissions() {
            try {
                // 优先使用新的权限管理器
                let permissions;
                if (window.permissionManager) {
                    permissions = window.permissionManager.checkLanguagePermissions();
                } else {
                    // 降级方案：使用APIService
                    const apiService = getApiService();
                    permissions = apiService.checkLanguagePermissions();
                }

                getLogger().log('🔐 开始应用语言选项权限控制', 'info', permissions);

                // 获取Paging选项元素
                const pagingCheckbox = document.getElementById('lang_5');
                const pagingLabel = document.querySelector('label[for="lang_5"]');

                // 根据权限显示/隐藏Paging选项
                if (pagingCheckbox && pagingLabel) {
                    if (permissions.canUsePaging) {
                        // 有权限：显示Paging选项
                        pagingCheckbox.style.display = '';
                        pagingLabel.style.display = '';
                        getLogger().log('✅ Paging选项已启用（显示）', 'success');
                    } else {
                        // 无权限：隐藏Paging选项
                        pagingCheckbox.style.display = 'none';
                        pagingLabel.style.display = 'none';
                        // 如果已选中，取消选中
                        pagingCheckbox.checked = false;
                        getLogger().log('🔒 Paging选项已隐藏（受限用户）', 'info');
                    }
                } else {
                    getLogger().log('未找到Paging选项元素', 'warning', {
                        pagingCheckbox: !!pagingCheckbox,
                        pagingLabel: !!pagingLabel
                    });
                }

                getLogger().log('语言选项权限控制初始化完成', 'success', {
                    canUsePaging: permissions.canUsePaging
                });

            } catch (error) {
                getLogger().logError('初始化语言选项权限控制失败', error);
            }
        }

        /**
         * 初始化渠道权限控制 - Linus重构版本
         * 核心原则：简洁、直接、优先显示受限用户的主要渠道
         */
        initializeChannelPermissions() {
            try {
                getLogger().log('🔧 开始初始化渠道权限控制', 'info');

                const otaSelect = document.getElementById('ota');
                if (!otaSelect) {
                    getLogger().log('未找到OTA渠道选择器', 'warning');
                    return;
                }

                // 获取当前用户身份（从多个来源尝试）
                const userIdentifier = this.getCurrentUserIdentifier();
                if (!userIdentifier) {
                    getLogger().log('无法确定用户身份，使用默认渠道设置', 'warning');
                    return;
                }

                getLogger().log('用户身份确认', 'debug', { 
                    userIdentifier: String(userIdentifier).substring(0, 20) + '...',
                    type: typeof userIdentifier
                });

                // 检查权限配置API可用性
                const hasPermissionAPI = !!(window.OTA?.config?.getAllowedChannels);
                getLogger().log('权限配置API检查', 'debug', { 
                    hasPermissionAPI,
                    hasWindowOTA: !!window.OTA,
                    hasConfig: !!(window.OTA?.config)
                });

                // 使用新的权限API - 简洁而强大
                const allowedChannels = window.OTA?.config?.getAllowedChannels?.(userIdentifier);
                const defaultChannel = window.OTA?.config?.getDefaultChannel?.(userIdentifier);

                getLogger().log('权限配置结果', 'debug', {
                    hasAllowedChannels: !!allowedChannels,
                    allowedChannelsLength: allowedChannels?.length || 0,
                    defaultChannel: defaultChannel || 'none',
                    isRestricted: !!allowedChannels
                });

                if (!allowedChannels) {
                    // 无限制用户，保持原有选项
                    getLogger().log('🌐 用户无渠道限制，保持所有选项', 'info');
                    return;
                }

                // Linus方法：重建选项而不是隐藏，确保正确的显示顺序
                this.rebuildChannelOptions(otaSelect, allowedChannels, defaultChannel);

                getLogger().log('✅ 渠道权限控制完成', 'success', {
                    userIdentifier: String(userIdentifier).substring(0, 20) + '...',
                    channelCount: allowedChannels.length,
                    defaultChannel
                });

            } catch (error) {
                getLogger().logError('初始化渠道权限控制失败', error);
            }
        }

        /**
         * 获取当前用户标识 - Linus风格：从多个来源智能获取
         */
        getCurrentUserIdentifier() {
            // 1. 优先使用权限管理器的用户信息
            if (window.permissionManager?.getUserIdentifier) {
                const userIdentifier = window.permissionManager.getUserIdentifier();
                if (userIdentifier) {
                    getLogger().log('从权限管理器获取用户标识', 'debug', { userIdentifier });
                    return userIdentifier;
                }
            }

            // 2. 从AppState获取
            try {
                const user = getAppState().get('auth.user');
                if (user?.email) {
                    getLogger().log('从AppState获取用户标识', 'debug', { email: user.email });
                    return user.email.toLowerCase();
                }
                if (user?.id) {
                    getLogger().log('从AppState获取用户ID', 'debug', { id: user.id });
                    return user.id;
                }
            } catch (error) {
                getLogger().log('从AppState获取用户信息失败', 'debug', { error: error.message });
            }

            // 3. 从全局变量获取
            if (window.currentUserId) return window.currentUserId;
            if (window.userId) return window.userId;

            // 4. 从localStorage获取
            const storedUserId = localStorage.getItem('userId') || localStorage.getItem('currentUserId');
            if (storedUserId) return storedUserId;

            // 5. 从URL参数获取（备用方案）
            const urlParams = new URLSearchParams(window.location.search);
            const urlUserId = urlParams.get('userId') || urlParams.get('user_id');
            if (urlUserId) return urlUserId;

            getLogger().log('无法从任何来源获取用户标识', 'warning');
            return null;
        }

        /**
         * 重建渠道选项 - Linus方法：清晰的创建而不是模糊的隐藏
         * @param {HTMLSelectElement} selectElement - 选择器元素
         * @param {Array} allowedChannels - 允许的渠道列表（已排序）
         * @param {string} defaultChannel - 默认选中的渠道
         */
        rebuildChannelOptions(selectElement, allowedChannels, defaultChannel) {
            // 1. 清空所有选项 - 一刀切，简单明了
            selectElement.innerHTML = '';

            // 2. 添加占位符（如果需要）
            const placeholder = document.createElement('option');
            placeholder.value = '';
            placeholder.textContent = '请选择渠道';
            placeholder.disabled = true;
            placeholder.selected = !defaultChannel; // 只有在没有默认渠道时才选中占位符
            selectElement.appendChild(placeholder);

            // 3. 按顺序添加允许的渠道（第一个就是优先渠道）
            allowedChannels.forEach((channel, index) => {
                const option = document.createElement('option');
                option.value = channel;
                option.textContent = channel;
                
                // 第一个渠道自动选中（或者是指定的默认渠道）
                if (channel === defaultChannel || (index === 0 && !defaultChannel)) {
                    option.selected = true;
                }
                
                selectElement.appendChild(option);
            });

            getLogger().log('🔄 渠道选项重建完成', 'info', {
                channelCount: allowedChannels.length,
                firstChannel: allowedChannels[0],
                defaultChannel
            });
        }

        /**
         * 过滤渠道选项（保留旧方法以兼容性）
         * @param {HTMLSelectElement} selectElement - 下拉选择器元素
         * @param {Array} allowedChannels - 允许的渠道列表
         */
        filterChannelOptions(selectElement, allowedChannels) {
            if (!selectElement || !allowedChannels || allowedChannels.length === 0) {
                // 如果没有允许的渠道，隐藏所有非占位符选项
                const options = Array.from(selectElement.options);
                options.forEach((option) => {
                    const isPlaceholder = option.value === '' || option.disabled === true;
                    option.style.display = isPlaceholder ? '' : 'none';
                });
                getLogger().log('🔒 渠道选项已隐藏（无权限或未提供允许列表）', 'info');
                return;
            }

            // 过滤选项，只显示允许的渠道
            const allowedSet = new Set(allowedChannels);
            const options = Array.from(selectElement.options);
            let visibleCount = 0;

            options.forEach((option) => {
                const isPlaceholder = option.value === '' || option.disabled === true;
                if (isPlaceholder) {
                    // 始终显示占位符（如果存在）
                    option.style.display = '';
                } else if (allowedSet.has(option.value) || allowedSet.has(option.text)) {
                    // 显示允许的选项
                    option.style.display = '';
                    visibleCount++;
                } else {
                    // 隐藏不允许的选项
                    option.style.display = 'none';
                }
            });

            getLogger().log('🎯 渠道选项已过滤', 'info', {
                totalOptions: options.length - 1, // 减去占位符
                visibleOptions: visibleCount,
                allowedChannels: allowedChannels
            });
        }

        /**
         * 智能初始化权限控制
         * @description 检测用户状态，如果已登录则应用权限，否则跳过
         */
        initializePermissions() {
            try {
                const currentUser = getAppState().get('auth.user');
                
                if (currentUser && currentUser.email) {
                    // 用户已登录，应用权限控制
                    getLogger().log('检测到用户已登录，应用权限控制', 'info', {
                        email: currentUser.email
                    });
                    
                    this.initializePriceFieldPermissions();
                    this.initializeLanguagePermissions();
                    this.initializeChannelPermissions();
                } else {
                    // 用户未登录，跳过权限控制
                    getLogger().log('用户未登录，跳过权限控制初始化', 'info');
                }
            } catch (error) {
                getLogger().logError('智能权限初始化失败', error);
            }
        }

        /**
         * 初始化语言选择器（简化版本）
         */
        async initLanguagesDropdown() {
            const logger = getLogger();

            try {
                logger.log('🔧 [FormManager] 初始化原生语言选择器', 'info');

                // 检查语言选择器元素是否存在
                if (!this.elements.languagesIdArray) {
                    logger.log('⚠️ [FormManager] 语言选择器元素不存在，跳过初始化', 'debug', {
                        availableElements: Object.keys(this.elements || {}).filter(key => key.includes('language'))
                    });
                    return false;
                }

                // 检查元素类型
                const elementType = this.elements.languagesIdArray.tagName;
                logger.log('✅ [FormManager] 语言选择器已就绪', 'success', {
                    elementId: this.elements.languagesIdArray.id,
                    elementType,
                    hasOptions: this.elements.languagesIdArray.options?.length > 0
                });

                return true;

            } catch (error) {
                logger.logError('💥 [FormManager] 初始化语言选择器失败', {
                    error: error.message,
                    stack: error.stack,
                    hasElements: !!this.elements,
                    elementsKeys: this.elements ? Object.keys(this.elements) : []
                });
                return false;
            }
        }



        /**
         * 获取语言数据 - 使用统一数据管理器
         * @returns {Promise<Array>} 语言数据数组
         */
        async getLanguageData() {
            try {
                // 🔧 优先使用统一数据管理器
                const dataManager = window.OTA?.unifiedDataManager || window.unifiedDataManager;
                
                if (dataManager && typeof dataManager.getData === 'function') {
                    return await dataManager.getData('languages', {
                        useCache: true,
                        fallbackToCache: true
                    });
                }

                // 降级到原有逻辑
                return this.getLegacyLanguageData();
                
            } catch (error) {
                const logger = getLogger();
                logger.logError('[FormManager] 统一数据管理器获取语言数据失败，使用降级方案', error);
                
                return this.getLegacyLanguageData();
            }
        }

        /**
         * 降级的语言数据获取方法
         * @returns {Array} 语言数据数组
         */
        getLegacyLanguageData() {
            let systemData = getAppState().get('systemData');
            const apiService = getApiService();

            // 强制数据同步：优先使用ApiService的静态数据
            if (!systemData || !systemData.languages || systemData.languages.length === 0) {
                if (apiService.staticData && apiService.staticData.languages && apiService.staticData.languages.length > 0) {
                    getAppState().setSystemData(apiService.staticData);
                    systemData = apiService.staticData;
                } else {
                    // 使用语言管理器的fallback数据
                    const languageManager = getLanguageManager();
                    const fallbackLanguages = languageManager.getLanguagesSync({ enabledOnly: true });
                    const fallbackData = { languages: fallbackLanguages };
                    getAppState().setSystemData(fallbackData);
                    systemData = fallbackData;
                }
            }

            return systemData?.languages || [];
        }



        /**
         * 设置表单的默认值
         */
        setDefaultValues() {
            // 延迟执行，确保用户认证状态已经加载
            setTimeout(() => {
                const defaultUserId = getApiService().getDefaultBackendUserId();
                if (defaultUserId && this.elements.incharge_by_backend_user_id) {
                    this.elements.incharge_by_backend_user_id.value = defaultUserId;
                    getLogger().log('已设置默认负责人', 'info', { userId: defaultUserId });
                } else {
                    getLogger().log('无法设置默认负责人', 'warning', { 
                        hasUserId: !!defaultUserId,
                        hasElement: !!this.elements.incharge_by_backend_user_id
                    });
                }
            }, 500);
        }

        /**
         * 填充表单选项
         */
        populateFormOptions() {
            try {
                // 获取系统数据，如果AppState中没有，则使用ApiService的静态数据作为降级
                let systemData = getAppState().get('systemData') || {};

                // 检查是否需要重新填充DOM选项
                const isDataIncomplete = !systemData.backendUsers || systemData.backendUsers.length === 0;

                if (isDataIncomplete) {
                    getLogger().log('检测到DOM未被填充，需要从AppState重新填充选项', 'warning', {
                        backendUsersCount: (systemData.backendUsers || []).length,
                        carTypesCount: (systemData.carTypes || []).length,
                        subCategoriesCount: (systemData.subCategories || []).length
                    });

                    // 尝试从ApiService获取静态数据作为降级方案
                    const apiService = getApiService();
                    if (apiService && apiService.staticData) {
                        systemData = apiService.staticData;
                        getAppState().setSystemData(systemData);
                        getLogger().log('已从ApiService加载静态数据作为降级方案', 'info');
                    }
                }

                // 安全地填充各种下拉选项 - 增加存在性检查
                if (this.elements.sub_category_id && systemData.subCategories) {
                    this.populateSelect(this.elements.sub_category_id, systemData.subCategories, 'id', 'name', 'form.selectServiceType');
                }

                if (this.elements.car_type_id && systemData.carTypes) {
                    this.populateSelect(this.elements.car_type_id, systemData.carTypes, 'id', 'name', 'form.selectCarType');
                }

                // 注意：incharge_by_backend_user_id 是hidden字段，根据电邮自动映射，无需填充选项

                if (this.elements.driving_region_id && systemData.drivingRegions) {
                    this.populateSelect(this.elements.driving_region_id, systemData.drivingRegions, 'id', 'name', 'form.selectDrivingRegion');
                }
                // this.populateSelect(this.elements.languagesIdArray, systemData.languages, 'id', 'name', 'form.selectLanguages'); // 由 initCustomComponents 处理

                // 设置默认值
                const defaultUserId = getApiService().getDefaultBackendUserId();
                if (defaultUserId && this.elements.incharge_by_backend_user_id) {
                    this.elements.incharge_by_backend_user_id.value = defaultUserId;
                }

                // 填充OTA渠道选项
                this.populateOtaChannelOptions();
                
                // 添加子分类提示
                this.addSubCategoryTooltips();
                
                getLogger().log('表单选项填充完成', 'success');
                
                // 记录成功填充的选项数量
                getLogger().log('表单选项填充完成', 'success', {
                    subCategoriesCount: systemData.subCategories?.length || 0,
                    carTypesCount: systemData.carTypes?.length || 0,
                    backendUsersCount: systemData.backendUsers?.length || 0,
                    drivingRegionsCount: systemData.drivingRegions?.length || 0,
                    elementsStatus: {
                        sub_category_id: !!this.elements.sub_category_id,
                        car_type_id: !!this.elements.car_type_id,
                        incharge_by_backend_user_id: !!this.elements.incharge_by_backend_user_id, // hidden字段，通过电邮映射
                        driving_region_id: !!this.elements.driving_region_id
                    }
                });

            } catch (error) {
                getLogger().logError('populateFormOptions: 填充表单选项时发生错误', {
                    error: error.message,
                    stack: error.stack,
                    elementsAvailable: Object.keys(this.elements || {}).length,
                    systemDataKeys: Object.keys(getAppState().get('systemData') || {})
                });

                // 尝试使用最小的错误恢复
                try {
                    const apiService = getApiService();
                    if (apiService && apiService.staticData) {
                        getLogger().log('尝试从ApiService静态数据恢复', 'warning');
                        const systemData = apiService.staticData;
                        getAppState().setSystemData(systemData);
                    }
                } catch (recoveryError) {
                    getLogger().logError('populateFormOptions: 错误恢复失败', {
                        error: recoveryError.message
                    });
                }
            }
        }

        /**
         * 简化的选择框选项填充 - 完全重写版本
         * @param {HTMLSelectElement} selectElement - 选择框元素
         * @param {Array} options - 选项数组
         * @param {string} valueField - 值字段名
         * @param {string} textField - 文本字段名
         * @param {string} placeholderKey - 占位符的国际化键名（可选）
         */
        populateSelect(selectElement, options, valueField, textField, placeholderKey = null) {
            // 静默检查，失败时直接返回
            if (!selectElement || !Array.isArray(options) || !valueField || !textField) {
                return;
            }

            // 清空并重建选项
            selectElement.innerHTML = '';

            // 添加占位符
            if (placeholderKey) {
                const placeholder = document.createElement('option');
                placeholder.value = '';
                placeholder.textContent = '请选择...';
                placeholder.disabled = true;
                placeholder.selected = true;
                selectElement.appendChild(placeholder);
            }

            // 添加选项
            options.forEach(option => {
                if (option && option[valueField] !== undefined && option[textField] !== undefined) {
                    const optionElement = document.createElement('option');
                    optionElement.value = option[valueField];
                    optionElement.textContent = option[textField];
                    selectElement.appendChild(optionElement);
                }
            });
        }

        /**
         * 从解析数据填充表单
         * @param {object} data - 解析后的数据
         * @param {object} options - 填充选项
         * @param {boolean} options.isRealtime - 是否为实时填充模式
         */
        fillFormFromData(data, options = {}) {
            // 🚀 性能优化：检测实时填充模式
            const isRealtimeMode = options.isRealtime || this.detectRealtimeMode();
            if (isRealtimeMode) {
                // 实时模式下禁用动画以提升性能
                this._realtimeFillMode = true;
                getLogger().log('🚀 启用实时填充模式（禁用动画）', 'info');
            }
            // 字段映射表：AI返回字段名 → HTML元素ID (统一使用snake_case)
            const fieldMapping = {
                'customer_name': 'customer_name',
                'customer_contact': 'customer_contact',
                'customer_email': 'customer_email',
                'pickup': 'pickup',
                'destination': 'destination',
                'date': 'date',
                // 兼容旧字段名
                'pickup_location': 'pickup',
                'dropoff_location': 'destination',
                'pickup_date': 'date',
                'time': 'time',
                // 时间别名（作为接送时间的降级来源）
                'arrival_time': 'time',
                'departure_time': 'time',
                'pickup_time': 'time',
                'passenger_number': 'passenger_number',
                'luggage_number': 'luggage_number',
                'flight_number': 'flight_info',
                'flight_info': 'flight_info',
                'ota_price': 'ota_price',
                // 兼容常见价格字段别名
                'price': 'ota_price',
                'total_price': 'ota_price',
                'currency': 'currency',
                'ota_reference_number': 'ota_reference_number',
                'extra_requirement': 'extra_requirement',
                'remark': 'remark',
                'sub_category_id': 'sub_category_id',
                'car_type_id': 'car_type_id',
                'incharge_by_backend_user_id': 'incharge_by_backend_user_id',  // 统一使用snake_case
                'driving_region_id': 'driving_region_id',
                // 行驶区域常见别名（名称或非ID字段）
                'driving_region': 'driving_region_id',
                'driving_area': 'driving_region_id',
                'region': 'driving_region_id'
                // 'languages_id_array': 'languages_id_array', // 注释：语言需求由本地逻辑主导
            };

            getLogger().log('开始填充表单数据', 'info', { dataKeys: Object.keys(data) });

            // 🚀 性能优化：批量处理DOM操作
            const domUpdates = [];

            // 遍历数据并准备DOM更新操作
            for (const snakeCaseKey in data) {
                if (Object.prototype.hasOwnProperty.call(data, snakeCaseKey)) {
                    // 🚀 简化：统一使用 snake_case 格式，优先使用映射表，否则直接使用字段名
                    const elementKey = fieldMapping[snakeCaseKey] || snakeCaseKey;
                    const element = this.elements[elementKey];

                    // 记录字段映射信息
                    if (fieldMapping[snakeCaseKey]) {
                        getLogger().log(`字段映射: ${snakeCaseKey} → ${elementKey}`, 'info', {
                            value: data[snakeCaseKey],
                            hasElement: !!element
                        });
                    }

                    if (element) {
                        // 🚀 性能优化：收集DOM更新操作而不是立即执行
                        if (element.type === 'checkbox') {
                            domUpdates.push({
                                type: 'checkbox',
                                element,
                                value: Boolean(data[snakeCaseKey]),
                                fieldName: elementKey
                            });
                        } else if (element.tagName === 'SELECT') {
                            // 处理下拉框
                            if (elementKey === 'languagesIdArray' && element.multiple) {
                                // 语言需求使用本地智能检测逻辑，立即处理（特殊情况）
                                this.fillLanguageMultiSelect(element, null, snakeCaseKey, data);
                            } else {
                                // 普通下拉框
                                if (data[snakeCaseKey] !== undefined) {
                                    domUpdates.push({
                                        type: 'select',
                                        element,
                                        value: data[snakeCaseKey],
                                        fieldName: elementKey,
                                        fallbackValue: this.getDefaultValueForField(elementKey, data)
                                    });
                                }
                            }
                        } else {
                            // 🚀 性能优化：收集普通输入框的DOM更新操作
                            if (data[snakeCaseKey] !== undefined) {
                                // 特殊处理基于输入类型的日期/时间格式
                                if (element.type === 'date' && data[snakeCaseKey]) {
                                    const dateValue = this.formatDateForInput(data[snakeCaseKey]);
                                    domUpdates.push({
                                        type: 'date',
                                        element,
                                        value: dateValue,
                                        fieldName: elementKey,
                                        originalValue: data[snakeCaseKey]
                                    });
                                } else if (element.type === 'time') {
                                    // 时间字段：允许空值不覆盖已有值；根据优先级决定覆盖行为
                                    if (data[snakeCaseKey]) {
                                        const timeValue = this.formatTimeForInput(data[snakeCaseKey]);
                                        const priority = {
                                            'pickup_time': 1,
                                            'arrival_time': 2, 
                                            'departure_time': 3,
                                            'time': 4
                                        }[snakeCaseKey] || 5;

                                        // 读取已计划的更高优先级更新是否存在
                                        const existingPlanned = domUpdates.find(u => u.type === 'time' && u.fieldName === elementKey);
                                        const shouldApply = !existingPlanned || priority < existingPlanned.priority;
                                        const isHighPriority = priority <= 3;

                                        if (shouldApply && (isHighPriority || !element.value)) {
                                            // 如果已有低优先级计划且被替换，移除旧计划
                                            if (existingPlanned && priority < existingPlanned.priority) {
                                                const idx = domUpdates.indexOf(existingPlanned);
                                                if (idx >= 0) domUpdates.splice(idx, 1);
                                            }
                                            domUpdates.push({
                                                type: 'time',
                                                element,
                                                value: timeValue,
                                                fieldName: elementKey,
                                                originalValue: data[snakeCaseKey],
                                                priority
                                            });
                                        } else {
                                            getLogger().log(`跳过时间字段覆盖: ${snakeCaseKey} (priority=${priority})`, 'debug', {
                                                existingValue: element.value,
                                                planned: !!existingPlanned,
                                                existingPlannedPriority: existingPlanned?.priority
                                            });
                                        }
                                    } else {
                                        // data[snakeCaseKey] 为 null/空，不做覆盖
                                        if (!element.value) {
                                            getLogger().log(`时间字段保持为空: ${snakeCaseKey}`, 'debug');
                                        } else {
                                            getLogger().log(`保留已存在时间值，忽略空输入: ${snakeCaseKey}`, 'debug', { current: element.value });
                                        }
                                    }
                                } else {
                                    // 普通字段
                                    domUpdates.push({
                                        type: 'input',
                                        element,
                                        value: data[snakeCaseKey],
                                        fieldName: elementKey
                                    });
                                }
                            }
                        }
                    } else {
                        // 特殊处理：OTA渠道字段
                        if (snakeCaseKey === 'ota' && data[snakeCaseKey]) {
                            getLogger().log(`特殊处理OTA渠道字段: ${snakeCaseKey}`, 'info', { value: data[snakeCaseKey] });
                            this.fillOtaChannelField(data[snakeCaseKey]);
                        }
                    }
                }
            }

            // 🚀 性能优化：批量执行DOM更新操作
            this.executeBatchDOMUpdates(domUpdates);

            // 应用智能默认值
            this.applySmartDefaults(data);

            // 🚀 性能优化：清理实时填充模式标识
            if (this._realtimeFillMode) {
                this._realtimeFillMode = false;
                getLogger().log('🚀 实时填充模式已完成', 'info');
            }

            getLogger().log('表单数据填充完成', 'success', {
                fieldsProcessed: Object.keys(data).length,
                realtimeMode: this._realtimeFillMode || false
            });

            // 🚀 新增：触发地址处理流水线
            this.triggerAddressPipeline(data, options);
        }

        /**
         * 🚀 触发地址处理流水线 (简化版)
         * 在Gemini返回数据填充表单后，自动触发简化的地址处理流水线
         * 使用 Gemini + 本地数据库 架构
         * @param {object} data - 填充的数据
         * @param {object} options - 填充选项
         */
        async triggerAddressPipeline(data, options = {}) {
            try {
                // 检查是否启用地址处理流水线
                if (!this.shouldTriggerAddressPipeline(data, options)) {
                    return;
                }

                getLogger().log('🚀 触发地址处理流水线 (简化版)', 'info', {
                    hasPickup: !!data.pickup || !!data.pickup_location,
                    hasDropoff: !!data.destination || !!data.dropoff_location,
                    architecture: 'Gemini + 本地数据库'
                });

                // 获取地址处理流水线协调器
                const pipelineCoordinator = this.getAddressPipelineCoordinator();
                if (!pipelineCoordinator) {
                    getLogger().log('地址处理流水线协调器不可用', 'warn');
                    return;
                }

                // 处理接送地址
                const addressesToProcess = this.extractAddressesFromData(data);

                if (addressesToProcess.length === 0) {
                    getLogger().log('未找到需要处理的地址', 'info');
                    return;
                }

                // 异步处理地址，不阻塞表单填充
                setTimeout(async () => {
                    await this.processAddressesAsync(addressesToProcess, pipelineCoordinator);
                }, 100);

            } catch (error) {
                getLogger().log('触发地址处理流水线失败', 'error', {
                    error: error.message
                });
            }
        }

        /**
         * 🚀 新增：判断是否应该触发地址处理流水线
         * @param {object} data - 数据
         * @param {object} options - 选项
         * @returns {boolean} 是否应该触发
         */
        shouldTriggerAddressPipeline(data, options) {
            // 实时模式下暂时禁用流水线处理（性能考虑）
            if (options.isRealtime || this._realtimeFillMode) {
                return false;
            }

            // 检查是否有地址字段
            const addressFields = [
                'pickup', 'pickup_location',
                'destination', 'dropoff_location', 'dropoff'
            ];

            const hasAddressData = addressFields.some(field =>
                data[field] && typeof data[field] === 'string' && data[field].trim().length > 2
            );

            if (!hasAddressData) {
                return false;
            }

            // 检查用户设置（如果有的话）
            try {
                const settings = getAppState().get('userSettings') || {};
                if (settings.disableAddressPipeline === true) {
                    return false;
                }
            } catch (error) {
                // 忽略设置获取错误
            }

            return true;
        }

        /**
         * 🚀 新增：从数据中提取地址
         * @param {object} data - 数据
         * @returns {Array} 地址列表
         */
        extractAddressesFromData(data) {
            const addresses = [];

            // 提取接送地址
            const pickupAddress = data.pickup || data.pickup_location;
            if (pickupAddress && typeof pickupAddress === 'string' && pickupAddress.trim().length > 2) {
                addresses.push({
                    type: 'pickup',
                    address: pickupAddress.trim(),
                    fieldName: 'pickup',
                    element: this.elements.pickup
                });
            }

            // 提取目的地地址
            const dropoffAddress = data.destination || data.dropoff_location || data.dropoff;
            if (dropoffAddress && typeof dropoffAddress === 'string' && dropoffAddress.trim().length > 2) {
                addresses.push({
                    type: 'dropoff',
                    address: dropoffAddress.trim(),
                    fieldName: 'dropoff',
                    element: this.elements.destination
                });
            }

            return addresses;
        }

        /**
         * 🚀 新增：异步处理地址列表
         * @param {Array} addresses - 地址列表
         * @param {object} pipelineCoordinator - 流水线协调器
         */
        async processAddressesAsync(addresses, pipelineCoordinator) {
            try {
                getLogger().log('开始异步处理地址列表', 'info', {
                    addressCount: addresses.length
                });

                const processingPromises = addresses.map(async (addressInfo) => {
                    try {
                        const result = await pipelineCoordinator.processAddress(
                            addressInfo.address,
                            {
                                type: addressInfo.type,
                                fieldName: addressInfo.fieldName,
                                source: 'form_manager'
                            }
                        );

                        // 如果处理成功且有改进的地址，更新表单
                        if (result.success && result.processedAddress &&
                            result.processedAddress !== addressInfo.address) {

                            this.updateAddressField(addressInfo, result);
                        }

                        return {
                            ...addressInfo,
                            pipelineResult: result,
                            success: result.success
                        };

                    } catch (error) {
                        getLogger().log(`地址处理失败: ${addressInfo.address}`, 'error', {
                            error: error.message
                        });

                        return {
                            ...addressInfo,
                            pipelineResult: null,
                            success: false,
                            error: error.message
                        };
                    }
                });

                const results = await Promise.all(processingPromises);

                // 统计处理结果
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;

                getLogger().log('地址处理流水线完成', 'success', {
                    successCount,
                    totalCount,
                    successRate: `${Math.round(successCount / totalCount * 100)}%`
                });

                // 触发地址处理完成事件
                this.triggerAddressPipelineCompleteEvent(results);

            } catch (error) {
                getLogger().log('异步地址处理失败', 'error', {
                    error: error.message
                });
            }
        }

        /**
         * 🚀 更新地址字段 (简化版) - 增强调试日志
         * @param {object} addressInfo - 地址信息
         * @param {object} pipelineResult - 流水线处理结果 (Gemini处理)
         */
        updateAddressField(addressInfo, pipelineResult) {
            try {
                getLogger().log('🔄 开始更新地址字段', 'info', {
                    fieldName: addressInfo.fieldName,
                    type: addressInfo.type,
                    hasElement: !!addressInfo.element,
                    originalAddress: addressInfo.address,
                    processedAddress: pipelineResult.processedAddress,
                    pipelineSuccess: pipelineResult.success
                });

                if (!addressInfo.element) {
                    getLogger().log('❌ 地址字段元素不存在，跳过更新', 'warn', {
                        fieldName: addressInfo.fieldName
                    });
                    return;
                }

                const oldValue = addressInfo.element.value;
                const newValue = pipelineResult.processedAddress;

                getLogger().log('📝 准备更新字段值', 'info', {
                    fieldName: addressInfo.fieldName,
                    oldValue: oldValue,
                    newValue: newValue,
                    valueChanged: oldValue !== newValue
                });

                // 更新表单字段
                addressInfo.element.value = newValue;

                // 触发change事件以确保其他监听器得到通知
                const changeEvent = new Event('change', { bubbles: true });
                addressInfo.element.dispatchEvent(changeEvent);

                getLogger().log('✅ 地址字段更新完成', 'success', {
                    fieldName: addressInfo.fieldName,
                    updated: true,
                    finalValue: addressInfo.element.value
                });

                getLogger().log('地址字段已更新', 'success', {
                    fieldName: addressInfo.fieldName,
                    oldValue,
                    newValue,
                    source: pipelineResult.primarySource
                });

                // 可选：显示用户通知
                this.showAddressUpdateNotification(addressInfo.fieldName, oldValue, newValue);

            } catch (error) {
                getLogger().log('更新地址字段失败', 'error', {
                    fieldName: addressInfo.fieldName,
                    error: error.message
                });
            }
        }

        /**
         * 🚀 显示地址更新通知 (简化版)
         * @param {string} fieldName - 字段名
         * @param {string} oldValue - 旧值
         * @param {string} newValue - 新值
         */
        showAddressUpdateNotification(fieldName, oldValue, newValue) {
            try {
                // 简化版：仅使用基础通知
                if (window.OTA?.notificationManager) {
                    const fieldDisplayName = fieldName === 'pickup' ? '接送地址' : '目的地地址';

                    window.OTA.notificationManager.show({
                        type: 'info',
                        title: '地址已优化 (Gemini AI)',
                        message: `${fieldDisplayName}已通过AI自动优化`,
                        duration: 2000
                    });
                }
            } catch (error) {
                // 忽略通知显示错误
            }
        }

        /**
         * 🚀 触发地址处理完成事件 (简化版)
         * @param {Array} results - 处理结果 (Gemini处理)
         */
        triggerAddressPipelineCompleteEvent(results) {
            try {
                const event = new CustomEvent('addressPipelineComplete', {
                    detail: {
                        results,
                        timestamp: new Date().toISOString(),
                        source: 'form_manager',
                        architecture: 'simplified_gemini'
                    }
                });

                window.dispatchEvent(event);

                // 通知其他组件
                if (window.OTA?.eventManager) {
                    window.OTA.eventManager.emit('address-pipeline-complete', {
                        results,
                        source: 'form_manager',
                        architecture: 'simplified_gemini'
                    });
                }

            } catch (error) {
                getLogger().log('触发地址处理完成事件失败', 'warn', {
                    error: error.message
                });
            }
        }

        /**
         * 🚀 获取地址处理流水线协调器 (简化版)
         * @returns {object|null} 流水线协调器实例 (Gemini集成版)
         */
        getAddressPipelineCoordinator() {
            // 尝试多种方式获取协调器
            if (window.addressPipelineCoordinator) {
                return window.addressPipelineCoordinator;
            }

            if (window.OTA?.addressPipelineCoordinator) {
                return window.OTA.addressPipelineCoordinator;
            }

            // 尝试通过服务定位器获取
            try {
                const serviceLocator = getServiceLocator();
                if (serviceLocator && serviceLocator.get) {
                    return serviceLocator.get('AddressPipelineCoordinator');
                }
            } catch (error) {
                // 忽略服务定位器错误
            }

            return null;
        }

        /**
         * 检测当前是否为实时填充模式
         * 🚀 性能优化：通过AppState检测实时模式
         * @returns {boolean} 是否为实时填充模式
         */
        detectRealtimeMode() {
            try {
                const currentOrder = getAppState().get('currentOrder');
                return currentOrder && currentOrder.source === 'realtime';
            } catch (error) {
                return false;
            }
        }

        /**
         * 批量执行DOM更新操作
         * 🚀 性能优化：使用requestAnimationFrame批量处理DOM操作，减少重排重绘
         * @param {Array} domUpdates - DOM更新操作数组
         */
        executeBatchDOMUpdates(domUpdates) {
            if (!domUpdates || domUpdates.length === 0) {
                return;
            }

            // 使用requestAnimationFrame确保在下一个重绘周期执行
            requestAnimationFrame(() => {
                getLogger().log(`🚀 开始批量执行${domUpdates.length}个DOM更新操作`, 'info');

                domUpdates.forEach(update => {
                    try {
                        switch (update.type) {
                            case 'checkbox':
                                update.element.checked = update.value;
                                break;
                            case 'select':
                                if (!this.safeSetElementValue(update.element, update.value, update.fieldName)) {
                                    // 如果设置失败，尝试应用默认值
                                    if (update.fallbackValue) {
                                        this.safeSetElementValue(update.element, update.fallbackValue, update.fieldName);
                                        getLogger().log(`已应用默认值: ${update.fieldName} = ${update.fallbackValue}`, 'info');
                                    }
                                }
                                break;
                            case 'date':
                                this.safeSetElementValue(update.element, update.value, update.fieldName);
                                getLogger().log(`日期字段填充: ${update.fieldName}`, 'info', {
                                    original: update.originalValue,
                                    formatted: update.value
                                });
                                break;
                            case 'time':
                                if (update.value == null || update.value === '') {
                                    getLogger().log(`跳过空时间字段填充: ${update.fieldName}`, 'debug', {
                                        original: update.originalValue,
                                        existing: update.element.value,
                                        priority: update.priority
                                    });
                                } else {
                                    this.safeSetElementValue(update.element, update.value, update.fieldName);
                                    getLogger().log(`时间字段填充: ${update.fieldName}`, 'info', {
                                        original: update.originalValue,
                                        formatted: update.value,
                                        primary: update.isPrimary,
                                        priority: update.priority
                                    });
                                }
                                break;
                            case 'input':
                            default:
                                this.safeSetElementValue(update.element, update.value, update.fieldName);
                                break;
                        }
                    } catch (error) {
                        getLogger().log(`DOM更新失败: ${update.fieldName}`, 'error', { error: error.message });
                    }
                });

                getLogger().log('🚀 批量DOM更新完成', 'success');
            });
        }

        /**
         * 收集表单数据
         * @returns {object} 表单数据
         */
        collectFormData() {
            // 🚀 简化：使用统一字段映射服务
            const frontendData = {};

            // 定义前端字段列表 (统一使用snake_case)
            const frontendFields = [
                'customer_name', 'customer_contact', 'customer_email',
                'pickup', 'destination', 'date', 'time',
                'passenger_number', 'luggage_number', 'flight_info',
                'ota_price', 'driver_fee', 'currency',
                'ota_reference_number', 'extra_requirement', 'remark',
                'sub_category_id', 'car_type_id', 'driving_region_id'
            ];

            // 收集前端字段数据
            frontendFields.forEach(field => {
                const element = this.elements[field];
                if (!element) return;
                const raw = element.value;
                if (raw !== null && raw !== undefined && raw.toString().trim() !== '') {
                    frontendData[field] = raw.toString().trim();
                } else if (field === 'ota_price') {
                    // ✅ 确保价格字段存在，默认为 0（避免后端缺字段）
                    frontendData[field] = '0';
                }
            });

            // 🚀 简化：基于订单内容的简单语言检测
            frontendData.languages_id_array = this.detectLanguageFromContent();

            // 收集特殊需求checkbox字段
            if (this.elements.babyChairMain) {
                frontendData.baby_chair = this.elements.babyChairMain.checked;
            }
            if (this.elements.tourGuideMain) {
                frontendData.tour_guide = this.elements.tourGuideMain.checked;
            }
            if (this.elements.meetAndGreetMain) {
                frontendData.meet_and_greet = this.elements.meetAndGreetMain.checked;
            }

            // 处理needs_paging_service字段（与lang_5关联）
            const pagingCheckbox = document.getElementById('lang_5');
            if (pagingCheckbox) {
                frontendData.needs_paging_service = pagingCheckbox.checked;
            }

            // 处理OTA渠道字段
            let otaChannelValue = '';
            if (this.elements.otaChannelCustom && this.elements.otaChannelCustom.value.trim() !== '') {
                otaChannelValue = this.elements.otaChannelCustom.value.trim();
            } else if (this.elements.otaChannel && this.elements.otaChannel.value.trim() !== '') {
                otaChannelValue = this.elements.otaChannel.value.trim();
            }
            if (otaChannelValue) {
                frontendData.ota = otaChannelValue;
            }

            // 处理负责人ID - 统一使用snake_case字段名
            if (this.elements.incharge_by_backend_user_id && this.elements.incharge_by_backend_user_id.value) {
                frontendData.incharge_by_backend_user_id = this.elements.incharge_by_backend_user_id.value;
            }

            // 🚀 简化：数据已经是 snake_case 格式，使用统一服务进行基本处理
            const mapper = window.OTA?.unifiedFieldMapper;
            if (mapper && typeof mapper.processData === 'function') {
                const processedData = mapper.processData(frontendData);

                // 确保必填字段有默认值
                if (!processedData.incharge_by_backend_user_id) {
                    const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                    processedData.incharge_by_backend_user_id = defaultBackendUserId || 1;
                }

                if (!processedData.ota_reference_number && this.elements.ota_reference_number) {
                    const timestamp = Date.now().toString().slice(-6);
                    const refNumber = `GMH-${timestamp}`;
                    this.elements.ota_reference_number.value = refNumber;
                    processedData.ota_reference_number = refNumber;
                }

                getLogger().log('✅ 表单数据收集完成（统一格式）', 'info', {
                    fieldCount: Object.keys(processedData).length
                });

                return processedData;
            } else {
                getLogger().log('⚠️ 统一字段映射服务不可用，使用降级方案', 'warning');
                return frontendData; // 降级方案
            }
        }



        /**
         * 显示验证错误
         * @param {object} errors - 错误对象
         */
        showValidationErrors(errors) {
            let errorMessages = [];
            Object.entries(errors).forEach(([field, messages]) => {
                if (Array.isArray(messages)) {
                    errorMessages = errorMessages.concat(messages);
                } else {
                    errorMessages.push(messages);
                }
            });
            // 这里需要调用UI管理器的showAlert方法
            if (window.OTA.uiManager && window.OTA.uiManager.showAlert) {
                window.OTA.uiManager.showAlert(`验证失败: ${errorMessages.join(', ')}`, 'error');
            }
        }

        /**
         * 显示字段错误
         * @param {string} fieldName - 字段名
         * @param {string} message - 错误消息
         */
        showFieldError(fieldName, message) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.add('error');
            
            // 安全地移除现有错误提示
            try {
                const existingError = element.parentNode ? element.parentNode.querySelector('.field-error') : null;
                if (existingError) {
                    existingError.remove();
                }
            } catch (error) {
                getLogger().logError('showFieldError: 移除现有错误提示失败', {
                    error: error.message,
                    fieldName,
                    elementId: element.id
                });
            }

            // 添加新的错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            element.parentNode.appendChild(errorDiv);
        }

        /**
         * 清除字段错误
         * @param {string} fieldName - 字段名
         */
        clearFieldError(fieldName) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.remove('error');
            
            // 安全地移除错误提示
            try {
                const errorDiv = element.parentNode ? element.parentNode.querySelector('.field-error') : null;
                if (errorDiv) {
                    errorDiv.remove();
                }
            } catch (error) {
                getLogger().logError('clearFieldError: 移除错误提示失败', {
                    error: error.message,
                    fieldName,
                    elementId: element.id
                });
            }
        }

        /**
         * 设置实时输入验证
         */
        setupRealtimeValidation() {
            // 邮箱验证
            if (this.elements.customer_email) {
                this.elements.customer_email.addEventListener('blur', () => {
                    const email = this.elements.customer_email.value;
                    if (email && !getApiService().isValidEmail(email)) {
                        this.showFieldError('customer_email', '邮箱格式不正确');
                    } else {
                        this.clearFieldError('customer_email');
                    }
                });
            }

            // 电话验证
            if (this.elements.customer_contact) {
                this.elements.customer_contact.addEventListener('blur', () => {
                    const phone = this.elements.customer_contact.value;
                    if (phone && !getApiService().isValidPhone(phone)) {
                        this.showFieldError('customer_contact', '电话格式可能不正确');
                    } else {
                        this.clearFieldError('customer_contact');
                    }
                });
            }

            // 日期验证（接送日期）
            if (this.elements.date) {
                this.elements.date.addEventListener('change', () => {
                    const date = this.elements.date.value;
                    if (date) {
                        const selectedDate = new Date(date);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);

                        if (selectedDate < today) {
                            this.showFieldError('pickupDate', '不能选择过去的日期');
                        } else {
                            this.clearFieldError('pickupDate');
                        }
                    }
                });
            }
        }

        /**
         * 初始化价格转换监听器
         * 注意：货币切换的智能转换逻辑已移至PriceManager中处理
         */
        initializePriceConversionListeners() {
            const priceInput = this.elements.ota_price;
            const currencySelect = this.elements.currency;

            if (!priceInput || !currencySelect) {
                return;
            }

            // 监听价格输入变化
            const updateConversion = () => {
                // 通过UIManager获取PriceManager实例
                const uiManager = window.OTA?.uiManager || window.uiManager;
                const priceManager = uiManager?.getManager('price');
                if (priceManager && priceManager.updatePriceConversion) {
                    priceManager.updatePriceConversion();
                }
            };

            // 只监听价格输入变化，货币切换逻辑由PriceManager处理
            priceInput.addEventListener('input', updateConversion);

            getLogger().log('价格转换监听器初始化完成（货币切换逻辑由PriceManager处理）', 'info');
        }

        /**
         * 填充OTA渠道选项和默认值
         * 根据当前登录用户自动设置OTA渠道
         */
        populateOtaChannelOptions() {
            // 统一改为从 user-permissions-config 提供的 COMPLETE_CHANNEL_LIST / getAllowedChannels 获取
            const user = getAppState().get('auth.user');
            const userIdOrEmail = user?.email?.toLowerCase() || user?.id || null;

            // 权限系统提供：受限用户 -> allowedChannels；无限制 -> null (表示全部)
            const allowed = userIdOrEmail ? window.OTA?.config?.getAllowedChannels?.(userIdOrEmail) : null;
            const fullList = window.OTA?.config?.COMPLETE_CHANNEL_LIST || [];

            // 受限列表：直接使用 allowed；否则使用完整列表
            const renderList = allowed && allowed.length > 0 ? allowed : fullList;

            if (!this.elements.otaChannel) return;
            const selectEl = this.elements.otaChannel;
            selectEl.innerHTML = '';

            // 统一策略：不使用空占位符；直接渲染渠道，首项即默认
            renderList.forEach((name, idx) => {
                const opt = document.createElement('option');
                opt.value = name;
                opt.textContent = name;
                if (idx === 0) opt.selected = true;
                selectEl.appendChild(opt);
            });

            getLogger().log('渠道下拉已渲染(统一权限体系)', 'info', {
                restricted: !!allowed,
                count: renderList.length,
                defaultSelected: renderList[0] || null
            });
        }

        /**
         * 初始化渠道检测集成
         * @param {object|null} otaConfig - 用户OTA配置
         */
        initializeChannelDetectionIntegration(otaConfig) {
            try {
                // 只对通用用户（没有专属配置的用户）启用智能渠道检测
                if (!otaConfig && window.OTA && window.OTA.unifiedLanguageDetector) {
                    const availableChannels = window.OTA?.config?.COMPLETE_CHANNEL_LIST || [];

                    // 启用智能渠道检测
                    window.OTA.unifiedLanguageDetector.initChannelDetection(availableChannels);

                    // 启用自动分析功能
                    window.OTA.unifiedLanguageDetector.enableAutoAnalysis(true);

                    getLogger().log('已启用智能渠道检测和自动分析', 'info', {
                        channelCount: availableChannels.length,
                        autoAnalysisEnabled: true
                    });
                } else if (otaConfig) {
                    getLogger().log('用户有专属渠道配置，跳过智能检测', 'info', {
                        userId: otaConfig.userId || 'unknown',
                        defaultChannel: otaConfig.default
                    });
                } else {
                    getLogger().log('统一语言检测器不可用，跳过渠道检测集成', 'warning');
                }
            } catch (error) {
                getLogger().log('渠道检测集成初始化失败', 'error', error);
            }
        }

        /**
         * 添加子分类选择提示
         */
        addSubCategoryTooltips() {
            if (this.elements.sub_category_id) {
                try {
                    const label = this.elements.sub_category_id.parentElement ?
                                 this.elements.sub_category_id.parentElement.querySelector('label') : null;
                    if (label) {
                        label.title = '仅支持：接机(Pickup)、送机(Dropoff)、包车(Charter)三种服务类型';
                        label.style.cursor = 'help';
                    }
                } catch (error) {
                    getLogger().logError('addSubCategoryTooltips: 添加提示失败', {
                        error: error.message,
                        elementId: this.elements.sub_category_id.id
                    });
                }
            }
        }

        /**
         * 应用智能默认值
         * @param {object} data - 订单数据
         */
        applySmartDefaults(data) {
            // 智能默认：子分类
            if (!data.sub_category_id && this.elements.sub_category_id) {
                // 本地默认：接机服务
                this.safeSetElementValue(this.elements.sub_category_id, 2, 'sub_category_id');
                getLogger().log('已应用默认子分类: 接机服务', 'info');
            }

            // 智能默认：车型
            if (!data.car_type_id && this.elements.car_type_id) {
                const recommendedCarType = getApiService().recommendCarType(data.passenger_number);
                this.safeSetElementValue(this.elements.car_type_id, recommendedCarType, 'car_type_id');
                getLogger().log(`已应用推荐车型: ${recommendedCarType}`, 'info');
            }

            // 智能默认：负责人
            if (!data.incharge_by_backend_user_id && this.elements.incharge_by_backend_user_id) {
                const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                this.safeSetElementValue(this.elements.incharge_by_backend_user_id, defaultBackendUserId, 'incharge_by_backend_user_id');
                getLogger().log(`已应用默认负责人: ${defaultBackendUserId}`, 'info');
            }

            // 智能默认：参考号
            if (!data.ota_reference_number && this.elements.ota_reference_number) {
                const timestamp = Date.now().toString().slice(-6);
                this.safeSetElementValue(this.elements.ota_reference_number, `GMH-${timestamp}`, 'ota_reference_number');
                getLogger().log(`已生成参考号: GMH-${timestamp}`, 'info');
            }
        }

        /**
         * 安全设置元素值
         * 🎬 增强：添加动画支持
         * @param {HTMLElement} element - 目标元素
         * @param {*} value - 要设置的值
         * @param {string} fieldName - 字段名（用于日志）
         * @returns {boolean} 是否设置成功
         */
        safeSetElementValue(element, value, fieldName) {
            // 如果是价格字段，通知PriceManager这是程序自动填充
            const isPriceField = fieldName === 'otaPrice';
            if (isPriceField) {
                this.setPriceManagerAutoFillingState(true);
            }
            try {
                if (!element) {
                    getLogger().log(`元素不存在: ${fieldName}`, 'warning');
                    return false;
                }

                if (element.tagName === 'SELECT') {
                    // 允许数字/字符串等价匹配
                    const opts = Array.from(element.options);
                    let match = opts.find(opt => opt.value == value);

                    // 针对行驶区域支持按名称匹配（当传入的是名称或别名时）
                    if (!match && fieldName === 'drivingRegionId' && value) {
                        const normalize = (s) => String(s).toLowerCase().replace(/\s+/g, '');
                        const v = normalize(value);
                        match = opts.find(opt => normalize(opt.textContent) === v);
                    }

                    if (match) {
                        element.value = match.value;
                        getLogger().log(`下拉框设置成功: ${fieldName} = ${match.value}`, 'info');
                        return true;
                    }

                    getLogger().log(`下拉框选项不存在: ${fieldName} = ${value}`, 'info', {
                        availableOptions: opts.map(opt => ({ value: opt.value, text: opt.textContent }))
                    });
                    return false;
                } else {
                    // 🚀 性能优化：实时模式下跳过动画
                    if (this._realtimeFillMode || !this.animationManager || !this.shouldAnimateField(fieldName, element)) {
                        // 直接设置值（实时模式或无动画）
                        element.value = value;
                    } else {
                        // 🎬 使用动画填充字段
                        this.animationManager.animateFieldFill(element, value, { fieldName });
                    }
                    getLogger().log(`字段设置成功: ${fieldName} = ${value}`, 'info');
                    return true;
                }
            } catch (error) {
                getLogger().log(`设置字段值失败: ${fieldName}`, 'error', { error: error.message, value });
                return false;
            } finally {
                // 恢复PriceManager的自动填充状态
                if (isPriceField) {
                    // 延迟恢复，确保值设置操作完成
                    setTimeout(() => {
                        this.setPriceManagerAutoFillingState(false);
                    }, 50);
                }
            }
        }

        /**
         * 判断字段是否应该使用动画填充
         * 🎬 新增：动画字段判断逻辑
         * @param {string} fieldName - 字段名
         * @param {HTMLElement} element - 元素
         * @returns {boolean} 是否应该使用动画
         */
        shouldAnimateField(fieldName, element) {
            // 关键字段使用动画
            const animatedFields = [
                'customerName', 'customerContact', 'pickup', 'dropoff',
                'pickupDate', 'pickupTime', 'passengerCount', 'luggageCount',
                'otaPrice', 'otaReferenceNumber', 'extraRequirement'
            ];

            // 检查字段是否在动画列表中
            if (!animatedFields.includes(fieldName)) {
                return false;
            }

            // 检查元素是否可见
            if (!element || element.offsetParent === null) {
                return false;
            }

            // 检查是否有值变化（避免无意义的动画）
            const currentValue = element.value || '';
            const newValue = String(element.value || '');
            if (currentValue === newValue) {
                return false;
            }

            return true;
        }

        /**
         * 获取字段的默认值（用于下拉框值匹配失败时的降级方案）
         * @param {string} fieldName - 字段名（camelCase）
         * @param {object} data - 订单数据
         * @returns {*} 默认值
         */
        getDefaultValueForField(fieldName, data) {
            switch (fieldName) {
                case 'carTypeId':
                    // 基于乘客人数推荐车型，默认5座
                    return getApiService().recommendCarType(data.passenger_number);
                case 'subCategoryId':
                    // 默认接机服务
                    return 2;
                case 'incharge_by_backend_user_id':
                    // 默认负责人
                    return getApiService().getDefaultBackendUserId();
                case 'drivingRegionId':
                    // 默认驾驶区域（优先使用ID=1，否则使用第一个可选项）
                    try {
                        const sel = this.elements.driving_region_id;
                        if (sel) {
                            const hasOne = Array.from(sel.options).some(o => o.value == 1);
                            if (hasOne) return 1;
                            const firstReal = Array.from(sel.options).find(o => o.value !== '');
                            if (firstReal) return firstReal.value;
                        }
                    } catch (_) {}
                    return 1;
                case 'languagesIdArray':
                    // 使用统一语言管理器的默认策略
                    try {
                        const languageManager = getLanguageManager();
                        return languageManager.getDefaultSelectionSync('form');
                    } catch (error) {
                        getLogger().logError('获取默认语言失败', error);
                        return [2]; // English fallback
                    }
                default:
                    return null;
            }
        }

        /**
         * 填充OTA渠道字段
         * @param {string} otaChannel - OTA渠道值
         */
        fillOtaChannelField(otaChannel) {
            // 内部辅助：标准化字符串用于模糊匹配（忽略大小写、空格、符号、标点）
            const normalize = (str) => {
                if (!str) return '';
                try {
                    return String(str)
                        .toLowerCase()
                        .replace(/[\s_\-.]/g, '')
                        .replace(/[^\p{L}\p{N}]/gu, '');
                } catch (_) {
                    return String(str).toLowerCase().replace(/[\s_\-.]/g, '');
                }
            };

            // 将常见检测到的渠道名映射为下拉可用展示名
            const mapToDropdownValue = (value) => {
                const raw = value || '';
                const n = normalize(raw);
                // 中文别名优先
                if (/精格/.test(raw)) return 'Jing Ge';
                if (/飞猪/.test(raw)) return 'Fliggy';
                // 英文归一
                if (n.includes('jingge')) return 'Jing Ge';
                if (n.includes('fliggy')) return 'Fliggy';
                return raw; // 未知则原样返回
            };

            const desiredValue = mapToDropdownValue(otaChannel);
            // 优先尝试在下拉框中查找匹配项
            if (this.elements.otaChannel) {
                // 先精确匹配映射后的值
                let matchedOption = Array.from(this.elements.otaChannel.options).find(opt => opt.value === desiredValue);

                // 若无精确匹配，执行模糊匹配（忽略大小写、空格、符号、标点）
                if (!matchedOption) {
                    const normalizedDesired = normalize(desiredValue);
                    matchedOption = Array.from(this.elements.otaChannel.options).find(opt => normalize(opt.value) === normalizedDesired);
                }

                if (matchedOption) {
                    this.elements.otaChannel.value = matchedOption.value;
                    // 清空自定义输入框
                    if (this.elements.otaChannelCustom) {
                        this.elements.otaChannelCustom.value = '';
                    }
                    // 触发变更事件以驱动依赖逻辑
                    try {
                        const changeEvent = new Event('change', { bubbles: true });
                        this.elements.otaChannel.dispatchEvent(changeEvent);
                    } catch (_) { /* 忽略事件触发失败 */ }

                    getLogger().log(`OTA渠道设置成功(下拉框): ${matchedOption.value}`, 'info');
                    return;
                }
            }

            // 如果下拉框中没有匹配项，使用自定义输入框
            if (this.elements.otaChannelCustom) {
                this.elements.otaChannelCustom.value = desiredValue;
                getLogger().log(`OTA渠道设置成功(自定义): ${desiredValue}`, 'info');
            }

            // 清空下拉框选择
            if (this.elements.otaChannel) {
                this.elements.otaChannel.value = '';
            }
        }

        /**
         * 填充语言多选字段
         * @param {HTMLSelectElement} element - 多选下拉框元素
         * @param {Array|Object} languageData - 语言数据
         * @param {string} originalField - 原始字段名
         * @param {object} allData - 完整订单数据
         */
        fillLanguageMultiSelect(element, languageData, originalField, allData) {
            // 语言需求由本地逻辑主导，忽略AI返回的语言字段，但执行智能检测
            getLogger().log('使用本地智能语言检测逻辑', 'info', {
                originalField: originalField,
                hasCustomerName: !!(allData.customer_name),
                hasExtraRequirement: !!(allData.extra_requirement || allData.remark)
            });

            // 忽略AI返回的语言数据，直接使用本地智能检测
            let languageIds = [];

            // 执行本地智能语言检测
            if (languageIds.length === 0) {
                getLogger().log('语言数据为空，启用智能检测', 'info', {
                    originalField,
                    languageData,
                    remark: allData.remark,
                    extraRequirement: allData.extra_requirement
                });

                // 基于备注和额外要求智能检测语言 - 使用统一语言检测器
                if (window.OTA && window.OTA.unifiedLanguageDetector) {
                    const combinedText = `${allData.remark || ''} ${allData.extra_requirement || ''} ${allData.customer_name || ''}`;
                    const hasChinese = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/.test(combinedText);
                    languageIds = hasChinese ? [4] : [2]; // 中文[4] 或 英文[2]
                    getLogger().log('智能语言检测成功', 'success', { detectedLanguages: languageIds, hasChinese });
                }

                // 如果智能检测也失败，使用统一管理器的默认策略
                if (languageIds.length === 0) {
                    try {
                        const languageManager = getLanguageManager();
                        languageIds = languageManager.getDefaultSelectionSync('form');
                        getLogger().log('使用统一管理器默认语言', 'info', { defaultLanguages: languageIds });
                    } catch (error) {
                        languageIds = [2]; // Ultimate fallback
                        getLogger().log('使用终极备用语言: 英语', 'info');
                    }
                }
            }

            // 应用语言选择
            let appliedCount = 0;
            languageIds.forEach(langId => {
                const option = Array.from(element.options).find(opt => parseInt(opt.value) === langId);
                if (option) {
                    option.selected = true;
                    appliedCount++;
                }
            });

            getLogger().log(`语言多选设置完成: ${originalField}`, 'info', {
                requestedLanguages: languageIds,
                appliedCount,
                totalOptions: element.options.length
            });

            // 更新多选下拉菜单显示（如果存在）
            if (this.languagesDropdown) {
                this.languagesDropdown.setSelectedValues(languageIds.map(id => id.toString()));
            }
        }

        /**
         * 获取选中的语言ID数组
         * @returns {Array} 语言ID数组（数字类型）
         */
        getSelectedLanguages() {
            // 获取所有语言复选框
            const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]:checked');
            return Array.from(checkboxes)
                .map(checkbox => parseInt(checkbox.value, 10)) // 转换为数字类型
                .filter(value => !isNaN(value) && value > 0); // 过滤无效值
        }

        /**
         * 🚀 简化：基于订单内容检测语言
         * @returns {Object} API格式的语言对象
         */
        detectLanguageFromContent() {
            // 获取订单输入内容
            const orderInput = document.getElementById('orderInput');
            const content = orderInput ? orderInput.value : '';

            // 简单的中文检测：包含中文字符就是中文，否则英文
            const hasChinese = /[\u4e00-\u9fa5]/.test(content);

            return hasChinese
                ? { "0": "4" }  // 中文 (修复: 使用正确的language_id=4)
                : { "0": "2" }; // 英文
        }

        /**
         * 设置语言选择状态
         * @param {Array} languageIds - 语言ID数组 [2, 4] 等
         */
        setLanguageSelection(languageIds) {
            try {
                if (!Array.isArray(languageIds) || languageIds.length === 0) {
                    // 如果没有提供语言ID，设置默认英文
                    languageIds = [2];
                    getLogger().log('setLanguageSelection: 使用默认语言（英文）', 'info');
                }

                // 方法1: 尝试使用多选下拉组件
                if (this.languagesDropdown && typeof this.languagesDropdown.setSelectedValues === 'function') {
                    const stringIds = languageIds.map(id => id.toString());
                    this.languagesDropdown.setSelectedValues(stringIds);
                    getLogger().log('通过多选下拉组件设置语言选择', 'info', { languageIds, stringIds });
                    return;
                }

                // 方法2: 直接操作复选框
                // 首先清除所有选中状态
                const allCheckboxes = document.querySelectorAll('input[name="languagesIdArray"]');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // 设置指定语言的选中状态
                languageIds.forEach(id => {
                    const checkbox = document.querySelector(`input[name="languagesIdArray"][value="${id}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                // 方法3: 触发相关的UI更新事件
                const languageField = this.elements.languagesIdArray;
                if (languageField) {
                    // 触发change事件以更新UI显示
                    const changeEvent = new Event('change', { bubbles: true });
                    languageField.dispatchEvent(changeEvent);
                }

                getLogger().log('已设置语言选择', 'info', { 
                    selectedLanguageIds: languageIds,
                    method: 'checkbox manipulation'
                });

            } catch (error) {
                getLogger().logError('设置语言选择失败', error);
            }
        }

        /**
         * 设置PriceManager的自动填充状态
         * @param {boolean} isAutoFilling - 是否正在自动填充
         */
        setPriceManagerAutoFillingState(isAutoFilling) {
            try {
                // 通过UIManager获取PriceManager实例
                const uiManager = window.OTA?.uiManager || window.uiManager;
                const priceManager = uiManager?.getManager('price');
                if (priceManager && typeof priceManager.setAutoFillingState === 'function') {
                    priceManager.setAutoFillingState(isAutoFilling);
                }
            } catch (error) {
                getLogger().logError('设置PriceManager自动填充状态失败', error);
            }
        }

        /**
         * **新增**: 获取i18n管理器，带重试机制
         * @returns {Object|null} i18n管理器实例
         */
        getI18nManagerWithRetry() {
            // 尝试多种方式获取i18n管理器
            const attempts = [
                () => window.getI18nManager && window.getI18nManager(),
                () => window.OTA && window.OTA.i18nManager,
                () => window.i18nManager
            ];

            for (const attempt of attempts) {
                try {
                    const manager = attempt();
                    if (manager && typeof manager.t === 'function') {
                        return manager;
                    }
                } catch (error) {
                    // 继续尝试下一种方式
                }
            }

            return null;
        }

        /**
         * 格式化日期为HTML input[type="date"]所需的格式 (YYYY-MM-DD)
         * @param {string} dateString - 原始日期字符串
         * @returns {string} 格式化后的日期字符串
         */
        formatDateForInput(dateString) {
            try {
                // 尝试解析各种日期格式
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    getLogger().log('日期格式无效，使用今天日期', 'warning', { input: dateString });
                    return new Date().toISOString().split('T')[0];
                }
                return date.toISOString().split('T')[0];
            } catch (error) {
                getLogger().log('日期解析失败，使用今天日期', 'error', { input: dateString, error: error.message });
                return new Date().toISOString().split('T')[0];
            }
        }

        /**
         * 格式化时间为HTML input[type="time"]所需的格式 (HH:MM)
         * @param {string} timeString - 原始时间字符串
         * @returns {string} 格式化后的时间字符串
         */
        formatTimeForInput(timeString) {
            try {
                // 处理各种时间格式
                if (timeString.includes(':')) {
                    // 已经是HH:MM或HH:MM:SS格式
                    const timeParts = timeString.split(':');
                    return `${timeParts[0].padStart(2, '0')}:${timeParts[1].padStart(2, '0')}`;
                } else {
                    // 尝试解析为完整日期时间
                    const date = new Date(timeString);
                    if (!isNaN(date.getTime())) {
                        return date.toTimeString().slice(0, 5); // HH:MM
                    }
                }

                getLogger().log('时间格式无效，使用当前时间', 'warning', { input: timeString });
                return new Date().toTimeString().slice(0, 5);
            } catch (error) {
                getLogger().log('时间解析失败，使用当前时间', 'error', { input: timeString, error: error.message });
                return new Date().toTimeString().slice(0, 5);
            }
        }

        /**
         * 重置表单
         */
        resetForm() {
            // 重置表单
            if (this.elements.orderForm) {
                this.elements.orderForm.reset();
            }
            if (this.elements.orderInput) {
                this.elements.orderInput.value = '';
            }

            // 清除所有字段错误
            Object.keys(this.elements).forEach(fieldName => {
                this.clearFieldError(fieldName);
            });

            // 重新应用默认值
            this.applySmartDefaults({});

            getLogger().log('表单已重置', 'success');
        }

        /**
         * 初始化自适应高度文本框
         * 为多个输入字段添加自动调整高度功能
         */
        initAutoResizeTextarea() {
            // 需要自适应高度的字段列表 (统一使用snake_case)
            const autoResizeFields = [
                'extra_requirement', // 额外要求文本域
                'pickup',           // 上车地点
                'destination',      // 目的地
                'customer_name',    // 客户姓名
                'flight_info',      // 航班信息
                'ota_reference_number' // OTA参考号
            ];

            autoResizeFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (!element) return;

                // 自动调整高度的函数
                const autoResize = () => {
                    // 重置高度以获取正确的scrollHeight
                    element.style.height = 'auto';

                    // 响应式高度计算：移动端使用较小的尺寸
                    const isMobile = window.innerWidth <= 768;
                    const minHeight = isMobile ? 30 : 35; // 移动端30px，桌面端35px
                    const maxHeight = isMobile ? 120 : 150; // 移动端120px，桌面端150px

                    // 对于文本域，使用更大的尺寸
                    const isTextarea = element.tagName.toLowerCase() === 'textarea';
                    const finalMinHeight = isTextarea ? (isMobile ? 50 : 60) : minHeight;
                    const finalMaxHeight = isTextarea ? (isMobile ? 150 : 200) : maxHeight;

                    // 计算新高度
                    const newHeight = Math.min(
                        Math.max(element.scrollHeight, finalMinHeight),
                        finalMaxHeight
                    );

                    // 设置新高度和平滑过渡
                    element.style.height = newHeight + 'px';
                    element.style.transition = 'height 0.2s ease';
                };

                // 绑定事件监听器
                element.addEventListener('input', autoResize);
                element.addEventListener('paste', () => {
                    // 粘贴后稍微延迟执行，确保内容已插入
                    setTimeout(autoResize, 10);
                });

                // 窗口大小改变时重新调整高度（响应式支持）
                window.addEventListener('resize', () => {
                    setTimeout(autoResize, 100);
                });

                // 初始调整
                autoResize();
            });

            getLogger().log('自适应高度输入框初始化完成', 'success');
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.FormManager = FormManager;

    // 🚀 关键路径优化：早期自注册到依赖容器
    // 🚀 优化版本2.0: 仅定义类，延迟注册到管理器实例化阶段
    // 注册将在ui-manager.js中统一进行，避免重复注册警告
    
    console.log('✅ FormManager类定义完成（阶段2: 配置和类定义）');

})();
