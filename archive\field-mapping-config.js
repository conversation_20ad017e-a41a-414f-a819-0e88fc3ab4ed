/**
 * 依赖标签（Dependency Tags）
 * 文件: js/multi-order/field-mapping-config.js
 * 角色: 字段映射配置（表单字段、接口字段、下拉值域、地区/语言映射）
 * 上游依赖(直接使用):
 *  - 无（纯配置/少量工具函数）
 * 下游被依赖(常见调用方):
 *  - MultiOrderTransformer / Detector / Processor / Validator
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_CONFIG 多订单字段映射配置中心
 * 🏷️ 标签: @OTA_FIELD_MAPPING_CONFIG
 * 📝 说明: 统一管理所有字段映射规则，确保数据转换的一致性和完整性
 * ⚠️ 警告: 修改此配置前请确保了解GoMyHire API规范
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.FieldMappingConfig) {
    console.log('字段映射配置已存在，跳过重复加载');
} else {

/**
 * 字段映射配置对象
 * 包含所有数据转换规则和验证逻辑
 */
const FIELD_MAPPING_CONFIG = {
    // Gemini AI返回字段 (snake_case) → 前端使用字段 (camelCase)
    AI_TO_FRONTEND: {
        'customer_name': 'customerName',
        'customer_contact': 'customerContact',
        'customer_email': 'customerEmail',
        'pickup_location': 'pickup',
        'dropoff_location': 'dropoff',
        'pickup_date': 'pickupDate',
        'pickup_time': 'pickupTime',
        'passenger_count': 'passengerCount',
        'luggage_count': 'luggageCount',
        'ota_price': 'otaPrice',
        'ota_reference_number': 'otaReferenceNumber',
        'extra_requirement': 'extraRequirement',
        'flight_info': 'flightInfo',
        'arrival_time': 'arrivalTime',
        'departure_time': 'departureTime',
        'sub_category_id': 'subCategoryId',
        'car_type_id': 'carTypeId',
        'driving_region_id': 'drivingRegionId',
        'languages_id_array': 'languagesIdArray',
        'meet_and_greet': 'meetAndGreet',
        'baby_chair': 'babyChair',
        'tour_guide': 'tourGuide'
    },

    // 前端字段 (camelCase) → GoMyHire API字段 (snake_case)
    FRONTEND_TO_API: {
        'pickup': 'pickup',                    // 🔧 修复：使用API标准字段名
        'dropoff': 'destination',              // 🔧 修复：使用API标准字段名
        'pickupDate': 'date',                  // 🔧 修复：使用API标准字段名
        'pickupTime': 'time',                  // 🔧 修复：使用API标准字段名
        'luggageCount': 'luggage_number',
        'customerContact': 'customer_contact',
        'customerName': 'customer_name',
        'customerEmail': 'customer_email',
        'otaPrice': 'ota_price',
        'otaReferenceNumber': 'ota_reference_number',
        'extraRequirement': 'extra_requirement',
        'passengerCount': 'passenger_number',  // 🔧 修复：使用API标准字段名
        'subCategoryId': 'sub_category_id',
        'carTypeId': 'car_type_id',
        'drivingRegionId': 'driving_region_id',
        'languagesIdArray': 'languages_id_array',
        'meetAndGreet': 'meet_and_greet',
        'babyChair': 'baby_chair',
        'tourGuide': 'tour_guide',
        'flightInfo': 'flight_info',
        'arrivalTime': 'arrival_time',
        'departureTime': 'departure_time',
        // 🔧 新增：坐标字段映射
        'pickupLat': 'pickup_lat',
        'pickupLong': 'pickup_long',
        'destinationLat': 'destination_lat',
        'destinationLong': 'destination_long',
        // 🔧 新增：司机费用字段映射
        'driverFee': 'driver_fee',
        'driverCollect': 'driver_collect'
    },

    // 备用字段名映射 - 处理不同数据源的字段名变体
    ALTERNATIVE_FIELDS: {
        'pickup': ['pickupLocation', 'pickup_location'],
        'destination': ['dropoff', 'dropoffLocation', 'dropoff_location'],
        'customerContact': ['phone', 'customer_contact', 'contact'],
        'customerName': ['customer_name', 'name'],
        'customerEmail': ['customer_email', 'email'],
        'price': ['otaPrice', 'ota_price', 'amount'],
        'otaPrice': ['price', 'ota_price', 'amount'],
        'date': ['pickupDate', 'pickup_date'],
        // 扩展时间别名，兼容 arrival_time / departure_time
        'time': ['pickupTime', 'pickup_time', 'arrival_time', 'departure_time'],
        'passengerNumber': ['passengerCount', 'passenger_count', 'passengers'],
        'luggageNumber': ['luggageCount', 'luggage_count', 'luggage'],
        'extraRequirement': ['extra_requirement', 'special_requirement', 'notes'],
        'otaReferenceNumber': ['ota_reference_number', 'reference_number', 'booking_reference'],
        // 新增：行驶区域ID别名，兼容多种来源字段
        'drivingRegionId': ['driving_region_id', 'driving_region', 'drivingRegion', 'region', 'driving_area', 'area']
    },

    // GoMyHire API必填字段列表
    REQUIRED_API_FIELDS: [
        'pickup',
        'destination',
        'date',
        'time',
        'customer_name',
        'ota_reference_number',
        'ota_price',
        'sub_category_id',
        'car_type_id',
        'driving_region_id',
        'languages_id_array'
    ],

    // 前端必填字段列表
    REQUIRED_FRONTEND_FIELDS: [
        'pickup',
        'dropoff',
        'pickupDate',
        'pickupTime',
        'customerName',
        'otaReferenceNumber',
        'otaPrice',
        'subCategoryId',
        'carTypeId',
        'drivingRegionId',
        'languagesIdArray'
    ],

    // 字段数据类型定义
    FIELD_TYPES: {
        'customerName': 'string',
        'customerContact': 'string',
        'customerEmail': 'string',
        'pickup': 'string',
        'dropoff': 'string',
        'pickupDate': 'date',
        'pickupTime': 'time',
        'passengerCount': 'number',
        'luggageCount': 'number',
        'otaPrice': 'number',
        'otaReferenceNumber': 'string',
        'extraRequirement': 'string',
        'subCategoryId': 'number',
        'carTypeId': 'number',
        'drivingRegionId': 'number',
        'languagesIdArray': 'array',
        'meetAndGreet': 'boolean',
        'babyChair': 'boolean',
        'tourGuide': 'boolean'
    },

    // 特殊字段处理规则
    SPECIAL_FIELD_RULES: {
        // languages_id_array必须转换为对象格式以避免API错误
        'languagesIdArray': {
            toAPI: (value) => {
                if (Array.isArray(value)) {
                    const objectFormat = {};
                    value.forEach((id, index) => {
                        objectFormat[index.toString()] = id.toString();
                    });
                    return objectFormat;
                }
                return value;
            },
            fromAPI: (value) => {
                if (typeof value === 'object' && value !== null) {
                    return Object.values(value).map(id => parseInt(id));
                }
                return Array.isArray(value) ? value : [2]; // 默认英文
            }
        },

        // 价格字段处理
        'otaPrice': {
            toAPI: (value) => parseFloat(value) || 0,
            fromAPI: (value) => parseFloat(value) || 0
        },

        // 数量字段处理
        'passengerCount': {
            toAPI: (value) => parseInt(value) || 1,
            fromAPI: (value) => parseInt(value) || 1
        },

        'luggageCount': {
            toAPI: (value) => parseInt(value) || 0,
            fromAPI: (value) => parseInt(value) || 0
        }
    }
};

// 导出配置对象
window.OTA = window.OTA || {};
window.OTA.FieldMappingConfig = FIELD_MAPPING_CONFIG;

// 向后兼容
window.FIELD_MAPPING_CONFIG = FIELD_MAPPING_CONFIG;

console.log('✅ 字段映射配置已加载', {
    aiToFrontendMappings: Object.keys(FIELD_MAPPING_CONFIG.AI_TO_FRONTEND).length,
    frontendToApiMappings: Object.keys(FIELD_MAPPING_CONFIG.FRONTEND_TO_API).length,
    alternativeFields: Object.keys(FIELD_MAPPING_CONFIG.ALTERNATIVE_FIELDS).length
});

// 结束防重复加载检查
}
