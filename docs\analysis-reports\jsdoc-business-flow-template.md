# JavaScript业务流程注释标准模板

## 📋 统一JSDoc注释格式标准

### 🏷️ 核心标签定义

#### @businessFlow - 业务流程描述
描述当前文件在整个业务流程中的位置和作用

#### @architecture - 架构层级说明
说明文件在母子两层架构中的层级（Mother Layer / Child Layer）

#### @dependencies - 依赖关系
列出文件的上游依赖和下游被依赖关系

#### @localProcessing - 本地处理职责
说明文件负责的本地处理逻辑（不调用Gemini API）

#### @remoteProcessing - 远程处理职责
说明文件负责的远程处理逻辑（调用Gemini API）

#### @compatibility - 兼容性保证
说明向后兼容性的保证措施

#### @refactoringConstraints - 重构约束
说明重构过程中需要遵守的约束条件

## 📝 标准注释模板

### 母层控制器文件模板
```javascript
/**
 * @fileoverview 核心业务流程控制器 - 母层架构
 * @description 统一管理整个订单处理业务流程，协调各个子层模块
 * 
 * @businessFlow 核心业务流程控制
 * 输入处理流程：
 * 1. 接收输入内容（文字/图片）
 * 2. 调用本地渠道特征检测
 * 3. 组合渠道专属提示词
 * 4. 调用Gemini API进行智能解析
 * 5. 处理解析结果（单订单/多订单分支）
 * 6. 映射到表单并调用GoMyHire API
 * 7. 保存到本地历史订单
 * 
 * @architecture Mother Layer (母层)
 * - 职责：业务流程的统一控制和协调
 * - 原则：不包含具体实现逻辑，只负责调用和协调子层
 * - 接口：提供统一的对外API接口
 * 
 * @dependencies
 * 上游依赖：无（作为顶层控制器）
 * 下游依赖：
 * - flow/channel-detector.js (渠道检测子层)
 * - flow/prompt-builder.js (提示词构建子层)
 * - flow/gemini-caller.js (Gemini API调用子层)
 * - flow/result-processor.js (结果处理子层)
 * 
 * @localProcessing 本地处理职责
 * - 业务流程控制和协调
 * - 子层模块的调用管理
 * - 错误处理和异常管理
 * - 状态管理和进度跟踪
 * 
 * @remoteProcessing 远程处理职责
 * - 通过子层调用Gemini API
 * - 不直接进行远程调用
 * 
 * @compatibility 兼容性保证
 * - 保持现有window.OTA.geminiService接口
 * - 保持现有parseOrder()、analyzeImage()方法签名
 * - 提供向后兼容的适配器
 * 
 * @refactoringConstraints 重构约束
 * - 必须保持现有渠道策略文件不变
 * - 严格遵循母子两层架构原则
 * - 子层不能反向依赖母层
 * - 保持单向依赖关系
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */
```

### 子层实现文件模板
```javascript
/**
 * @fileoverview 渠道检测器 - 子层实现
 * @description 负责本地渠道特征检测，不调用Gemini API
 * 
 * @businessFlow 本地渠道特征检测
 * 处理流程：
 * 1. 接收输入文本/图片内容
 * 2. 基于正则表达式检测Fliggy特征（订单编号+19位数字）
 * 3. 基于关键词检测JingGe特征
 * 4. 基于参考号模式检测其他渠道
 * 5. 返回检测结果（渠道名称+置信度）
 * 
 * @architecture Child Layer (子层)
 * - 职责：渠道检测的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供检测服务
 * 
 * @dependencies
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器)
 * 下游依赖：无（底层实现）
 * 
 * @localProcessing 本地处理职责
 * - Fliggy渠道特征检测：/订单编号[：:\s]*\d{19}/
 * - JingGe渠道特征检测：/jingge|jinggeshop|精格|精格商铺/i
 * - 参考号模式检测：/^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i
 * - 置信度计算和结果封装
 * 
 * @remoteProcessing 远程处理职责
 * - 无（纯本地处理）
 * 
 * @compatibility 兼容性保证
 * - 保持现有检测逻辑不变
 * - 兼容现有的检测结果格式
 * 
 * @refactoringConstraints 重构约束
 * - 不能调用Gemini API
 * - 不能依赖其他子层
 * - 必须保持检测准确率
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */
```

### 现有核心文件注释模板
```javascript
/**
 * @fileoverview Gemini AI服务 - 待重构的超大文件
 * @description 包含AI服务的完整实现，计划拆分为母子两层架构
 * 
 * @businessFlow 当前业务流程（待重构）
 * 当前包含的功能：
 * 1. Gemini API调用和错误处理
 * 2. 订单文本解析和结构化
 * 3. 图像分析和OCR处理
 * 4. 多订单检测和分割
 * 5. 知识库管理和数据缓存
 * 
 * @architecture 待重构 - 当前为单体结构
 * 重构计划：
 * - 拆分为1个母层控制器 + 4个子层实现
 * - 母层：business-flow-controller.js
 * - 子层：gemini-caller.js, order-parser.js, image-analyzer.js, knowledge-base.js
 * 
 * @dependencies 当前依赖关系（复杂，待简化）
 * 上游依赖：多个文件直接调用
 * 下游依赖：混合了多种功能
 * 
 * @localProcessing 本地处理职责（待分离）
 * - 配置管理和参数验证
 * - 结果缓存和数据管理
 * - 错误处理和状态管理
 * 
 * @remoteProcessing 远程处理职责（待分离）
 * - Gemini API调用
 * - 文本和图像分析
 * - 多订单检测和分割
 * 
 * @compatibility 兼容性保证
 * - 重构期间保持现有API不变
 * - 提供适配器确保向后兼容
 * - 逐步迁移到新架构
 * 
 * @refactoringConstraints 重构约束
 * - 文件过大（4760行），必须拆分
 * - 职责混乱，需要清晰分离
 * - 保持功能完整性
 * 
 * @refactoringPlan 重构计划
 * 阶段1：创建母层控制器，保持现有接口
 * 阶段2：逐步迁移功能到子层
 * 阶段3：删除重复代码，优化结构
 * 阶段4：测试验证，确保兼容性
 * 
 * <AUTHOR>
 * @version 1.0.0 (待升级到2.0.0)
 * @since 2024-XX-XX
 * @lastModified 2025-08-09
 * @refactoringStatus 计划中
 */
```

### 渠道策略文件注释模板
```javascript
/**
 * @fileoverview Fliggy渠道策略 - 保持不变的策略文件
 * @description 提供Fliggy渠道专属的字段级提示词片段，纯静态工具类
 * 
 * @businessFlow 渠道策略提供
 * 在业务流程中的作用：
 * 1. 当检测到Fliggy渠道时被调用
 * 2. 提供字段级提示词片段
 * 3. 提供价格计算规则
 * 4. 提供车型ID映射
 * 
 * @architecture 独立策略层
 * - 职责：提供渠道专属配置和规则
 * - 原则：纯静态，无状态，无副作用
 * - 接口：静态方法，不依赖实例
 * 
 * @dependencies
 * 上游依赖：
 * - flow/prompt-builder.js (调用getFieldPromptSnippets)
 * - 价格计算模块 (调用价格规则)
 * 下游依赖：无
 * 
 * @localProcessing 本地处理职责
 * - 提供字段级提示词片段
 * - 提供价格计算规则（商家实收×0.84×0.615）
 * - 提供车型ID映射（经济型/舒适型/五座→5）
 * 
 * @remoteProcessing 远程处理职责
 * - 无（纯配置文件）
 * 
 * @compatibility 兼容性保证
 * - 接口完全保持不变
 * - getFieldPromptSnippets()方法签名不变
 * - 返回数据格式不变
 * 
 * @refactoringConstraints 重构约束
 * - 严禁修改此文件的接口和实现
 * - 必须保持现有的策略逻辑
 * - 不能改变价格计算公式
 * - 不能改变车型ID映射
 * 
 * <AUTHOR>
 * @version 1.0.0 (稳定版本，不升级)
 * @since 2024-XX-XX
 * @lastModified 2025-08-09
 * @refactoringStatus 保持不变
 */
```

## 🎯 注释添加优先级

### 第一优先级：核心业务流程文件
1. gemini-service.js - 超大文件，重构重点
2. multi-order-manager-v2.js - 复杂管理器
3. ota-channel-detector.js - 渠道检测核心

### 第二优先级：策略和配置文件
1. fliggy-ota-strategy.js - 保持不变的策略
2. jingge-ota-strategy.js - 保持不变的策略
3. prompt-templates.js - 提示词模板

### 第三优先级：支持和工具文件
1. app-state.js - 状态管理
2. logger.js - 日志服务
3. utils.js - 工具函数

## ✅ 注释质量检查标准

### 必须包含的内容
- [ ] 完整的业务流程描述
- [ ] 明确的架构层级说明
- [ ] 清晰的依赖关系
- [ ] 本地vs远程处理分工
- [ ] 兼容性保证措施
- [ ] 重构约束说明

### 格式要求
- [ ] 使用标准JSDoc格式
- [ ] 包含所有必需标签
- [ ] 中英文混合，关键术语使用英文
- [ ] 代码示例使用正确语法

### 维护要求
- [ ] 版本号管理
- [ ] 修改时间记录
- [ ] 作者信息完整
- [ ] 重构状态标记

---

*JSDoc标准模板版本: 1.0*
*适用范围: 所有JavaScript文件*
*更新时间: 2025-08-09*
