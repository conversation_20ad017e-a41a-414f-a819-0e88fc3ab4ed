/**
 * 简单路由系统
 * 文件: js/pages/router.js
 * 角色: 页面路由管理器，支持hash路由和页面切换
 * 
 * @ROUTER 页面路由系统
 * 🏷️ 标签: @OTA_PAGE_ROUTER
 * 📝 说明: 负责页面路由管理和导航控制
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 简单路由器类
     * 支持hash路由和页面切换
     */
    class SimpleRouter {
        constructor() {
            this.routes = new Map();
            this.currentRoute = null;
            this.routeData = null;
            this.logger = this.getLogger();
            
            // 路由配置
            this.config = {
                defaultRoute: '/',
                enableLogging: true,
                enableTransitions: true
            };

            // 初始化路由系统
            this.init();
            
            this.logger.log('🚀 路由系统已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 初始化路由系统
         */
        init() {
            // 监听hash变化
            window.addEventListener('hashchange', () => {
                this.handleRouteChange();
            });

            // 监听页面加载
            window.addEventListener('load', () => {
                this.handleRouteChange();
            });

            // 监听前进后退
            window.addEventListener('popstate', () => {
                this.handleRouteChange();
            });
        }

        /**
         * 添加路由
         * @param {string} path - 路由路径
         * @param {function} handler - 路由处理函数
         * @param {Object} options - 路由选项
         */
        addRoute(path, handler, options = {}) {
            if (typeof handler !== 'function') {
                throw new Error(`路由处理器必须是函数: ${path}`);
            }

            this.routes.set(path, {
                handler,
                options: {
                    title: options.title || path,
                    requiresAuth: options.requiresAuth || false,
                    beforeEnter: options.beforeEnter || null,
                    afterEnter: options.afterEnter || null,
                    ...options
                }
            });

            this.logger.log(`📍 路由已注册: ${path}`, 'info');
        }

        /**
         * 导航到指定路由
         * @param {string} path - 目标路径
         * @param {Object} data - 传递的数据
         * @param {Object} options - 导航选项
         */
        navigate(path, data = null, options = {}) {
            try {
                // 保存路由数据
                if (data) {
                    this.setRouteData(data);
                }

                // 更新URL
                if (options.replace) {
                    window.location.replace(`#${path}`);
                } else {
                    window.location.hash = path;
                }

                this.logger.log(`🧭 导航到: ${path}`, 'info');
            } catch (error) {
                this.logger.logError(`导航失败: ${path}`, error);
                throw error;
            }
        }

        /**
         * 处理路由变化
         */
        async handleRouteChange() {
            try {
                const hash = window.location.hash.slice(1) || this.config.defaultRoute;
                const route = this.routes.get(hash);

                if (!route) {
                    this.handleNotFound(hash);
                    return;
                }

                // 执行路由前置钩子
                if (route.options.beforeEnter) {
                    const canEnter = await route.options.beforeEnter(hash, this.routeData);
                    if (!canEnter) {
                        this.logger.log(`🚫 路由被前置钩子阻止: ${hash}`, 'warning');
                        return;
                    }
                }

                // 执行路由处理器
                await this.executeRoute(hash, route);

                // 执行路由后置钩子
                if (route.options.afterEnter) {
                    await route.options.afterEnter(hash, this.routeData);
                }

                // 更新当前路由
                this.currentRoute = hash;

                this.logger.log(`✅ 路由切换完成: ${hash}`, 'success');

            } catch (error) {
                this.logger.logError('路由处理失败', error);
                this.handleRouteError(error);
            }
        }

        /**
         * 执行路由处理器
         * @param {string} path - 路由路径
         * @param {Object} route - 路由配置
         */
        async executeRoute(path, route) {
            try {
                // 更新页面标题
                if (route.options.title) {
                    document.title = route.options.title;
                }

                // 执行路由处理器
                await route.handler(this.routeData);

            } catch (error) {
                this.logger.logError(`路由执行失败: ${path}`, error);
                throw error;
            }
        }

        /**
         * 处理404情况
         * @param {string} path - 未找到的路径
         */
        handleNotFound(path) {
            this.logger.log(`❌ 路由未找到: ${path}`, 'warning');
            
            // 尝试导航到默认路由
            if (path !== this.config.defaultRoute) {
                this.navigate(this.config.defaultRoute);
            } else {
                console.error('默认路由也未找到，请检查路由配置');
            }
        }

        /**
         * 处理路由错误
         * @param {Error} error - 错误对象
         */
        handleRouteError(error) {
            this.logger.logError('路由系统错误', error);
            
            // 可以在这里添加错误页面显示逻辑
            console.error('路由系统发生错误:', error);
        }

        /**
         * 设置路由数据
         * @param {Object} data - 路由数据
         */
        setRouteData(data) {
            this.routeData = data;
            
            // 可选：将数据保存到sessionStorage
            if (data && typeof data === 'object') {
                try {
                    sessionStorage.setItem('routeData', JSON.stringify(data));
                } catch (error) {
                    this.logger.logError('保存路由数据失败', error);
                }
            }
        }

        /**
         * 获取当前路由数据
         * @returns {Object|null} 路由数据
         */
        getCurrentRouteData() {
            if (this.routeData) {
                return this.routeData;
            }

            // 尝试从sessionStorage恢复
            try {
                const data = sessionStorage.getItem('routeData');
                return data ? JSON.parse(data) : null;
            } catch (error) {
                this.logger.logError('恢复路由数据失败', error);
                return null;
            }
        }

        /**
         * 清除路由数据
         */
        clearRouteData() {
            this.routeData = null;
            try {
                sessionStorage.removeItem('routeData');
            } catch (error) {
                this.logger.logError('清除路由数据失败', error);
            }
        }

        /**
         * 获取当前路由
         * @returns {string} 当前路由路径
         */
        getCurrentRoute() {
            return this.currentRoute || window.location.hash.slice(1) || this.config.defaultRoute;
        }

        /**
         * 检查是否为指定路由
         * @param {string} path - 路由路径
         * @returns {boolean} 是否匹配
         */
        isCurrentRoute(path) {
            return this.getCurrentRoute() === path;
        }

        /**
         * 返回上一页
         */
        goBack() {
            window.history.back();
        }

        /**
         * 前进到下一页
         */
        goForward() {
            window.history.forward();
        }

        /**
         * 获取所有注册的路由
         * @returns {Array} 路由列表
         */
        getRoutes() {
            return Array.from(this.routes.keys());
        }

        /**
         * 销毁路由器
         */
        destroy() {
            // 移除事件监听器
            window.removeEventListener('hashchange', this.handleRouteChange);
            window.removeEventListener('load', this.handleRouteChange);
            window.removeEventListener('popstate', this.handleRouteChange);
            
            // 清理数据
            this.routes.clear();
            this.clearRouteData();
            
            this.logger.log('🗑️ 路由系统已销毁', 'info');
        }
    }

    // 创建全局路由器实例
    const router = new SimpleRouter();

    // 暴露到OTA命名空间
    window.OTA.SimpleRouter = SimpleRouter;
    window.OTA.router = router;

    // 向后兼容
    window.router = router;

    console.log('✅ 简单路由系统已加载');

})();
