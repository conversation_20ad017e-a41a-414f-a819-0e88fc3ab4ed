/**
 * BaseManager适配器 - 零破坏性BaseManager兼容方案
 * 
 * 设计目标：
 * - 为OTAManager提供BaseManager功能，无需修改原有代码
 * - 通过适配器模式实现完全兼容
 * - 遵循现有Manager模式的接口规范
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.adapters = window.OTA.adapters || {};

(function() {
    'use strict';

    /**
     * BaseManager适配器类
     * 提供BaseManager的所有基础功能
     */
    class BaseManagerAdapter {
        constructor(name) {
            this.name = name || 'UnknownManager';
            this.logger = null;
            this.initialized = false;
            this.eventListeners = new Map();
            
            // 延迟初始化logger，避免循环依赖
            this.initLogger();
        }

        /**
         * 初始化日志器
         * 使用与现有Manager一致的日志获取方式
         */
        initLogger() {
            try {
                // 优先使用全局getLogger函数（与FormManager、EventManager一致）
                if (typeof getLogger === 'function') {
                    this.logger = getLogger();
                } else if (window.OTA && window.OTA.container) {
                    // 备选：使用依赖容器获取
                    this.logger = window.OTA.container.get('logger');
                } else {
                    // 降级：使用控制台
                    this.logger = this.createFallbackLogger();
                }
            } catch (error) {
                this.logger = this.createFallbackLogger();
            }
        }

        /**
         * 创建降级日志器
         * @returns {Object} 降级日志器
         */
        createFallbackLogger() {
            return {
                log: (message, level = 'info', data = null) => {
                    const prefix = `[${this.name}][${level.toUpperCase()}]`;
                    if (data) {
                        console.log(prefix, message, data);
                    } else {
                        console.log(prefix, message);
                    }
                }
            };
        }

        /**
         * 日志记录方法 - 与OTAManager中的this.log()完全兼容
         * 支持OTAManager的调用方式：this.log('message')
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别
         * @param {any} data - 附加数据
         */
        log(message, level = 'info', data = null) {
            try {
                if (this.logger && typeof this.logger.log === 'function') {
                    this.logger.log(`[${this.name}] ${message}`, level, data);
                } else {
                    // 确保即使logger失败也能输出日志
                    console.log(`[${this.name}][${level.toUpperCase()}] ${message}`, data || '');
                }
            } catch (error) {
                // 最终降级方案
                console.log(`[${this.name}] ${message}`, data || '');
            }
        }

        /**
         * 错误日志记录 - 与OTAManager中的this.error()完全兼容
         * 支持OTAManager的调用方式：this.error('message:', errorObject)
         * @param {string} message - 错误消息
         * @param {Error|any} error - 错误对象
         */
        error(message, error = null) {
            try {
                let errorData = null;

                if (error) {
                    if (error instanceof Error) {
                        errorData = {
                            message: error.message,
                            stack: error.stack,
                            name: error.name
                        };
                    } else {
                        errorData = error;
                    }
                }

                this.log(message, 'error', errorData);

                // 同时输出到console.error以确保错误可见
                if (error instanceof Error) {
                    console.error(`[${this.name}] ${message}`, error);
                } else {
                    console.error(`[${this.name}] ${message}`, error || '');
                }
            } catch (logError) {
                // 最终降级方案
                console.error(`[${this.name}] ${message}`, error || '');
            }
        }

        /**
         * 警告日志记录
         * @param {string} message - 警告消息
         * @param {any} data - 附加数据
         */
        warn(message, data = null) {
            this.log(message, 'warn', data);
        }

        /**
         * 信息日志记录
         * @param {string} message - 信息消息
         * @param {any} data - 附加数据
         */
        info(message, data = null) {
            this.log(message, 'info', data);
        }

        /**
         * 调试日志记录
         * @param {string} message - 调试消息
         * @param {any} data - 附加数据
         */
        debug(message, data = null) {
            this.log(message, 'debug', data);
        }

        /**
         * 事件发射方法 - 与OTAManager中的this.emit()完全兼容
         * 支持OTAManager的调用方式：this.emit('event-name', { data })
         * @param {string} eventName - 事件名称
         * @param {any} data - 事件数据
         */
        emit(eventName, data = {}) {
            try {
                // 构建事件数据
                const eventData = {
                    ...(typeof data === 'object' && data !== null ? data : { value: data }),
                    source: this.name,
                    timestamp: Date.now()
                };

                // 优先使用全局事件协调器
                if (window.OTA && window.OTA.eventCoordinator && typeof window.OTA.eventCoordinator.emit === 'function') {
                    window.OTA.eventCoordinator.emit(eventName, eventData);
                    this.debug(`Event emitted via OTA.eventCoordinator: ${eventName}`);
                } else if (window.globalEventCoordinator && typeof window.globalEventCoordinator.emit === 'function') {
                    window.globalEventCoordinator.emit(eventName, eventData);
                    this.debug(`Event emitted via globalEventCoordinator: ${eventName}`);
                } else {
                    // 降级：触发本地监听器
                    this.triggerLocalListeners(eventName, eventData);
                    this.debug(`Event emitted via local listeners: ${eventName}`);
                }

                // 记录事件发射（调试用）
                this.debug(`Event emitted: ${eventName}`, eventData);

            } catch (error) {
                this.error('Failed to emit event', error);
                // 即使发射失败，也不应该影响主流程
            }
        }

        /**
         * 触发本地事件监听器
         * @param {string} eventName - 事件名称
         * @param {any} data - 事件数据
         */
        triggerLocalListeners(eventName, data) {
            const listeners = this.eventListeners.get(eventName) || [];
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    this.error(`Event listener error for ${eventName}`, error);
                }
            });
        }

        /**
         * 添加事件监听器
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 监听器函数
         */
        on(eventName, listener) {
            if (!this.eventListeners.has(eventName)) {
                this.eventListeners.set(eventName, []);
            }
            this.eventListeners.get(eventName).push(listener);
        }

        /**
         * 移除事件监听器
         * @param {string} eventName - 事件名称
         * @param {Function} listener - 监听器函数
         */
        off(eventName, listener) {
            const listeners = this.eventListeners.get(eventName) || [];
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }

        /**
         * 获取管理器名称
         * @returns {string} 管理器名称
         */
        getName() {
            return this.name;
        }

        /**
         * 检查是否已初始化
         * @returns {boolean} 初始化状态
         */
        isInitialized() {
            return this.initialized;
        }

        /**
         * 设置初始化状态
         * @param {boolean} status - 初始化状态
         */
        setInitialized(status = true) {
            this.initialized = status;
            if (status) {
                this.log('Manager initialized successfully');
            }
        }

        /**
         * 销毁适配器
         */
        destroy() {
            this.eventListeners.clear();
            this.initialized = false;
            this.log('BaseManagerAdapter destroyed');
        }
    }

    // 暴露到OTA命名空间
    window.OTA.adapters.BaseManagerAdapter = BaseManagerAdapter;

    // 提供便捷的创建函数
    window.OTA.createBaseManagerAdapter = function(name) {
        return new BaseManagerAdapter(name);
    };

    // 为了让OTAManager能够继承，将BaseManagerAdapter作为全局BaseManager暴露
    window.BaseManager = BaseManagerAdapter;

    console.log('✅ BaseManager适配器已加载，BaseManager全局类已可用');

})();
