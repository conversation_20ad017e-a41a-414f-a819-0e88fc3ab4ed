# 页面系统架构

## 概述

这是OTA订单处理系统的新页面架构，采用独立分页模式替代原有的浮窗模式。

## 目录结构

```
js/pages/
├── README.md                    # 本文档
├── router.js                    # 路由系统
├── page-manager.js             # 页面管理器
└── multi-order/                # 多订单页面模块
    ├── multi-order-page.js     # 页面控制器
    ├── components/             # UI组件 (待创建)
    │   ├── order-card.js       # 订单卡片组件
    │   ├── batch-controls.js   # 批量操作控件
    │   ├── progress-indicator.js # 进度指示器
    │   └── status-panel.js     # 状态面板
    ├── services/               # 业务服务 (待创建)
    │   ├── order-detector.js   # 订单检测服务
    │   ├── order-processor.js  # 订单处理服务
    │   ├── batch-manager.js    # 批量管理服务
    │   ├── state-manager.js    # 状态管理服务
    │   └── api-client.js       # API客户端
    ├── utils/                  # 工具函数 (待创建)
    │   ├── order-validator.js  # 订单验证
    │   └── data-transformer.js # 数据转换
    └── styles/                 # 样式文件 (待创建)
        ├── multi-order-page.css # 页面主样式
        ├── components.css      # 组件样式
        └── responsive.css      # 响应式样式
```

## 核心组件

### 1. 路由系统 (router.js)

**功能：**
- 支持hash路由 (#/ 和 #/multi-order)
- 路由数据传递和管理
- 路由前置和后置钩子
- 页面导航和历史管理

**主要API：**
```javascript
// 添加路由
router.addRoute(path, handler, options);

// 导航到路由
router.navigate(path, data, options);

// 获取当前路由数据
router.getCurrentRouteData();
```

### 2. 页面管理器 (page-manager.js)

**功能：**
- 管理主页面和多订单页面的显示/隐藏
- 页面切换动画和过渡效果
- 页面状态管理和历史记录
- 页面生命周期管理

**主要API：**
```javascript
// 显示主页面
pageManager.showMainPage();

// 显示多订单页面
pageManager.showMultiOrderPage(data);

// 获取当前页面
pageManager.getCurrentPage();
```

### 3. 多订单页面控制器 (multi-order-page.js)

**功能：**
- 多订单页面的主控制器
- 组件和服务的协调管理
- 页面生命周期管理
- 事件处理和状态管理

**主要API：**
```javascript
// 初始化页面
multiOrderPage.initialize(data);

// 处理批量创建
multiOrderPage.handleBatchCreate();

// 销毁页面
multiOrderPage.destroy();
```

## 路由配置

### 支持的路由

1. **主页面路由：** `#/` 或 `#/main`
   - 显示主要的订单处理界面
   - 包含订单输入、表单填写等功能

2. **多订单页面路由：** `#/multi-order`
   - 显示多订单管理界面
   - 包含订单预览、批量操作等功能

### 路由数据传递

```javascript
// 跳转到多订单页面并传递数据
router.navigate('/multi-order', {
    orders: parsedOrders,
    originalText: inputText,
    confidence: 0.95
});

// 在多订单页面获取数据
const data = router.getCurrentRouteData();
```

## 数据流

### 1. 多订单检测流程

```
用户输入 → Gemini API → 检测到多订单 → 自动跳转到 #/multi-order
```

### 2. 多订单处理流程

```
页面初始化 → 渲染订单预览 → 用户选择 → 批量处理 → 显示结果 → 自动返回主页
```

### 3. 页面切换流程

```
路由变化 → 页面管理器 → 隐藏当前页面 → 显示目标页面 → 更新状态
```

## 与原系统的集成

### 触发点集成

在 `realtime-analysis-manager.js` 中：
```javascript
if (parseResult.length > 1) {
    // 检测到多订单，自动跳转
    window.OTA.router.navigate('/multi-order', {
        orders: parseResult,
        originalText: orderText,
        confidence: this.calculateAverageConfidence(parseResult)
    });
}
```

### API集成

复用现有的API服务：
- `api-service.js` - GoMyHire API调用
- `gemini-service.js` - AI分析服务
- `order-history-manager.js` - 历史记录管理

### 样式集成

复用现有的样式系统：
- CSS变量和主题支持
- 响应式设计
- 暗色模式支持

## 开发计划

### 已完成
- [x] 路由系统基础框架
- [x] 页面管理器基础框架
- [x] 多订单页面控制器基础框架

### 待完成
- [ ] UI组件开发
- [ ] 业务服务迁移
- [ ] 样式系统创建
- [ ] 工具函数开发
- [ ] 集成测试
- [ ] 原系统清理

## 使用示例

### 基本使用

```javascript
// 初始化路由和页面管理器
const router = window.OTA.router;
const pageManager = window.OTA.pageManager;

// 初始化页面管理器
pageManager.init(router);

// 导航到多订单页面
router.navigate('/multi-order', {
    orders: [order1, order2, order3],
    originalText: 'original input text'
});
```

### 自定义路由

```javascript
// 添加自定义路由
router.addRoute('/custom-page', (data) => {
    // 自定义页面处理逻辑
    console.log('Custom page data:', data);
}, {
    title: '自定义页面',
    beforeEnter: (path, data) => {
        // 路由前置检查
        return data && data.isValid;
    }
});
```

## 注意事项

1. **向后兼容性：** 新系统与原有多订单系统并存，确保平滑过渡
2. **性能优化：** 页面切换使用CSS过渡，避免重复渲染
3. **错误处理：** 完善的错误处理和降级方案
4. **状态管理：** 页面级状态管理，避免全局状态污染
5. **内存管理：** 页面销毁时正确清理资源

## 扩展性

系统设计支持：
- 添加新的页面路由
- 扩展组件系统
- 自定义页面切换动画
- 集成第三方路由库
- 支持嵌套路由（未来版本）
