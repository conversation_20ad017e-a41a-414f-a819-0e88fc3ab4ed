/**
 * 订单卡片组件
 * 文件: js/pages/multi-order/components/order-card.js
 * 角色: 订单卡片UI组件，显示订单信息和选择状态
 * 
 * @ORDER_CARD 订单卡片组件
 * 🏷️ 标签: @OTA_ORDER_CARD_COMPONENT
 * 📝 说明: 模块化的订单卡片组件，支持选择和状态显示
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 订单卡片组件类
     * 继承自BaseComponent
     */
    class OrderCard extends window.OTA.Components.BaseComponent {
        constructor(options = {}) {
            super({
                container: options.container,
                autoRender: false,
                enableEvents: true,
                ...options
            });

            // 组件特定配置
            this.config = {
                showCheckbox: options.showCheckbox !== false,
                showStatus: options.showStatus !== false,
                enableSelection: options.enableSelection !== false,
                enableHover: options.enableHover !== false,
                ...options.config
            };

            // 订单数据
            this.orderData = options.orderData || {};
            this.orderIndex = options.orderIndex || 0;
            this.isSelected = options.isSelected || false;
            this.status = options.status || 'parsed';

            // 事件回调
            this.onSelectionChange = options.onSelectionChange || null;
            this.onCardClick = options.onCardClick || null;
        }

        /**
         * 初始化状态
         */
        initializeState() {
            this.state.data = {
                order: this.orderData,
                index: this.orderIndex,
                selected: this.isSelected,
                status: this.status
            };
        }

        /**
         * 渲染组件
         */
        render() {
            if (!this.elements.container) {
                throw new Error('订单卡片组件需要容器元素');
            }

            // 生成卡片HTML
            const cardHTML = this.generateCardHTML();
            
            // 创建卡片元素
            const cardElement = document.createElement('div');
            cardElement.innerHTML = cardHTML;
            this.elements.root = cardElement.firstElementChild;

            // 添加到容器
            this.elements.container.appendChild(this.elements.root);

            // 绑定事件
            this.bindCardEvents();

            // 标记为已渲染
            this.state.isRendered = true;

            this.logger.log(`📋 订单卡片 ${this.orderIndex + 1} 已渲染`, 'info');
        }

        /**
         * 生成卡片HTML
         * @returns {string} HTML字符串
         */
        generateCardHTML() {
            const order = this.state.data.order;
            const index = this.state.data.index;
            const selected = this.state.data.selected;
            const status = this.state.data.status;

            const orderId = `order-card-${this.componentId}`;
            const checkboxId = `order-checkbox-${this.componentId}`;

            return `
                <div class="order-card ${selected ? 'selected' : ''} ${this.config.enableHover ? 'hoverable' : ''}" 
                     data-order-index="${index}" 
                     data-component-id="${this.componentId}"
                     id="${orderId}">
                    <div class="order-card-header">
                        <div class="order-selector">
                            ${this.config.showCheckbox ? `
                                <input type="checkbox" 
                                       id="${checkboxId}" 
                                       class="order-checkbox" 
                                       ${selected ? 'checked' : ''}
                                       ${this.config.enableSelection ? '' : 'disabled'}>
                            ` : ''}
                            <div class="order-title">
                                <span class="order-number">订单 ${index + 1}</span>
                                ${order.customer_name ? `
                                    <span class="customer-name">${order.customer_name}</span>
                                ` : ''}
                            </div>
                        </div>
                        ${this.config.showStatus ? `
                            <div class="order-status">
                                <span class="status-badge status-${status}">${this.getStatusText(status)}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="order-card-body">
                        ${this.generateOrderSummary(order)}
                    </div>
                </div>
            `;
        }

        /**
         * 生成订单摘要
         * @param {Object} order - 订单数据
         * @returns {string} 摘要HTML
         */
        generateOrderSummary(order) {
            const fields = [
                {
                    label: '客户',
                    value: order.customer_name || '未知客户',
                    show: true
                },
                {
                    label: '电话',
                    value: order.customer_phone || '未提供',
                    show: !!order.customer_phone
                },
                {
                    label: '路线',
                    value: `${order.pickup_location || '未知'} → ${order.dropoff_location || '未知'}`,
                    show: !!(order.pickup_location || order.dropoff_location)
                },
                {
                    label: '时间',
                    value: order.pickup_time || '未指定',
                    show: !!order.pickup_time
                },
                {
                    label: '价格',
                    value: order.total_price ? `¥${order.total_price}` : '未指定',
                    show: !!order.total_price
                },
                {
                    label: '备注',
                    value: order.notes || '',
                    show: !!order.notes
                }
            ];

            const summaryRows = fields
                .filter(field => field.show)
                .map(field => `
                    <div class="summary-row">
                        <span class="summary-label">${field.label}:</span>
                        <span class="summary-value">${field.value}</span>
                    </div>
                `).join('');

            return `
                <div class="order-summary">
                    ${summaryRows}
                </div>
            `;
        }

        /**
         * 获取状态文本
         * @param {string} status - 状态
         * @returns {string} 状态文本
         */
        getStatusText(status) {
            const statusMap = {
                'parsed': '已解析',
                'processing': '处理中',
                'completed': '已完成',
                'failed': '失败',
                'waiting': '等待中'
            };
            return statusMap[status] || status;
        }

        /**
         * 绑定卡片事件
         */
        bindCardEvents() {
            if (!this.elements.root) return;

            // 绑定复选框事件
            const checkbox = this.elements.root.querySelector('.order-checkbox');
            if (checkbox && this.config.enableSelection) {
                this.addEventListener(checkbox, 'change', (e) => {
                    e.stopPropagation();
                    this.handleSelectionChange(checkbox.checked);
                });
            }

            // 绑定卡片点击事件
            if (this.config.enableSelection) {
                this.addEventListener(this.elements.root, 'click', (e) => {
                    // 如果点击的不是复选框，切换选择状态
                    if (e.target.type !== 'checkbox') {
                        const newSelected = !this.state.data.selected;
                        this.setSelected(newSelected);
                        
                        if (checkbox) {
                            checkbox.checked = newSelected;
                        }
                    }
                    
                    // 调用卡片点击回调
                    if (this.onCardClick) {
                        this.onCardClick(this.state.data.index, this.state.data.order);
                    }
                });
            }

            // 绑定悬停效果
            if (this.config.enableHover) {
                this.addEventListener(this.elements.root, 'mouseenter', () => {
                    this.elements.root.classList.add('hover');
                });

                this.addEventListener(this.elements.root, 'mouseleave', () => {
                    this.elements.root.classList.remove('hover');
                });
            }
        }

        /**
         * 处理选择状态变化
         * @param {boolean} selected - 是否选中
         */
        handleSelectionChange(selected) {
            this.setSelected(selected);
            
            if (this.onSelectionChange) {
                this.onSelectionChange(this.state.data.index, selected, this.state.data.order);
            }
        }

        /**
         * 设置选中状态
         * @param {boolean} selected - 是否选中
         */
        setSelected(selected) {
            this.state.data.selected = selected;
            
            if (this.elements.root) {
                this.elements.root.classList.toggle('selected', selected);
                
                const checkbox = this.elements.root.querySelector('.order-checkbox');
                if (checkbox) {
                    checkbox.checked = selected;
                }
            }
        }

        /**
         * 获取选中状态
         * @returns {boolean} 是否选中
         */
        isSelected() {
            return this.state.data.selected;
        }

        /**
         * 设置状态
         * @param {string} status - 新状态
         */
        setStatus(status) {
            this.state.data.status = status;
            
            if (this.elements.root) {
                const statusBadge = this.elements.root.querySelector('.status-badge');
                if (statusBadge) {
                    // 移除旧状态类
                    statusBadge.className = statusBadge.className.replace(/status-\w+/g, '');
                    // 添加新状态类
                    statusBadge.classList.add(`status-${status}`);
                    statusBadge.textContent = this.getStatusText(status);
                }
            }
        }

        /**
         * 获取状态
         * @returns {string} 当前状态
         */
        getStatus() {
            return this.state.data.status;
        }

        /**
         * 更新订单数据
         * @param {Object} newOrderData - 新的订单数据
         */
        updateOrderData(newOrderData) {
            this.state.data.order = { ...newOrderData };
            
            // 重新渲染卡片内容
            if (this.elements.root) {
                const cardBody = this.elements.root.querySelector('.order-card-body');
                if (cardBody) {
                    cardBody.innerHTML = this.generateOrderSummary(newOrderData);
                }
                
                // 更新客户名称
                const customerNameElement = this.elements.root.querySelector('.customer-name');
                if (customerNameElement && newOrderData.customer_name) {
                    customerNameElement.textContent = newOrderData.customer_name;
                }
            }
        }

        /**
         * 获取订单数据
         * @returns {Object} 订单数据
         */
        getOrderData() {
            return { ...this.state.data.order };
        }

        /**
         * 获取订单索引
         * @returns {number} 订单索引
         */
        getOrderIndex() {
            return this.state.data.index;
        }

        /**
         * 启用组件
         */
        onEnable() {
            if (this.elements.root) {
                const checkbox = this.elements.root.querySelector('.order-checkbox');
                if (checkbox) {
                    checkbox.disabled = false;
                }
            }
        }

        /**
         * 禁用组件
         */
        onDisable() {
            if (this.elements.root) {
                const checkbox = this.elements.root.querySelector('.order-checkbox');
                if (checkbox) {
                    checkbox.disabled = true;
                }
            }
        }

        /**
         * 销毁回调
         */
        onDestroy() {
            // 清理订单数据
            this.orderData = null;
            this.onSelectionChange = null;
            this.onCardClick = null;
        }
    }

    // 注册组件到组件注册中心
    if (window.OTA?.Components?.registry) {
        window.OTA.Components.registry.register('OrderCard', OrderCard, {
            singleton: false,
            autoCreate: false
        });
    }

    // 暴露到OTA命名空间
    window.OTA.Components.OrderCard = OrderCard;

    console.log('✅ 订单卡片组件已加载');

})();
