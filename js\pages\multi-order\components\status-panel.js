/**
 * 状态面板组件
 * 文件: js/pages/multi-order/components/status-panel.js
 * 角色: 状态面板UI组件，显示处理状态、错误信息和详细结果报告
 * 
 * @STATUS_PANEL 状态面板组件
 * 🏷️ 标签: @OTA_STATUS_PANEL_COMPONENT
 * 📝 说明: 模块化的状态面板，支持结果展示和错误信息显示
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 状态面板组件类
     * 继承自BaseComponent
     */
    class StatusPanel extends window.OTA.Components.BaseComponent {
        constructor(options = {}) {
            super({
                container: options.container,
                autoRender: false,
                enableEvents: true,
                ...options
            });

            // 组件特定配置
            this.config = {
                showSummary: options.showSummary !== false,
                showSuccessOrders: options.showSuccessOrders !== false,
                showFailedOrders: options.showFailedOrders !== false,
                showActions: options.showActions !== false,
                enableExport: options.enableExport !== false,
                enableRetry: options.enableRetry !== false,
                maxDisplayItems: options.maxDisplayItems || 50,
                ...options.config
            };

            // 状态数据
            this.statusData = {
                summary: null,
                successfulOrders: [],
                failedOrders: [],
                totalTime: 0,
                processedAt: null
            };

            // 事件回调
            this.onRetryFailed = options.onRetryFailed || null;
            this.onExportResults = options.onExportResults || null;
            this.onBackToMain = options.onBackToMain || null;
        }

        /**
         * 初始化状态
         */
        initializeState() {
            this.state.data = {
                ...this.statusData
            };
        }

        /**
         * 渲染组件
         */
        render() {
            if (!this.elements.container) {
                throw new Error('状态面板组件需要容器元素');
            }

            // 生成状态面板HTML
            const panelHTML = this.generatePanelHTML();
            
            // 创建状态面板元素
            const panelElement = document.createElement('div');
            panelElement.innerHTML = panelHTML;
            this.elements.root = panelElement.firstElementChild;

            // 添加到容器
            this.elements.container.appendChild(this.elements.root);

            // 绑定事件
            this.bindPanelEvents();

            // 标记为已渲染
            this.state.isRendered = true;

            this.logger.log('📈 状态面板已渲染', 'info');
        }

        /**
         * 生成状态面板HTML
         * @returns {string} HTML字符串
         */
        generatePanelHTML() {
            return `
                <div class="status-panel" data-component-id="${this.componentId}">
                    <div class="status-header">
                        <h4>📈 处理结果</h4>
                        <div class="status-timestamp" id="statusTimestamp-${this.componentId}">
                            <!-- 处理时间将在这里显示 -->
                        </div>
                    </div>
                    
                    ${this.config.showSummary ? `
                        <div class="results-summary" id="resultsSummary-${this.componentId}">
                            <!-- 结果摘要将在这里显示 -->
                        </div>
                    ` : ''}
                    
                    ${this.config.showSuccessOrders ? `
                        <div class="success-orders" id="successOrders-${this.componentId}">
                            <h5>✅ 成功创建的订单</h5>
                            <div class="success-list">
                                <!-- 成功订单将在这里显示 -->
                            </div>
                        </div>
                    ` : ''}
                    
                    ${this.config.showFailedOrders ? `
                        <div class="failed-orders" id="failedOrders-${this.componentId}">
                            <h5>❌ 创建失败的订单</h5>
                            <div class="failed-list">
                                <!-- 失败订单将在这里显示 -->
                            </div>
                        </div>
                    ` : ''}
                    
                    ${this.config.showActions ? `
                        <div class="status-actions">
                            ${this.config.enableRetry ? `
                                <button type="button" 
                                        class="btn btn-warning retry-btn hidden" 
                                        id="retryFailedBtn-${this.componentId}">
                                    🔄 重试失败订单
                                </button>
                            ` : ''}
                            ${this.config.enableExport ? `
                                <button type="button" 
                                        class="btn btn-info export-btn" 
                                        id="exportResultsBtn-${this.componentId}">
                                    📄 导出结果
                                </button>
                            ` : ''}
                            <button type="button" 
                                    class="btn btn-primary back-btn" 
                                    id="backToMainBtn-${this.componentId}">
                                🏠 返回主页
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        /**
         * 绑定面板事件
         */
        bindPanelEvents() {
            if (!this.elements.root) return;

            // 重试按钮
            const retryBtn = this.elements.root.querySelector('.retry-btn');
            if (retryBtn) {
                this.addEventListener(retryBtn, 'click', () => {
                    this.handleRetryFailed();
                });
            }

            // 导出按钮
            const exportBtn = this.elements.root.querySelector('.export-btn');
            if (exportBtn) {
                this.addEventListener(exportBtn, 'click', () => {
                    this.handleExportResults();
                });
            }

            // 返回主页按钮
            const backBtn = this.elements.root.querySelector('.back-btn');
            if (backBtn) {
                this.addEventListener(backBtn, 'click', () => {
                    this.handleBackToMain();
                });
            }
        }

        /**
         * 显示处理结果
         * @param {Object} results - 处理结果
         */
        showResults(results) {
            if (!results) return;

            // 更新状态数据
            this.state.data = {
                summary: results.summary || null,
                successfulOrders: results.successful || [],
                failedOrders: results.failed || [],
                totalTime: results.totalTime || 0,
                processedAt: results.processedAt || new Date().toISOString()
            };

            // 更新UI
            this.updateSummaryDisplay();
            this.updateSuccessOrdersDisplay();
            this.updateFailedOrdersDisplay();
            this.updateTimestampDisplay();
            this.updateActionsDisplay();

            this.logger.log('📈 处理结果已显示', 'info', this.state.data.summary);
        }

        /**
         * 更新摘要显示
         */
        updateSummaryDisplay() {
            if (!this.config.showSummary) return;

            const summaryElement = this.elements.root?.querySelector(`#resultsSummary-${this.componentId}`);
            if (!summaryElement || !this.state.data.summary) return;

            const summary = this.state.data.summary;
            const successRate = summary.successRate || 0;
            const processingTime = Math.round((this.state.data.totalTime || 0) / 1000);

            summaryElement.innerHTML = `
                <div class="summary-stats">
                    <div class="summary-stat">
                        <span class="stat-number">${summary.total || 0}</span>
                        <span class="stat-label">总订单数</span>
                    </div>
                    <div class="summary-stat success">
                        <span class="stat-number">${summary.successful || 0}</span>
                        <span class="stat-label">成功创建</span>
                    </div>
                    <div class="summary-stat failed">
                        <span class="stat-number">${summary.failed || 0}</span>
                        <span class="stat-label">创建失败</span>
                    </div>
                    <div class="summary-stat rate">
                        <span class="stat-number">${successRate}%</span>
                        <span class="stat-label">成功率</span>
                    </div>
                </div>
                <div class="summary-message">
                    ${this.generateSummaryMessage(summary)}
                </div>
                <div class="summary-details">
                    <span class="processing-time">处理时间: ${processingTime}秒</span>
                </div>
            `;
        }

        /**
         * 生成摘要消息
         * @param {Object} summary - 摘要数据
         * @returns {string} 摘要消息
         */
        generateSummaryMessage(summary) {
            const total = summary.total || 0;
            const successful = summary.successful || 0;
            const failed = summary.failed || 0;

            if (successful === total && total > 0) {
                return '🎉 所有订单都已成功创建！';
            } else if (failed === total && total > 0) {
                return '😞 所有订单创建都失败了，请检查订单信息。';
            } else if (total > 0) {
                return `✅ ${successful} 个订单创建成功，${failed} 个订单需要处理。`;
            } else {
                return '没有订单需要处理。';
            }
        }

        /**
         * 更新成功订单显示
         */
        updateSuccessOrdersDisplay() {
            if (!this.config.showSuccessOrders) return;

            const successSection = this.elements.root?.querySelector(`#successOrders-${this.componentId}`);
            const successList = successSection?.querySelector('.success-list');
            if (!successList) return;

            const successfulOrders = this.state.data.successfulOrders.slice(0, this.config.maxDisplayItems);

            if (successfulOrders.length === 0) {
                successList.innerHTML = '<div class="empty-message">没有成功创建的订单</div>';
                successSection.style.display = 'none';
            } else {
                successList.innerHTML = successfulOrders.map(result => {
                    const customerName = result.order?.customer_name || result.orderData?.customer_name || '未知客户';
                    const orderId = result.result?.orderId || result.result?.apiResponse?.id || '未知订单号';
                    
                    return `
                        <div class="result-item">
                            <div class="result-item-info">
                                <div class="result-item-title">${customerName}</div>
                                <div class="result-item-details">订单号: ${orderId}</div>
                            </div>
                            <div class="result-item-status status-success">成功</div>
                        </div>
                    `;
                }).join('');
                
                successSection.style.display = 'block';
            }
        }

        /**
         * 更新失败订单显示
         */
        updateFailedOrdersDisplay() {
            if (!this.config.showFailedOrders) return;

            const failedSection = this.elements.root?.querySelector(`#failedOrders-${this.componentId}`);
            const failedList = failedSection?.querySelector('.failed-list');
            if (!failedList) return;

            const failedOrders = this.state.data.failedOrders.slice(0, this.config.maxDisplayItems);

            if (failedOrders.length === 0) {
                failedList.innerHTML = '<div class="empty-message">没有失败的订单</div>';
                failedSection.style.display = 'none';
            } else {
                failedList.innerHTML = failedOrders.map(result => {
                    const customerName = result.order?.customer_name || result.orderData?.customer_name || '未知客户';
                    const errorMessage = result.error || result.message || '未知错误';
                    
                    return `
                        <div class="result-item">
                            <div class="result-item-info">
                                <div class="result-item-title">${customerName}</div>
                                <div class="result-item-details">错误: ${errorMessage}</div>
                            </div>
                            <div class="result-item-status status-failed">失败</div>
                        </div>
                    `;
                }).join('');
                
                failedSection.style.display = 'block';
            }
        }

        /**
         * 更新时间戳显示
         */
        updateTimestampDisplay() {
            const timestampElement = this.elements.root?.querySelector(`#statusTimestamp-${this.componentId}`);
            if (!timestampElement) return;

            if (this.state.data.processedAt) {
                const processedTime = new Date(this.state.data.processedAt);
                timestampElement.textContent = `处理完成时间: ${processedTime.toLocaleString()}`;
            }
        }

        /**
         * 更新操作按钮显示
         */
        updateActionsDisplay() {
            if (!this.config.showActions) return;

            // 更新重试按钮
            if (this.config.enableRetry) {
                const retryBtn = this.elements.root?.querySelector('.retry-btn');
                if (retryBtn) {
                    const failedCount = this.state.data.failedOrders.length;
                    if (failedCount > 0) {
                        retryBtn.classList.remove('hidden');
                        retryBtn.textContent = `🔄 重试失败订单 (${failedCount})`;
                    } else {
                        retryBtn.classList.add('hidden');
                    }
                }
            }
        }

        /**
         * 处理重试失败订单
         */
        handleRetryFailed() {
            const failedOrders = this.state.data.failedOrders;
            
            if (failedOrders.length === 0) {
                this.showToast('没有失败的订单需要重试', 'warning');
                return;
            }

            const confirmed = confirm(`确定要重试 ${failedOrders.length} 个失败的订单吗？`);
            if (!confirmed) return;

            this.logger.log(`🔄 重试 ${failedOrders.length} 个失败订单`, 'info');

            if (this.onRetryFailed) {
                this.onRetryFailed(failedOrders);
            }
        }

        /**
         * 处理导出结果
         */
        handleExportResults() {
            try {
                const exportData = {
                    timestamp: new Date().toISOString(),
                    summary: this.state.data.summary,
                    successfulOrders: this.state.data.successfulOrders,
                    failedOrders: this.state.data.failedOrders,
                    totalTime: this.state.data.totalTime,
                    processedAt: this.state.data.processedAt
                };

                if (this.onExportResults) {
                    this.onExportResults(exportData);
                } else {
                    this.defaultExportResults(exportData);
                }

                this.showToast('结果已导出', 'success');
                this.logger.log('📄 处理结果已导出', 'success');

            } catch (error) {
                this.logger.logError('导出结果失败', error);
                this.showToast('导出失败，请重试', 'error');
            }
        }

        /**
         * 默认导出结果方法
         * @param {Object} exportData - 导出数据
         */
        defaultExportResults(exportData) {
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `multi-order-results-${Date.now()}.json`;
            link.click();
        }

        /**
         * 处理返回主页
         */
        handleBackToMain() {
            this.logger.log('🏠 返回主页', 'info');

            if (this.onBackToMain) {
                this.onBackToMain();
            }
        }

        /**
         * 显示提示消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         */
        showToast(message, type = 'info') {
            // 创建临时提示元素
            const toast = document.createElement('div');
            toast.className = `status-panel-toast toast-${type}`;
            toast.textContent = message;
            
            // 添加到面板容器
            if (this.elements.root) {
                this.elements.root.appendChild(toast);
                
                // 显示动画
                setTimeout(() => toast.classList.add('show'), 10);
                
                // 自动移除
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        }

        /**
         * 清空结果显示
         */
        clearResults() {
            this.state.data = {
                summary: null,
                successfulOrders: [],
                failedOrders: [],
                totalTime: 0,
                processedAt: null
            };

            // 清空UI
            if (this.elements.root) {
                const summaryElement = this.elements.root.querySelector(`#resultsSummary-${this.componentId}`);
                if (summaryElement) summaryElement.innerHTML = '';

                const successList = this.elements.root.querySelector('.success-list');
                if (successList) successList.innerHTML = '';

                const failedList = this.elements.root.querySelector('.failed-list');
                if (failedList) failedList.innerHTML = '';

                const timestampElement = this.elements.root.querySelector(`#statusTimestamp-${this.componentId}`);
                if (timestampElement) timestampElement.textContent = '';
            }
        }

        /**
         * 销毁回调
         */
        onDestroy() {
            // 清理回调函数
            this.onRetryFailed = null;
            this.onExportResults = null;
            this.onBackToMain = null;
            
            // 清理状态数据
            this.statusData = null;
        }
    }

    // 注册组件到组件注册中心
    if (window.OTA?.Components?.registry) {
        window.OTA.Components.registry.register('StatusPanel', StatusPanel, {
            singleton: false,
            autoCreate: false
        });
    }

    // 暴露到OTA命名空间
    window.OTA.Components.StatusPanel = StatusPanel;

    console.log('✅ 状态面板组件已加载');

})();
