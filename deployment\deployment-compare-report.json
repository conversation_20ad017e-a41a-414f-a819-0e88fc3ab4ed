{"baseUrl": "https://createjobgmh.netlify.app", "generatedAt": "2025-08-17T08:40:00.134Z", "files": [{"file": "index.html", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "277091f3391dd34eeea769fe93d51233a9c97d260973bc9f42d6fa8f1c2f068e", "remoteHash": "02a4764e00ee7587e6c99d98ad632c299f675ccbe34f26122a3ad2bab51b7e31", "localSize": 35675, "remoteSize": 32861, "remoteError": null, "previewLocal": "<!-- build-stamp: will be replaced at deploy; version 2.0.3 -->\r", "previewRemote": "<!DOCTYPE html>"}, {"file": "sw.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "fb5d69077b04c332d51f0984d6dea8f3783848c1db95072201d43f2f439aa068", "remoteHash": "fb5d69077b04c332d51f0984d6dea8f3783848c1db95072201d43f2f439aa068", "localSize": 11327, "remoteSize": 10391, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "main.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "96b43aeb68dc24da951a43b65be6d54553d079f6a1ab0e83a3c68bbceaa83eb9", "remoteHash": "96b43aeb68dc24da951a43b65be6d54553d079f6a1ab0e83a3c68bbceaa83eb9", "localSize": 8643, "remoteSize": 6199, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/script-manifest.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "55f41e82865de73af09c1ab587c1f42550266ed81d071f79b3d234441c80a743", "remoteHash": "6c7cff55e83cf505a94347acfc2a133da86850bfd4efb826fa975551e63f8558", "localSize": 7021, "remoteSize": 5592, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/script-loader.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "7dadc270c30c05647bd8f72912db9922b0f35c95bb88b0513bef5c782568d018", "remoteHash": "7dadc270c30c05647bd8f72912db9922b0f35c95bb88b0513bef5c782568d018", "localSize": 7139, "remoteSize": 7049, "remoteError": null, "previewLocal": "// Script Loader - phased, ordered, resilient loader for classic scripts", "previewRemote": "// Script Loader - phased, ordered, resilient loader for classic scripts"}, {"file": "js/core/dependency-container.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "bc2ffdaf9184787508fff0cae4502d278091e9bea2692d79caefda93b7d0bd0a", "remoteHash": "bc2ffdaf9184787508fff0cae4502d278091e9bea2692d79caefda93b7d0bd0a", "localSize": 9726, "remoteSize": 8546, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/service-locator.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "802773d573bab1d11df890ba5cc229a7d7d2d6431d26edcc036ada1b02439d0f", "remoteHash": "802773d573bab1d11df890ba5cc229a7d7d2d6431d26edcc036ada1b02439d0f", "localSize": 18273, "remoteSize": 15529, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/application-bootstrap.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "944a05f70d50c685ed066527a06e7d09716b809e7934ed255a0b329638bcd946", "remoteHash": "944a05f70d50c685ed066527a06e7d09716b809e7934ed255a0b329638bcd946", "localSize": 20907, "remoteSize": 18799, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/component-lifecycle-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "a1984c914422edc887e92e04b3c3e22a4a2a2b4152d7523536eb2b86278f9dc9", "remoteHash": "a1984c914422edc887e92e04b3c3e22a4a2a2b4152d7523536eb2b86278f9dc9", "localSize": 16133, "remoteSize": 14751, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/runtime-version-watcher.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "515a9a583486a4b065b906209fdf328682107219c337b9937cdacdc45203adcc", "remoteHash": "515a9a583486a4b065b906209fdf328682107219c337b9937cdacdc45203adcc", "localSize": 2925, "remoteSize": 2819, "remoteError": null, "previewLocal": "// Runtime Version Watcher", "previewRemote": "// Runtime Version Watcher"}, {"file": "js/core/language-detector.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "dc41ca9d2a7e818b6c5209f5e34bf6c95d090fad1cc1e97c1c6e126bd8cf1078", "remoteHash": "dc41ca9d2a7e818b6c5209f5e34bf6c95d090fad1cc1e97c1c6e126bd8cf1078", "localSize": 34097, "remoteSize": 30389, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/core/feature-toggle.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "cc13f1c051cfe27e885600ebba4cc683d484d621b9631b4bbe840a5c610d7b76", "remoteHash": "cc13f1c051cfe27e885600ebba4cc683d484d621b9631b4bbe840a5c610d7b76", "localSize": 11808, "remoteSize": 10756, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/hotel-name-database.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "7fd00a8d45c3a635c72b4d16c2f45de119079fb40a27e945801c2850b7a51aad", "remoteHash": "7fd00a8d45c3a635c72b4d16c2f45de119079fb40a27e945801c2850b7a51aad", "localSize": 48838, "remoteSize": 37562, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/hotel-data-essential.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "f997e4f3a6c503fe66f627700a6ea13d13833481577a264ae81e80a299deb96c", "remoteHash": "b0595d4f7a1bf70e1dd313a61da19b6467b745c7c68a2cf8f074af750f12880d", "localSize": 21213, "remoteSize": 18196, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (essential data)\r", "previewRemote": "/**"}, {"file": "js/flow/prompt-builder.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "a3d391462bd6f6502e8d21b784c4a8dda88b25662ccbc235335a271d81951480", "remoteHash": "a3d391462bd6f6502e8d21b784c4a8dda88b25662ccbc235335a271d81951480", "localSize": 23845, "remoteSize": 17655, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/flow/gemini-caller.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "65665922dfc98163a19b4b8a37041f84f26464630d1dc0dc9dcd02a62efdd53a", "remoteHash": "65665922dfc98163a19b4b8a37041f84f26464630d1dc0dc9dcd02a62efdd53a", "localSize": 15984, "remoteSize": 13838, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/flow/result-processor.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "fac66633d71dd3ba568b5b1aa25c1980d525f82e45c37c4ca6a5b6338bdf7ca6", "remoteHash": "fac66633d71dd3ba568b5b1aa25c1980d525f82e45c37c4ca6a5b6338bdf7ca6", "localSize": 18095, "remoteSize": 15725, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/flow/order-parser.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "30a5ae913d1281237a6c9edb6314a186735eabd460145957047ca3c55e43fb24", "remoteHash": "30a5ae913d1281237a6c9edb6314a186735eabd460145957047ca3c55e43fb24", "localSize": 20596, "remoteSize": 18132, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/flow/knowledge-base.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "9ddb91f634645b5452e215b25ffd8f1d5d23d1c6215a4120982b3c01659ee4d2", "remoteHash": "9ddb91f634645b5452e215b25ffd8f1d5d23d1c6215a4120982b3c01659ee4d2", "localSize": 17335, "remoteSize": 15353, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "hotels_by_region.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "f35e1a13c49da71b167f4ada9a0975ea804b04b3e3e2c20833b8eadce737a689", "remoteHash": "f35e1a13c49da71b167f4ada9a0975ea804b04b3e3e2c20833b8eadce737a689", "localSize": 680783, "remoteSize": 587352, "remoteError": null, "previewLocal": "  /**", "previewRemote": "  /**"}, {"file": "js/order/api-caller.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "6182e15f700176285de7985757d180d5779555a12f4adea121c72d7c9c29e2a5", "remoteHash": "6182e15f700176285de7985757d180d5779555a12f4adea121c72d7c9c29e2a5", "localSize": 14861, "remoteSize": 13287, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/order/history-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "a42f5e4c2e0696eb34d3b562e19d4ba0076e006c44278d0a021a4ecb7ac5869d", "remoteHash": "a42f5e4c2e0696eb34d3b562e19d4ba0076e006c44278d0a021a4ecb7ac5869d", "localSize": 18996, "remoteSize": 16966, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/controllers/order-management-controller.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "d04e0be9af298b17a28b496032d88e9195a21dfafaf9d1c065246e51372f6389", "remoteHash": "d04e0be9af298b17a28b496032d88e9195a21dfafaf9d1c065246e51372f6389", "localSize": 12615, "remoteSize": 10697, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/adapters/multi-order-manager-adapter.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "0706533f3c973f9d6976863cb1fec0319e70b7670664d6cef3d0eb88b172b62f", "remoteHash": "811aeb2aef6f76c31cd8caa75c31c3a4f7e2b6aa2cab2776098824bd78dc0acb", "localSize": 21071, "remoteSize": 17795, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-manager-adapter)\r", "previewRemote": "/**"}, {"file": "js/adapters/ui-manager-adapter.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "83d016a8707f2da91d1d578ec1ef1c7586e94d17c237bb5d2fa0b29274e253bd", "remoteHash": "5e9df7cdeb2f58bb39e35acc89aa6fb1d96d87acaa447c5099dc7f9f71e5eb7c", "localSize": 10202, "remoteSize": 8293, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (ui-manager-adapter)\r", "previewRemote": "/**"}, {"file": "js/api-service.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "bdf85ced3a75e7851b73be3a78ca38aa0482d61799c60beb7c9e0c8524e4313b", "remoteHash": "bdf85ced3a75e7851b73be3a78ca38aa0482d61799c60beb7c9e0c8524e4313b", "localSize": 60877, "remoteSize": 53521, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/hotel-data-inline.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "dfb53a1d08663a8ad0b25543a3040226f0e917d17a0864505d134b77df349c37", "remoteHash": "389975559a035febd3f106248ee79adac744215e7c8721a7e2232403f036d4e5", "localSize": 9539, "remoteSize": 8156, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (hotel-data-inline)\r", "previewRemote": "/**"}, {"file": "js/image-upload-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "592ad3241455b4949627d517b8c57a4e739b8a61e96586960e31b68847cb0e1d", "remoteHash": "592ad3241455b4949627d517b8c57a4e739b8a61e96586960e31b68847cb0e1d", "localSize": 40566, "remoteSize": 36732, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/flight-info-service.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "d9785cdd20026f889a48549dd08ad386f2ef049bd8e644b5aaa991838edd170b", "remoteHash": "d9785cdd20026f889a48549dd08ad386f2ef049bd8e644b5aaa991838edd170b", "localSize": 5431, "remoteSize": 4887, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/page-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "5202222e68ebbb2c9143382866f25fd56c1d4e0069ee25ae8a5d6634c381f955", "remoteHash": "5202222e68ebbb2c9143382866f25fd56c1d4e0069ee25ae8a5d6634c381f955", "localSize": 12426, "remoteSize": 10902, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/base-component.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "7e3842de74268033fdf5aada88e95494c0d265788bc989760c69c7e29396fcff", "remoteHash": "7e3842de74268033fdf5aada88e95494c0d265788bc989760c69c7e29396fcff", "localSize": 10412, "remoteSize": 9238, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/component-registry.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "7ddc228e0d62fe6f3b4adb66bd39445f4f3d0e8d83b473ffa5bf6f4cc36b8e31", "remoteHash": "7ddc228e0d62fe6f3b4adb66bd39445f4f3d0e8d83b473ffa5bf6f4cc36b8e31", "localSize": 12463, "remoteSize": 11051, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/order-card.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "b6634bc2df52c58078f0babe646c4b40bcda9cce327cb3d8afd4366d5df894cf", "remoteHash": "b6634bc2df52c58078f0babe646c4b40bcda9cce327cb3d8afd4366d5df894cf", "localSize": 13590, "remoteSize": 12723, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/batch-controls.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "ee7fe8790bcec1b7d9f2efbb7452b628dfcd2e35c096d66620f66314790af828", "remoteHash": "ee7fe8790bcec1b7d9f2efbb7452b628dfcd2e35c096d66620f66314790af828", "localSize": 14836, "remoteSize": 13672, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/progress-indicator.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "854b8ace0bbf82032e482c212e8913a1010b38a3c19da6172cd82a70c9b302ff", "remoteHash": "854b8ace0bbf82032e482c212e8913a1010b38a3c19da6172cd82a70c9b302ff", "localSize": 17524, "remoteSize": 16458, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/components/status-panel.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "80ca91d7ca785827d5b377524ced0fac8f2cf8803abeddf89c12d1acc1e57b57", "remoteHash": "80ca91d7ca785827d5b377524ced0fac8f2cf8803abeddf89c12d1acc1e57b57", "localSize": 20462, "remoteSize": 19114, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/services/order-detector.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "92d16c4bcaf9821fa5e0a7d212663337cbf82dbc99ad7cd4cf85181fb6210f9d", "remoteHash": "92d16c4bcaf9821fa5e0a7d212663337cbf82dbc99ad7cd4cf85181fb6210f9d", "localSize": 11597, "remoteSize": 10495, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/services/order-processor.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "7af7f89ebe7ad414423cbd61daad1be016f367e64de5931d4cfef23334553122", "remoteHash": "7af7f89ebe7ad414423cbd61daad1be016f367e64de5931d4cfef23334553122", "localSize": 14163, "remoteSize": 12937, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/services/batch-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "d5b9a797677c0fe20d9332c3841b264c4f31e5a1aa23bdeac7fee00b59ab79f8", "remoteHash": "d5b9a797677c0fe20d9332c3841b264c4f31e5a1aa23bdeac7fee00b59ab79f8", "localSize": 19749, "remoteSize": 18171, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/services/state-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "b48925012cd6d15ef68482e1193354c7ab576f3a48573f02be6a286f0e189d19", "remoteHash": "b48925012cd6d15ef68482e1193354c7ab576f3a48573f02be6a286f0e189d19", "localSize": 18432, "remoteSize": 17156, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/services/api-client.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "ab6eb77b17b7bceb506d3a7b00ef77244d7bdefcd4febc41c3c604045b4b9a94", "remoteHash": "ab6eb77b17b7bceb506d3a7b00ef77244d7bdefcd4febc41c3c604045b4b9a94", "localSize": 14910, "remoteSize": 13710, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/pages/multi-order/multi-order-page-v2.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "dc6b434306f633ba406ebb1eacbd4316962381b88f6e31162b50b073c8ef644c", "remoteHash": "dc6b434306f633ba406ebb1eacbd4316962381b88f6e31162b50b073c8ef644c", "localSize": 47154, "remoteSize": 42880, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/multi-order/field-mapping-validator.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "a852291ef4d506aa6b954a9e68c70045acdfcffaf0c522ffb44077d20b6bf9ce", "remoteHash": "3d064e3c5b5733d970077997d094d36ec5bdfea1d78e60255d55b453bd839762", "localSize": 12824, "remoteSize": 11152, "remoteError": null, "previewLocal": "/**\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-detector.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "80fd90b2b0eb3a89ab46de07a3caed24b4c5660b0a980d12424ca112785f90ae", "remoteHash": "678ac7e8eec06be70367388e1151510a9edbe7eb8a3f2140910f1216a7a7f5d8", "localSize": 11569, "remoteSize": 9734, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-detector)\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-renderer.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "e1d9d6e502852a4c63278f4bc8540ce91f6fd8b61c9f75d72b63ffbd01246cf9", "remoteHash": "1e67158ab87b6e2cb69c414f9ce51180a5f581034802365b724d9f52dd3fd63c", "localSize": 21389, "remoteSize": 18666, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-renderer)\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-processor.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "f93a453764597ddcdf41d8b2561f451bb080b3045d8524fa2738f6dc561184a4", "remoteHash": "ce2715d3bfbd70a228d0ee4f654caa2af95f308748687ae48165a476d57ff4c8", "localSize": 19436, "remoteSize": 16669, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-processor)\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-transformer.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "2fd1c11ac35c1e50a32dcb82dba3ae80f24eb7a889abc7055e0de71c16c52fd2", "remoteHash": "c8728283b7ab6f72369fa1da913d4b239e3ef16b6254e981de2bf3bcecfc7d79", "localSize": 32514, "remoteSize": 28292, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-transformer)\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-state-manager.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "cadea99018053db8f31067be1cca6a31ac898187c1368a2aecffbeb3880ce617", "remoteHash": "07522067cd360b7c5817ec07c801e21da76c7c057a81e027367b938d0c6ae126", "localSize": 19017, "remoteSize": 16578, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-state-manager)\r", "previewRemote": "/**"}, {"file": "js/multi-order/batch-processor.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "36a4380d472e2950b28f1dcd7331b8fcf468a20d698f8aa43f2d1ea07a9f2aba", "remoteHash": "2690c3f940fe44f780db7f7dd2a1c7687a27f1f7c9d81e2b799e58ae1f7f147a", "localSize": 14845, "remoteSize": 13089, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (batch-processor)\r", "previewRemote": "/**"}, {"file": "js/multi-order/multi-order-coordinator.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "b9184be46a4d89cf0b87fa6c022e7b7a87eecece4395c635ef6eb26885656643", "remoteHash": "10bc6ff4b31a97dbe3fc41140bbcc09ad158ed29c8cfb8983ed512d1a14b4ec0", "localSize": 17141, "remoteSize": 14991, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (multi-order-coordinator)\r", "previewRemote": "/**"}, {"file": "js/multi-order/system-integrity-checker.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "150294d8b25b66dd01ed8c893e88f9751964fcbd33f8cd4e9737042a574fefe3", "remoteHash": "13effb4bbedc03e77ac44874e7a7cb974e4275dc656281b99932c69801e04aac", "localSize": 13729, "remoteSize": 11551, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (system-integrity-checker)\r", "previewRemote": "/**"}, {"file": "js/managers/event-manager.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "df38c6edd25f10968454663689b4136caccdb66fc6b98bc1f7bb3ffba0bdab70", "remoteHash": "bbe9aa826a7bdb280d9985bf627ee351cce377ec39dac1b668f3e37c226e8929", "localSize": 56908, "remoteSize": 50183, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (event-manager)\r", "previewRemote": "/**"}, {"file": "js/managers/ui-state-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "d81a9ed181a8d9eeedb70d0db184cfcc4648dca75a2bae7b6192ae0f9d541651", "remoteHash": "d81a9ed181a8d9eeedb70d0db184cfcc4648dca75a2bae7b6192ae0f9d541651", "localSize": 22659, "remoteSize": 20210, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/managers/animation-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "09cd09ca582213012b9c588e71232b219fd91b08cccbef92f8cc173123bfa18d", "remoteHash": "09cd09ca582213012b9c588e71232b219fd91b08cccbef92f8cc173123bfa18d", "localSize": 16851, "remoteSize": 15071, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/managers/realtime-analysis-manager.js", "localExists": true, "remoteStatus": 200, "same": true, "localHash": "373f42a0230dfb266bf2ed108d3e01992e1bb7c0a0e9cb44d01f1316f8aa309b", "remoteHash": "373f42a0230dfb266bf2ed108d3e01992e1bb7c0a0e9cb44d01f1316f8aa309b", "localSize": 46897, "remoteSize": 40315, "remoteError": null, "previewLocal": "/**", "previewRemote": "/**"}, {"file": "js/adapters/ota-manager-decorator.js", "localExists": true, "remoteStatus": 200, "same": false, "localHash": "ef088f637f8e7a8315191a968df79e83e06d636df819386d3b0f1e5169fa506a", "remoteHash": "08d7165d2ed591f0f1edb73c776dbe19b0f5bb441af0739ae8ab4552ab20e00b", "localSize": 6222, "remoteSize": 5345, "remoteError": null, "previewLocal": "// build-stamp: version 2.0.3 (ota-manager-decorator)\r", "previewRemote": "/**"}], "summary": {"total": 56, "mismatches": 17, "missingRemote": 0, "missingLocal": 0}}