#!/usr/bin/env node
/**
 * generate-build-info.js
 * 自动生成部署元数据: /.well-known/build.json + build-info.js
 */
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
function safeRead(p){ return fs.existsSync(p) ? fs.readFileSync(p,'utf8'):''; }
function sha256(txt){ return crypto.createHash('sha256').update(txt).digest('hex'); }
const root = process.cwd();
const files = { manifest: 'js/core/script-manifest.js', loader: 'js/core/script-loader.js', main: 'main.js' };
const contents = Object.fromEntries(Object.entries(files).map(([k,rel]) => [k, safeRead(path.join(root, rel))]));
let version = 'dev';
const match = contents.manifest.match(/version:\s*['"]([^'"\n]+)['"]/); if (match) version = match[1];
const hashes = Object.fromEntries(Object.entries(contents).map(([k,txt]) => [k+"Hash", sha256(txt)]));
const buildHash = sha256(Object.values(contents).join('\n---\n'));
const buildInfo = { version, buildHash, generatedAt: new Date().toISOString(), environment: process.env.DEPLOY_ENV || 'local', gitCommit: process.env.GIT_COMMIT || null, node: process.version, files: hashes };
const wkDir = path.join(root, '.well-known'); if (!fs.existsSync(wkDir)) fs.mkdirSync(wkDir); fs.writeFileSync(path.join(wkDir, 'build.json'), JSON.stringify(buildInfo, null, 2));
const jsOut = `window.BUILD_INFO=${JSON.stringify(buildInfo)};`; fs.writeFileSync(path.join(root, 'build-info.js'), jsOut);
console.log('✅ build.json 已生成 (.well-known/build.json)'); console.log('🔐 buildHash:', buildHash.slice(0,16)+'...'); console.log('🛠️ version:', version);
