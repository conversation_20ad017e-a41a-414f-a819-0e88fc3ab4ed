/**
 * 生产环境构建脚本
 * 压缩、优化、合并Linus重构版本
 */

const fs = require('fs');
const path = require('path');

// 简单的JavaScript压缩器
function minifyJS(code) {
    return code
        // 移除注释
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\/\/.*$/gm, '')
        // 移除多余空白
        .replace(/\s+/g, ' ')
        .replace(/;\s*}/g, ';}')
        .replace(/{\s*/g, '{')
        .replace(/}\s*/g, '}')
        .replace(/,\s*/g, ',')
        .replace(/:\s*/g, ':')
        .replace(/;\s*/g, ';')
        // 移除行首行尾空白
        .trim();
}

// 构建配置
const buildConfig = {
    input: {
        core: 'js/core.js',
        compatibility: 'js/compatibility-bridge.js',
        main: 'main.js'
    },
    output: {
        dir: 'dist',
        filename: 'ota-system.min.js'
    }
};

console.log('🏗️  开始构建生产版本...');

try {
    // 创建输出目录
    if (!fs.existsSync(buildConfig.output.dir)) {
        fs.mkdirSync(buildConfig.output.dir);
    }

    // 读取源文件
    const coreCode = fs.readFileSync(buildConfig.input.core, 'utf8');
    const compatibilityCode = fs.readFileSync(buildConfig.input.compatibility, 'utf8');
    const mainCode = fs.readFileSync(buildConfig.input.main, 'utf8');

    // 合并代码
    const combinedCode = `
        /** OTA System - Linus Refactor Production Build */
        ${coreCode}
        
        ${compatibilityCode}
        
        ${mainCode}
    `;

    // 压缩代码
    const minifiedCode = minifyJS(combinedCode);

    // 计算压缩比
    const originalSize = combinedCode.length;
    const minifiedSize = minifiedCode.length;
    const compressionRatio = ((originalSize - minifiedSize) / originalSize * 100).toFixed(1);

    // 写入文件
    const outputPath = path.join(buildConfig.output.dir, buildConfig.output.filename);
    fs.writeFileSync(outputPath, minifiedCode);

    // 创建生产环境HTML
    const productionHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - 生产环境</title>
    <link rel="stylesheet" href="../css/main.css">
    <style>
        .production-banner {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        .performance-stats {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="production-banner">
        🚀 生产环境 - Linus Torvalds式重构版本
    </div>
    
    <div class="performance-stats" id="perfStats">
        Loading...
    </div>

    <!-- 主要内容同index.html，但使用压缩版本 -->
    <div id="app">
        <!-- 登录面板等UI组件... -->
        <div id="loginPanel" class="login-panel">
            <div class="login-card">
                <h2>🚗 OTA订单处理系统</h2>
                <form id="loginForm">
                    <input type="email" id="email" placeholder="邮箱" required>
                    <input type="password" id="password" placeholder="密码" required>
                    <button type="submit">登录</button>
                </form>
            </div>
        </div>

        <div id="workspace" class="hidden">
            <h1>订单处理</h1>
            <textarea id="orderInput" placeholder="粘贴订单信息..."></textarea>
            
            <form id="orderForm">
                <input type="text" id="customerName" placeholder="客户姓名">
                <input type="text" id="pickup" placeholder="接送地点">
                <input type="text" id="dropoff" placeholder="目的地">
                <input type="date" id="pickupDate">
                <input type="time" id="pickupTime">
                <button type="submit">创建订单</button>
            </form>
        </div>

        <div id="multiOrderPanel" class="hidden">
            <!-- 多订单面板 -->
        </div>
    </div>

    <!-- 单一压缩文件 -->
    <script src="${buildConfig.output.filename}"></script>
    
    <script>
        // 性能监控
        const startTime = performance.now();
        
        window.addEventListener('load', () => {
            const loadTime = performance.now() - startTime;
            const perfStats = document.getElementById('perfStats');
            if (perfStats) {
                perfStats.innerHTML = \`
                    ⚡ \${loadTime.toFixed(0)}ms<br>
                    📦 ${minifiedSize} bytes<br>
                    🗜️ ${compressionRatio}% compressed
                \`;
            }
            
            console.log('🚀 Production system loaded in', loadTime.toFixed(1) + 'ms');
            console.log('📦 Bundle size:', ${minifiedSize}, 'bytes');
            console.log('🗜️ Compression:', '${compressionRatio}%');
        });
    </script>
</body>
</html>`;

    fs.writeFileSync(path.join(buildConfig.output.dir, 'index.html'), productionHTML);

    // 构建报告
    console.log('✅ 构建完成！');
    console.log(`📁 输出目录: ${buildConfig.output.dir}/`);
    console.log(`📦 文件大小: ${originalSize} → ${minifiedSize} bytes`);
    console.log(`🗜️ 压缩比: ${compressionRatio}%`);
    console.log(`⚡ 预计加载时间: ~${Math.round(minifiedSize / 1000)}ms (1MB/s网络)`);

} catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
}