<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API 时间测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 14px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .results {
            margin-top: 30px;
        }
        .result-item {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .timing-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .timing-card {
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .timing-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .timing-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.testing {
            background-color: #fff3cd;
            color: #856404;
        }
        .prompt-preview {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error-details {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #721c24;
        }
        .json-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Gemini API 时间测试页面</h1>
            <p>完全同步当前系统的提示词构建逻辑，测试API响应时间和成功率</p>
        </div>

        <div class="test-section">
            <h3>📝 测试配置</h3>
            
            <div class="input-group">
                <label for="testInput">订单文本输入：</label>
                <textarea id="testInput" placeholder="请输入订单文本进行测试...">订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08:51:26
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡市中豪亚酒店

[抵达]樟宜机场T1

约26.4公里

2025-08-11 14:10:00

联系人许丽俊

真实号：13916811351

---
4成人0儿童

司机姓名：kk

司机电话：17605088</textarea>
            </div>

            <div class="input-group">
                <label for="channelSelect">渠道选择：</label>
                <select id="channelSelect">
                    <option value="auto">自动检测</option>
                    <option value="Fliggy" selected>Fliggy</option>
                    <option value="Jing Ge">Jing Ge</option>
                    <option value="generic">通用（无渠道）</option>
                </select>
            </div>

            <div class="button-group">
                <button class="btn-primary" onclick="runSingleTest()">🚀 单次测试</button>
                <button class="btn-secondary" onclick="runBatchTest()">📊 批量测试 (5次)</button>
                <button class="btn-secondary" onclick="clearResults()">🗑️ 清空结果</button>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 当前配置</h3>
            <div class="timing-info">
                <div class="timing-card">
                    <div class="timing-value">15s</div>
                    <div class="timing-label">基础超时</div>
                </div>
                <div class="timing-card">
                    <div class="timing-value">19s</div>
                    <div class="timing-label">最大超时 (15s+4s)</div>
                </div>
                <div class="timing-card">
                    <div class="timing-value">2</div>
                    <div class="timing-label">重试次数</div>
                </div>
                <div class="timing-card">
                    <div class="timing-value">8192</div>
                    <div class="timing-label">Token限制</div>
                </div>
            </div>
        </div>

        <div id="currentStatus" class="test-section" style="display: none;">
            <h3>🔄 当前状态</h3>
            <div id="statusContent"></div>
        </div>

        <div id="promptPreview" class="test-section" style="display: none;">
            <h3>📋 生成的提示词预览</h3>
            <div class="prompt-preview" id="promptContent"></div>
        </div>

        <div id="results" class="results">
            <h3>📊 测试结果</h3>
            <div id="resultsList"></div>
        </div>
    </div>

    <script>
        // 🚀 完全同步当前系统的配置
        const GEMINI_CONFIG = {
            apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            modelVersion: 'gemini-2.5-flash',
            baseTimeout: 15000,
            maxRetries: 2,
            retryDelays: [1000, 2000],
            maxOutputTokens: 8192
        };

        const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_CONFIG.modelVersion}:generateContent`;

        let testResults = [];
        let currentTestId = 0;

        // 🚀 完全同步的基础提示词模板
        const BASE_PROMPT_TEMPLATE = `
你是一个专为GoMyHire用车服务订单解析的智能引擎。请严格按照以下"输出契约"和"业务规则"解析输入文本，并返回标准化JSON。

【输出契约】
- 单订单：返回单个JSON对象（不是数组、无Markdown代码块、无额外解释文本）。
- 多订单：返回一个JSON对象，结构如下：
  {
    "isMultiOrder": true,
    "orderCount": <number>,
    "orders": [ { ...单订单对象... }, ... ],
    "confidence": <number|null>
  }

【字段定义】
单订单对象必须包含以下字段（未知则为null）：
{
  "customer_name": <string|null>,
  "customer_contact": <string|null>,
  "customer_email": <string|null>,
  "pickup_location": <string|null>,
  "dropoff_location": <string|null>,
  "pickup_date": <string|null>,
  "pickup_time": <string|null>,
  "ota_price": <number|null>,
  "driver_fee": <number|null>,
  "car_type_id": <number|null>,
  "sub_category_id": <number|null>,
  "driving_region_id": <number|null>,
  "backend_user_id": <number|null>,
  "ota": <string|null>,
  "ota_reference_number": <string|null>,
  "flight_info": <string|null>,
  "extra_requirement": <string|null>
}

【业务规则】
1. 价格处理：优先识别"商家实收"、"总价"、"实付"等关键词后的数值。
2. 时间格式：pickup_date使用YYYY-MM-DD，pickup_time使用HH:MM:SS。
3. 联系方式：customer_contact优先手机号，customer_email提取邮箱。
4. 地点信息：pickup_location和dropoff_location保持原始描述的准确性。
5. 车型映射：根据文本中的车型描述，映射到对应的car_type_id。
6. 渠道识别：根据订单格式和特征，识别ota字段。
7. 参考号：ota_reference_number提取订单编号或参考号。

【最终输出要求】
- 仅输出符合上述结构的 JSON（无多余解释/无Markdown代码块）。
- 单订单→返回"单个对象"；多订单→返回"多订单对象"。
- 所有定义字段必须出现，即使为 null。
 - 严禁输出未在"字段定义"中列出的任何字段；特别是不要返回 languages_id_array、language、languages、orderIndex、processedAt 等中间/调试字段。
 - 以下字段即使文本未给出也必须显式出现（未知则置为 null）：pickup_date、driving_region_id、customer_email、flight_info、extra_requirement。

订单信息：`;

        // 🚀 完全同步的渠道策略片段
        const CHANNEL_STRATEGIES = {
            'Fliggy': {
                ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
                ota_price: '价格识别与换算：请识别"商家实收"数值作为订单价格；识别订单所属地区，若为马来西亚，最终价格=商家实收×0.84×0.615；若为新加坡，最终价格=商家实收×0.84×0.2；均保留两位小数，输出最终价；无法确定地区时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                pickup_date: 'Fliggy日期提取：**必须从订单文本中提取具体日期**，如"2025-08-11"等完整格式，转换为YYYY-MM-DD格式输出。',
                driving_region_id: 'Fliggy地区映射：根据地点准确识别driving_region_id，特别注意斗湖机场→4(Sabah)，不可返回null。',
                pickup_location: '上车地点：必须从订单中提取完整地点名称，保持原始描述准确性。'
            },
            'Jing Ge': {
                ota: '渠道识别：请识别这是JingGe商铺渠道的订单，输出JSON时ota字段请设置为"Jing Ge"。',
                ota_price: '价格识别与换算：若为JingGe商铺订单，最终价格=基础价×0.615；保留两位小数，明确输出最终价；无法确定时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                customer_contact: '若订单未提供手机号，可临时使用订单号(ota_reference_number)作为联系标识填充customer_contact字段；若存在手机号，请保持原值，不要覆盖。',
                ota_reference_number: '订单号识别：请从文本中抽取明确的订单编号；一般为纯数字组合，若无可靠线索，请返回null，不要凭空生成。'
            }
        };

        // 🚀 完全同步的渠道检测逻辑
        function detectChannel(input) {
            if (!input) return { channel: null, confidence: 0, method: 'invalid_input' };

            // Fliggy检测
            if (/订单编号[：:\s]*\d{19}/.test(input)) {
                return { channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern' };
            }

            // JingGe检测
            if (/商铺/.test(input)) {
                return { channel: 'Jing Ge', confidence: 0.85, method: 'jingge_pattern' };
            }

            return { channel: null, confidence: 0, method: 'no_match' };
        }

        // 🚀 完全同步的提示词构建函数
        function buildPrompt(input, channelResult) {
            let prompt = BASE_PROMPT_TEMPLATE;

            if (!channelResult.channel || channelResult.channel === 'generic') {
                // 通用提示词
                prompt += `
通用处理规则：
- 使用标准的订单解析逻辑
- 支持多语言和多渠道格式
- 采用通用的价格计算方式
- 标准车型推荐算法
`;
                prompt += `\n\n${input}`;
                return prompt;
            }

            // 渠道专属处理
            const strategy = CHANNEL_STRATEGIES[channelResult.channel];
            if (strategy) {
                prompt += `\n\n**${channelResult.channel}渠道专属处理规则：**\n`;
                
                const fieldInstructions = Object.entries(strategy)
                    .map(([field, instruction], index) => `${index + 1}. [${field}] ${instruction}`)
                    .join('\n');

                prompt += fieldInstructions;
                prompt += '\n';
                prompt += `\n**当前处理渠道：${channelResult.channel}**\n`;
                prompt += '请严格按照该渠道的处理规则进行解析。\n';
            }

            prompt += `\n\n${input}`;
            return prompt;
        }

        // 🚀 完全同步的API调用函数
        async function callGeminiAPI(prompt, testId) {
            const startTime = performance.now();
            let lastError;

            for (let attempt = 0; attempt < GEMINI_CONFIG.maxRetries; attempt++) {
                const currentTimeout = GEMINI_CONFIG.baseTimeout + (attempt * 4000);
                const attemptStartTime = performance.now();

                try {
                    updateStatus(testId, `尝试 ${attempt + 1}/${GEMINI_CONFIG.maxRetries} (超时: ${currentTimeout/1000}s)`, 'testing');

                    const requestBody = {
                        contents: [{
                            parts: [{ text: prompt }]
                        }],
                        generationConfig: {
                            temperature: 0.3,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: GEMINI_CONFIG.maxOutputTokens,
                            responseMimeType: "application/json"
                        }
                    };

                    // 🚀 使用修复后的AbortController
                    const abortController = new AbortController();
                    const timeoutId = setTimeout(() => {
                        abortController.abort();
                    }, currentTimeout);

                    const response = await fetch(`${API_URL}?key=${GEMINI_CONFIG.apiKey}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestBody),
                        signal: abortController.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();
                    const totalTime = performance.now() - startTime;
                    const attemptTime = performance.now() - attemptStartTime;

                    return {
                        success: true,
                        data: data,
                        totalTime: totalTime,
                        attemptTime: attemptTime,
                        attempt: attempt + 1,
                        timeout: currentTimeout
                    };

                } catch (error) {
                    lastError = error;
                    const attemptTime = performance.now() - attemptStartTime;
                    
                    console.log(`尝试 ${attempt + 1} 失败:`, error.message, `耗时: ${attemptTime.toFixed(2)}ms`);

                    if (attempt < GEMINI_CONFIG.maxRetries - 1) {
                        const retryDelay = GEMINI_CONFIG.retryDelays[attempt] || 2000;
                        updateStatus(testId, `等待 ${retryDelay}ms 后重试...`, 'testing');
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    }
                }
            }

            const totalTime = performance.now() - startTime;
            return {
                success: false,
                error: lastError.message,
                totalTime: totalTime,
                attempt: GEMINI_CONFIG.maxRetries
            };
        }

        // 更新状态显示
        function updateStatus(testId, message, type) {
            const statusDiv = document.getElementById('currentStatus');
            const statusContent = document.getElementById('statusContent');
            statusDiv.style.display = 'block';
            statusContent.innerHTML = `
                <div class="status ${type}">
                    测试 #${testId}: ${message}
                </div>
            `;
        }

        // 显示提示词预览
        function showPromptPreview(prompt) {
            const previewDiv = document.getElementById('promptPreview');
            const promptContent = document.getElementById('promptContent');
            previewDiv.style.display = 'block';
            promptContent.textContent = prompt;
        }

        // 单次测试
        async function runSingleTest() {
            const input = document.getElementById('testInput').value.trim();
            const selectedChannel = document.getElementById('channelSelect').value;
            
            if (!input) {
                alert('请输入订单文本');
                return;
            }

            currentTestId++;
            const testId = currentTestId;

            try {
                updateStatus(testId, '开始测试...', 'testing');

                // 渠道检测
                let channelResult;
                if (selectedChannel === 'auto') {
                    channelResult = detectChannel(input);
                } else if (selectedChannel === 'generic') {
                    channelResult = { channel: null, confidence: 0, method: 'manual_generic' };
                } else {
                    channelResult = { channel: selectedChannel, confidence: 1.0, method: 'manual_select' };
                }

                // 构建提示词
                const prompt = buildPrompt(input, channelResult);
                showPromptPreview(prompt);

                updateStatus(testId, '调用 Gemini API...', 'testing');

                // 调用API
                const result = await callGeminiAPI(prompt, testId);

                // 显示结果
                displayResult(testId, {
                    input: input,
                    channel: channelResult.channel || '通用',
                    promptLength: prompt.length,
                    ...result
                });

                updateStatus(testId, result.success ? '测试完成' : '测试失败', result.success ? 'success' : 'error');

            } catch (error) {
                displayResult(testId, {
                    input: input,
                    success: false,
                    error: error.message,
                    totalTime: 0
                });
                updateStatus(testId, '测试异常', 'error');
            }
        }

        // 批量测试
        async function runBatchTest() {
            const input = document.getElementById('testInput').value.trim();
            if (!input) {
                alert('请输入订单文本');
                return;
            }

            for (let i = 1; i <= 5; i++) {
                await runSingleTest();
                if (i < 5) {
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 间隔2秒
                }
            }
        }

        // 显示测试结果
        function displayResult(testId, result) {
            const resultsList = document.getElementById('resultsList');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result-item';
            
            const statusClass = result.success ? 'success' : 'error';
            const statusText = result.success ? '✅ 成功' : '❌ 失败';
            
            resultDiv.innerHTML = `
                <h4>测试 #${testId} - ${statusText}</h4>
                
                <div class="timing-info">
                    <div class="timing-card">
                        <div class="timing-value">${result.totalTime ? (result.totalTime/1000).toFixed(2) + 's' : 'N/A'}</div>
                        <div class="timing-label">总耗时</div>
                    </div>
                    <div class="timing-card">
                        <div class="timing-value">${result.attempt || 'N/A'}</div>
                        <div class="timing-label">尝试次数</div>
                    </div>
                    <div class="timing-card">
                        <div class="timing-value">${result.promptLength || 'N/A'}</div>
                        <div class="timing-label">提示词长度</div>
                    </div>
                    <div class="timing-card">
                        <div class="timing-value">${result.channel || '通用'}</div>
                        <div class="timing-label">检测渠道</div>
                    </div>
                </div>

                <div><strong>输入文本:</strong> ${result.input ? result.input.substring(0, 100) + '...' : 'N/A'}</div>
                
                ${result.success ? `
                    <div class="json-result">
                        <strong>API返回结果:</strong><br>
                        ${JSON.stringify(result.data, null, 2)}
                    </div>
                ` : `
                    <div class="error-details">
                        <strong>错误信息:</strong> ${result.error}
                    </div>
                `}
                
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    测试时间: ${new Date().toLocaleString()}
                </div>
            `;
            
            resultsList.insertBefore(resultDiv, resultsList.firstChild);
            testResults.push(result);
        }

        // 清空结果
        function clearResults() {
            document.getElementById('resultsList').innerHTML = '';
            document.getElementById('currentStatus').style.display = 'none';
            document.getElementById('promptPreview').style.display = 'none';
            testResults = [];
            currentTestId = 0;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Gemini API 时间测试页面已加载');
            console.log('配置信息:', GEMINI_CONFIG);
        });
    </script>
</body>
</html>
