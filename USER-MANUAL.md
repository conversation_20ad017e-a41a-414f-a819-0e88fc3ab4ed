# OTA系统用户手册 - Linus重构版

## 📖 目录
1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [基本操作](#基本操作)
4. [高级功能](#高级功能)
5. [监控与维护](#监控与维护)
6. [故障排除](#故障排除)
7. [最佳实践](#最佳实践)

## 🚀 快速开始

### 首次使用
1. **打开系统**: 在浏览器中访问系统地址
2. **配置API**: 点击设置图标，输入Gemini API密钥
3. **测试连接**: 创建一个测试订单验证系统正常工作
4. **安装PWA**: 点击浏览器提示安装应用（可选）

### 5分钟快速上手
```
1. 填写客户信息 → 2. 选择地点时间 → 3. 点击创建 → 4. 完成！
```

## 🖥️ 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────┐
│  🏠 OTA订单处理系统      [⚙️设置] [📊监控] [📱安装]  │
├─────────────────────────────────────────────────────┤
│  📝 订单信息                                        │
│  ┌─────────────────────────────────────────────────┐ │
│  │ 客户姓名: [____________]  联系方式: [__________] │ │
│  │ 接送地点: [____________]  目的地:   [__________] │ │
│  │ 日期时间: [____________]  乘客数:   [__________] │ │
│  └─────────────────────────────────────────────────┘ │
│                                                     │
│  📸 图片上传                                        │
│  ┌─────────────────────────────────────────────────┐ │
│  │           拖拽图片到此处或点击上传                │ │
│  └─────────────────────────────────────────────────┘ │
│                                                     │
│  📋 批量处理                                        │
│  ┌─────────────────────────────────────────────────┐ │
│  │           粘贴多个订单信息到此处                  │ │
│  └─────────────────────────────────────────────────┘ │
│                                                     │
│         [创建订单] [批量处理] [查看历史]             │
└─────────────────────────────────────────────────────┘
```

### 功能区域说明

#### 1. 顶部导航栏
- **系统标题**: 显示当前系统名称和版本
- **设置按钮** ⚙️: 配置API密钥和系统参数
- **监控按钮** 📊: 打开性能监控面板
- **安装按钮** 📱: 安装PWA应用

#### 2. 订单信息区
- **客户信息**: 姓名、联系方式
- **行程信息**: 接送地点、目的地、时间
- **服务信息**: 乘客数量、特殊需求

#### 3. 图片上传区
- **拖拽上传**: 直接拖拽图片文件
- **点击上传**: 点击区域选择文件
- **格式支持**: JPG, PNG, WebP

#### 4. 批量处理区
- **文本输入**: 粘贴多个订单信息
- **自动分割**: AI智能识别订单边界
- **批量处理**: 一次性处理多个订单

## 📝 基本操作

### 创建单个订单

#### 步骤1: 填写基本信息
```
客户姓名: 张三
联系方式: 1380013800
```

#### 步骤2: 设置行程信息
```
接送地点: 香格里拉酒店
目的地: KLIA机场
日期: 2025-08-16
时间: 14:00
乘客数量: 2
```

#### 步骤3: 创建订单
1. 检查信息准确性
2. 点击"创建订单"按钮
3. 等待系统处理
4. 确认订单创建成功

### 使用图片创建订单

#### 支持的图片类型
- **聊天截图**: WhatsApp, 微信对话
- **表格截图**: Excel, Google Sheets
- **手写便条**: 清晰的手写订单信息
- **打印文档**: PDF, 图片格式的订单

#### 操作步骤
1. **上传图片**: 拖拽或点击上传
2. **等待分析**: 系统AI分析图片内容（通常2-5秒）
3. **确认信息**: 检查AI提取的信息准确性
4. **修正错误**: 手动修正任何识别错误
5. **创建订单**: 点击创建完成

#### 图片要求
- **分辨率**: 最小300x300像素
- **文字清晰**: 字体大小至少12px
- **格式**: JPG, PNG, WebP
- **大小**: 最大10MB

### 批量处理订单

#### 文本格式要求
```
订单1:
客户: 李四
电话: 1390013900
从: 希尔顿酒店
到: LCCT机场
时间: 2025-08-16 15:30
人数: 3

订单2:
客户: 王五
电话: 1350013500
从: 万豪酒店
到: KL Central
时间: 2025-08-16 16:00
人数: 2
```

#### 处理流程
1. **粘贴文本**: 将订单信息粘贴到批量处理区
2. **自动分割**: 系统识别订单边界
3. **逐个确认**: 确认每个订单信息
4. **批量创建**: 一次性处理所有订单
5. **查看结果**: 检查处理结果和错误

## 🔥 高级功能

### 智能地点识别

#### 支持的地点类型
- **酒店**: 自动识别马来西亚主要酒店
- **机场**: KLIA, KLIA2, LCCT, 槟城机场等
- **地标**: KL Central, KLCC, Genting等
- **地址**: 完整街道地址

#### 模糊匹配
```
输入: "香格里拉"
系统识别: "Shangri-La Hotel Kuala Lumpur"

输入: "吉隆坡机场"
系统识别: "KLIA机场"
```

### 时间智能处理

#### 支持格式
- **24小时制**: 14:30, 09:00
- **12小时制**: 2:30 PM, 9:00 AM
- **相对时间**: 明天早上, 后天下午
- **航班时间**: 配合航班号自动计算

#### 自动调整
- **时区转换**: 自动处理不同时区
- **提前时间**: 根据路程自动建议提前时间
- **冲突检测**: 检查时间冲突并提醒

### 客户信息管理

#### 自动补全
```javascript
// 系统记住常用客户信息
{
  "张三": {
    "phone": "1380013800",
    "preferences": {
      "pickup": "香格里拉酒店",
      "vehicle": "标准轿车"
    }
  }
}
```

#### 历史记录
- **订单历史**: 查看客户以往订单
- **偏好设置**: 记住客户常用地点
- **特殊需求**: 保存客户特殊要求

### 多语言支持

#### 输入语言
- **中文**: 简体、繁体
- **英文**: 英式、美式
- **马来文**: 标准马来语
- **混合**: 中英文混合输入

#### 输出格式
```
中文输出: 客户: 张三, 从香格里拉酒店到KLIA机场
英文输出: Customer: Zhang San, From Shangri-La Hotel to KLIA Airport
马来文输出: Pelanggan: Zhang San, Dari Hotel Shangri-La ke Lapangan Terbang KLIA
```

## 📊 监控与维护

### 性能监控面板

#### 访问方式
1. 点击主界面的"监控"按钮
2. 或直接访问 `monitoring-dashboard.html`

#### 监控指标

**系统概览**
- 响应时间: 平均API响应时间
- 吞吐量: 每秒处理请求数
- 错误率: 失败请求百分比
- 活跃用户: 当前在线用户数

**性能详情**
- CPU使用率: 系统资源使用情况
- 内存使用: 应用内存占用
- 缓存命中率: 缓存效率
- 数据库连接: 数据库性能

**错误监控**
- 错误趋势: 错误发生趋势图
- 错误分类: 按类型分类的错误
- 告警通知: 实时错误告警

### 负载测试

#### 测试类型
```javascript
// 轻量测试: 5个并发用户，30秒
runLoadTest('light');

// 中等测试: 20个并发用户，2分钟
runLoadTest('medium');

// 重负载测试: 50个并发用户，5分钟
runLoadTest('heavy');

// 压力测试: 100个并发用户，10分钟
runLoadTest('stress');
```

#### 测试场景
- **基础流程**: 模拟正常用户操作
- **API压力**: 快速连续API调用
- **多订单**: 批量订单处理
- **图片上传**: 文件上传压力测试

### 数据导出

#### 监控报告
```javascript
// 导出性能报告
dashboard.exportData();

// 导出错误日志
window.errorMonitor.exportErrors();

// 导出负载测试结果
runLoadTest('medium').then(result => {
    console.log('测试报告:', result);
});
```

#### 报告格式
```json
{
  "timestamp": "2025-08-16T06:00:00.000Z",
  "metrics": {
    "responseTime": 156,
    "throughput": 45.2,
    "errorRate": 0.012
  },
  "errors": [
    {
      "type": "APIError",
      "message": "Request timeout",
      "timestamp": "2025-08-16T05:58:30.000Z"
    }
  ],
  "system": {
    "version": "1.0.0-linus-refactor",
    "environment": "production"
  }
}
```

## 🛠️ 故障排除

### 常见问题诊断

#### 问题1: 订单创建失败
**症状**: 点击创建订单后显示错误信息
**诊断步骤**:
1. 检查网络连接
2. 验证API密钥配置
3. 查看错误监控面板
4. 检查输入数据格式

**解决方案**:
```javascript
// 1. 检查API密钥
console.log('API配置:', window.PRODUCTION_CONFIG.gemini);

// 2. 重新配置API
window.productionConfig.set('gemini.apiKey', 'YOUR_NEW_API_KEY');

// 3. 测试连接
window.ota.testAPIConnection();
```

#### 问题2: 图片上传失败
**症状**: 图片无法上传或分析失败
**诊断步骤**:
1. 检查图片格式和大小
2. 确认网络连接稳定
3. 查看浏览器控制台错误
4. 检查API限制

**解决方案**:
```javascript
// 检查图片信息
const fileInfo = {
    name: file.name,
    size: file.size,
    type: file.type
};
console.log('图片信息:', fileInfo);

// 压缩图片
const compressedFile = await compressImage(file, 0.8);
```

#### 问题3: 系统响应缓慢
**症状**: 页面加载慢，操作反应迟钝
**诊断步骤**:
1. 打开性能监控面板
2. 检查网络状况
3. 清除浏览器缓存
4. 查看系统资源使用

**解决方案**:
```javascript
// 1. 清除缓存
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.controller.postMessage({
        type: 'CLEAR_CACHE'
    });
}

// 2. 重新加载核心模块
location.reload();

// 3. 检查性能指标
const metrics = window.performanceMonitor.getMetrics();
console.log('性能指标:', metrics);
```

### 错误代码参考

| 代码 | 含义 | 解决方案 |
|------|------|----------|
| E001 | API密钥无效 | 重新配置正确的API密钥 |
| E002 | 网络连接失败 | 检查网络连接，稍后重试 |
| E003 | 图片格式不支持 | 使用JPG、PNG或WebP格式 |
| E004 | 订单数据格式错误 | 检查必填字段是否完整 |
| E005 | 服务器响应超时 | 检查网络或联系管理员 |
| E006 | 权限不足 | 检查API密钥权限设置 |
| E007 | 并发限制 | 减少同时处理的订单数量 |
| E008 | 存储空间不足 | 清理浏览器缓存 |

### 调试工具

#### 控制台命令
```javascript
// 查看系统状态
window.ota.getSystemStatus();

// 查看配置信息
console.log('系统配置:', window.PRODUCTION_CONFIG);

// 测试API连接
window.ota.testAPIConnection();

// 查看缓存信息
navigator.serviceWorker.controller.postMessage({
    type: 'GET_CACHE_INFO'
});

// 生成诊断报告
window.ota.generateDiagnosticReport();
```

#### 性能分析
```javascript
// 性能标记
performance.mark('operation-start');
// ... 执行操作 ...
performance.mark('operation-end');
performance.measure('operation-time', 'operation-start', 'operation-end');

// 查看结果
const measures = performance.getEntriesByType('measure');
console.log('性能测量:', measures);
```

## 🎯 最佳实践

### 效率提升技巧

#### 1. 键盘快捷键
- `Ctrl + N`: 新建订单
- `Ctrl + S`: 保存当前订单
- `Ctrl + Enter`: 快速创建订单
- `Ctrl + H`: 查看订单历史
- `Ctrl + U`: 上传图片

#### 2. 批量操作
```
// 高效的批量处理格式
订单1: 张三, 1380013800, 香格里拉→KLIA, 2025-08-16 14:00, 2人
订单2: 李四, 1390013900, 希尔顿→LCCT, 2025-08-16 15:30, 3人
订单3: 王五, 1350013500, 万豪→KL Central, 2025-08-16 16:00, 2人
```

#### 3. 模板使用
```javascript
// 创建订单模板
const template = {
    pickup: '香格里拉酒店',
    destination: 'KLIA机场',
    time_advance: 180, // 提前3小时
    vehicle_type: '标准轿车'
};

// 应用模板
window.ota.applyTemplate(template);
```

### 数据管理

#### 1. 定期备份
```javascript
// 导出订单历史
const history = window.ota.getOrderHistory();
const backup = {
    timestamp: new Date().toISOString(),
    data: history
};
localStorage.setItem('ota_backup', JSON.stringify(backup));
```

#### 2. 清理旧数据
```javascript
// 清理30天前的数据
const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
window.ota.cleanupOldData(thirtyDaysAgo);
```

### 安全建议

#### 1. API密钥管理
- 定期更换API密钥（建议每月一次）
- 不要在多个设备间共享密钥
- 监控API使用量，发现异常及时处理

#### 2. 数据保护
- 不要在公共场所处理敏感客户信息
- 定期清理浏览器缓存
- 使用HTTPS连接访问系统

#### 3. 访问控制
- 限制系统访问人员
- 使用强密码保护相关账户
- 定期审查访问日志

### 性能优化

#### 1. 网络优化
```javascript
// 启用压缩传输
window.productionConfig.set('api.compression', true);

// 使用缓存
window.productionConfig.set('cache.enabled', true);
```

#### 2. 资源管理
```javascript
// 定期清理缓存
setInterval(() => {
    if (performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {
        window.ota.cleanup();
    }
}, 300000); // 每5分钟检查一次
```

#### 3. 监控告警
```javascript
// 设置性能告警
window.performanceMonitor.setThreshold('responseTime', 1000);
window.performanceMonitor.setThreshold('errorRate', 0.05);
```

## 📞 支持与反馈

### 技术支持
- **在线文档**: 本手册和迁移指南
- **监控面板**: 实时系统状态
- **错误日志**: 详细错误信息
- **诊断工具**: 内置调试功能

### 联系方式
- **邮箱**: <EMAIL>
- **电话**: +60-3-xxxx-xxxx（工作时间：周一至周五 9:00-18:00）
- **在线支持**: 访问官网在线客服

### 反馈渠道
- **功能建议**: 通过邮箱提交改进建议
- **错误报告**: 使用诊断工具生成错误报告
- **用户体验**: 分享使用心得和建议

---

**感谢使用OTA订单处理系统 - Linus重构版！** 🎉

本手册将持续更新，如有疑问请随时联系技术支持团队。