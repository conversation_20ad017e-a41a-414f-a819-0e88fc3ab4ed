/**
 * 核心OTA系统 - <PERSON><PERSON> Torvalds式重构版本
 * 
 * 这个文件体现了"好品味"原则：
 * - 消除不必要的抽象层
 * - 直接解决问题，不创造问题
 * - 简单、直接、高效
 * 
 * "我是个该死的实用主义者。" - <PERSON><PERSON> Torvalds
 */

'use strict';

// 简化的全局对象 - 消除服务定位器地狱
window.ota = {
    config: {
        api: {
            baseURL: 'https://gomyhire.com.my/api',
            timeout: 30000
        },
        gemini: {
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent',
            timeout: 45000
        }
    },
    
    // 合并的订单历史 - 简化版本 (替代OrderHistoryManager)
    history: {
        storageKey: 'ota_order_history',
        maxSize: 1000,
        
        save(order, userEmail) {
            const key = `${this.storageKey}_${userEmail}`;
            const history = JSON.parse(localStorage.getItem(key) || '[]');
            
            // 添加时间戳
            order.created_at = new Date().toISOString();
            order.id = order.id || Date.now().toString();
            
            // 添加到开头，保持最新的在前
            history.unshift(order);
            
            // 限制数量
            if (history.length > this.maxSize) {
                history.splice(this.maxSize);
            }
            
            localStorage.setItem(key, JSON.stringify(history));
            console.log(`✅ 订单已保存到历史: ${order.id}`);
        },
        
        get(userEmail, limit = 50) {
            const key = `${this.storageKey}_${userEmail}`;
            const history = JSON.parse(localStorage.getItem(key) || '[]');
            return history.slice(0, limit);
        },
        
        clear(userEmail) {
            const key = `${this.storageKey}_${userEmail}`;
            localStorage.removeItem(key);
        }
    },
    
    // 合并的酒店数据 - 核心版本 (替代3个酒店数据文件)
    hotels: {
        data: [
            // 吉隆坡主要酒店
            { name: '香格里拉酒店', english: 'Shangri-La Hotel Kuala Lumpur', region: 'KL' },
            { name: '希尔顿酒店', english: 'Hilton Kuala Lumpur', region: 'KL' },
            { name: '万豪酒店', english: 'JW Marriott Kuala Lumpur', region: 'KL' },
            { name: '丽思卡尔顿酒店', english: 'The Ritz-Carlton Kuala Lumpur', region: 'KL' },
            { name: '洲际酒店', english: 'InterContinental Kuala Lumpur', region: 'KL' },
            // 槟城主要酒店  
            { name: '槟城香格里拉酒店', english: 'Shangri-La Hotel Penang', region: 'Penang' },
            { name: '东方大酒店', english: 'Eastern & Oriental Hotel', region: 'Penang' },
            // 马六甲主要酒店
            { name: '马六甲香格里拉酒店', english: 'Shangri-La Hotel Malacca', region: 'Malacca' }
        ],
        
        find(query) {
            const q = query.toLowerCase();
            return this.data.filter(hotel => 
                hotel.name.includes(q) || 
                hotel.english.toLowerCase().includes(q)
            );
        },
        
        normalize(hotelName) {
            const found = this.find(hotelName);
            return found.length > 0 ? found[0].english : hotelName;
        }
    },
    
    // 渠道检测 - 本地处理，快速响应
    channelDetector: {
        patterns: {
            fliggy: /订单编号[：:\s]*\d{19}/,
            jingge: /jingge|jinggeshop|精格|精格商铺/i,
            reference: /^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i
        },
        
        detect(text) {
            const results = [];
            
            // Fliggy检测
            if (this.patterns.fliggy.test(text)) {
                results.push({ channel: 'fliggy', confidence: 0.9 });
            }
            
            // JingGe检测
            if (this.patterns.jingge.test(text)) {
                results.push({ channel: 'jingge', confidence: 0.85 });
            }
            
            // 参考号检测
            if (this.patterns.reference.test(text)) {
                results.push({ channel: 'generic', confidence: 0.7 });
            }
            
            return results.length > 0 ? results[0] : null;
        }
    },
    
    // Gemini API调用器 - 直接调用，无适配器废话
    gemini: {
        async parseOrder(text, channel = null) {
            const prompt = this.buildPrompt(text, channel);
            const apiKey = window.GEMINI_API_KEY;
            
            if (!apiKey) {
                throw new Error('Gemini API密钥未配置');
            }
            
            try {
                const response = await fetch(`${window.ota.config.gemini.endpoint}?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 4096
                        }
                    }),
                    signal: AbortSignal.timeout(window.ota.config.gemini.timeout)
                });
                
                if (!response.ok) {
                    throw new Error(`Gemini API错误: ${response.status}`);
                }
                
                const data = await response.json();
                const resultText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!resultText) {
                    throw new Error('Gemini返回空结果');
                }
                
                return JSON.parse(resultText);
                
            } catch (error) {
                console.error('Gemini API调用失败:', error);
                throw error;
            }
        },
        
        buildPrompt(text, channel) {
            let basePrompt = `请解析以下订单文本，返回JSON格式的订单信息：\n\n${text}\n\n`;
            
            if (channel) {
                basePrompt += `检测到的渠道: ${channel.channel}\n`;
            }
            
            basePrompt += `
要求：
1. 如果包含多个订单，设置 "isMultiOrder": true，并在 "orders" 数组中返回所有订单
2. 如果只有一个订单，设置 "isMultiOrder": false，在 "order" 中返回订单信息
3. 必须提取：customer_name, customer_contact, pickup, destination, date, time, passenger_number
4. 可选字段：customer_email, ota_reference_number, flight_info, sub_category_id, driving_region_id

返回格式示例：
{
  "isMultiOrder": false,
  "order": {
    "customer_name": "张三",
    "customer_contact": "+60123456789",
    "pickup": "吉隆坡国际机场",
    "destination": "市中心酒店",
    "date": "2025-08-15",
    "time": "14:00",
    "passenger_number": 2
  }
}`;
            
            return basePrompt;
        }
    },
    
    // API服务 - 简化版，只保留核心功能
    api: {
        async request(endpoint, options = {}) {
            const url = `${window.ota.config.api.baseURL}${endpoint}`;
            const token = localStorage.getItem('access_token');
            
            const config = {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` }),
                    ...options.headers
                },
                signal: AbortSignal.timeout(window.ota.config.api.timeout)
            };
            
            if (options.body) {
                config.body = JSON.stringify(options.body);
            }
            
            try {
                const response = await fetch(url, config);
                
                if (!response.ok) {
                    throw new Error(`API错误: ${response.status} ${response.statusText}`);
                }
                
                return await response.json();
                
            } catch (error) {
                console.error(`API请求失败 [${endpoint}]:`, error);
                throw error;
            }
        },
        
        // 核心API方法 - 去掉所有适配器废话
        async createOrder(orderData) {
            return this.request('/v1/order-jobs', {
                method: 'POST',
                body: orderData
            });
        },
        
        async getOrderHistory(params = {}) {
            const query = new URLSearchParams(params).toString();
            return this.request(`/v1/order-jobs?${query}`);
        },
        
        async getCoreData() {
            // 一次性获取所有核心数据，避免多次请求
            const [subCategories, carTypes, regions] = await Promise.all([
                this.request('/v1/sub-categories'),
                this.request('/v1/car-types'),
                this.request('/v1/driving-regions')
            ]);
            
            return { subCategories, carTypes, regions };
        }
    },
    
    // 合并的多订单处理 - 简化版本 (替代MultiOrderCoordinator等5个模块)
    multiOrder: {
        isActive: false,
        orders: [],
        originalText: '',
        
        // 检测多订单 - 简化逻辑
        detect(text) {
            const orderPatterns = [
                /订单[编号]*[：:\s]*[\d\w]+/gi,
                /\d{2}:\d{2}.*?\d{2}:\d{2}/g,  // 时间范围
                /(\d{1,2}月\d{1,2}日|\d{4}-\d{2}-\d{2})/g  // 日期
            ];
            
            const matches = orderPatterns.map(pattern => 
                (text.match(pattern) || []).length
            );
            
            const totalMatches = matches.reduce((a, b) => a + b, 0);
            return totalMatches >= 3; // 3个以上匹配认为是多订单
        },
        
        // 激活多订单模式
        activate(orders, originalText) {
            this.isActive = true;
            this.orders = orders;
            this.originalText = originalText;
            
            console.log(`✅ 多订单模式已激活: ${orders.length}个订单`);
            
            // 直接显示界面
            this.showUI();
        },
        
        // 显示多订单界面 - 简化版本
        showUI() {
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) return;
            
            // 生成订单列表HTML
            const ordersHTML = this.orders.map((order, index) => `
                <div class="multi-order-item" data-index="${index}">
                    <input type="checkbox" checked class="order-checkbox">
                    <div class="order-summary">
                        <strong>${order.customer_name || '未知客户'}</strong>
                        <span>${order.date} ${order.time}</span>
                        <span>${order.pickup} → ${order.destination}</span>
                    </div>
                </div>
            `).join('');
            
            panel.innerHTML = `
                <h3>检测到多个订单 (${this.orders.length})</h3>
                <div class="multi-order-list">${ordersHTML}</div>
                <div class="multi-order-actions">
                    <button onclick="window.ota.multiOrder.createSelected()">创建选中订单</button>
                    <button onclick="window.ota.multiOrder.createAll()">创建全部订单</button>
                    <button onclick="window.ota.multiOrder.cancel()">取消</button>
                </div>
            `;
            
            panel.classList.remove('hidden');
        },
        
        // 创建选中的订单
        async createSelected() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            const selectedOrders = Array.from(checkboxes).map(cb => {
                const index = parseInt(cb.closest('.multi-order-item').dataset.index);
                return this.orders[index];
            });
            
            await this.batchCreate(selectedOrders);
        },
        
        // 创建所有订单
        async createAll() {
            await this.batchCreate(this.orders);
        },
        
        // 批量创建订单 - 简化版本
        async batchCreate(orders) {
            console.log(`🚀 开始批量创建 ${orders.length} 个订单`);
            
            const results = [];
            
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                
                try {
                    console.log(`📝 创建订单 ${i + 1}/${orders.length}: ${order.customer_name}`);
                    
                    const result = await window.ota.api.createOrder(order);
                    results.push({ success: true, order, result });
                    
                    // 保存到历史
                    const userEmail = localStorage.getItem('user_email');
                    if (userEmail) {
                        window.ota.history.save(order, userEmail);
                    }
                    
                } catch (error) {
                    console.error(`❌ 订单创建失败: ${order.customer_name}`, error);
                    results.push({ success: false, order, error });
                }
                
                // 简单延迟，避免API限制
                if (i < orders.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            // 显示结果
            this.showResults(results);
        },
        
        // 显示批量创建结果
        showResults(results) {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;
            
            alert(`批量创建完成:\n✅ 成功: ${successCount}\n❌ 失败: ${failCount}`);
            
            // 清理状态
            this.cancel();
        },
        
        // 取消多订单模式
        cancel() {
            this.isActive = false;
            this.orders = [];
            this.originalText = '';
            
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.add('hidden');
            }
            
            console.log('✅ 多订单模式已取消');
        }
    },

    // UI管理器 - 直接DOM操作，无适配器
    ui: {
        elements: {},
        
        init() {
            // 缓存常用元素
            this.elements = {
                orderInput: document.getElementById('orderInput'),
                workspace: document.getElementById('workspace'),
                multiOrderPanel: document.getElementById('multiOrderPanel'),
                orderForm: document.getElementById('orderForm')
            };
            
            // 绑定事件
            this.bindEvents();
            
            console.log('✅ UI初始化完成');
        },
        
        bindEvents() {
            // 订单输入实时分析
            if (this.elements.orderInput) {
                this.elements.orderInput.addEventListener('input', 
                    this.debounce((e) => this.analyzeInput(e.target.value), 1000)
                );
            }
        },
        
        // 直接处理流水线 - 消除事件驱动复杂性
        async analyzeInput(text) {
            if (!text.trim() || text.length < 20) return;
            
            try {
                // 步骤1: 检测多订单
                if (window.ota.multiOrder.detect(text)) {
                    const result = await window.ota.gemini.parseOrder(text);
                    if (result.isMultiOrder) {
                        // 直接激活多订单模式 - 无事件派发
                        return window.ota.multiOrder.activate(result.orders, text);
                    }
                }
                
                // 步骤2: 单订单处理流水线
                const channel = window.ota.channelDetector.detect(text);
                const result = await window.ota.gemini.parseOrder(text, channel);
                
                // 步骤3: 直接填充表单 - 无事件链
                this.fillOrderForm(result);
                
            } catch (error) {
                console.error('订单分析失败:', error);
                this.showError('订单分析失败，请检查输入格式');
            }
        },
        
        // 直接表单填充 - 无事件派发
        fillOrderForm(orderData) {
            const fields = {
                'customerName': orderData.customer_name,
                'customerContact': orderData.customer_contact,
                'customerEmail': orderData.customer_email,
                'pickup': orderData.pickup,
                'dropoff': orderData.destination,
                'pickupDate': orderData.date,
                'pickupTime': orderData.time,
                'passengerCount': orderData.passenger_number,
                'otaReferenceNumber': orderData.ota_reference_number,
                'flightInfo': orderData.flight_info
            };
            
            Object.entries(fields).forEach(([fieldId, value]) => {
                const element = document.getElementById(fieldId);
                if (element && value) {
                    element.value = value;
                    // 酒店名称标准化
                    if (fieldId === 'pickup' || fieldId === 'dropoff') {
                        element.value = window.ota.hotels.normalize(value);
                    }
                }
            });
            
            console.log('✅ 订单表单已填充');
            this.showSuccess('订单信息已自动填充');
        },
        
        // 直接显示消息 - 无事件系统
        showSuccess(message) {
            this.showMessage(message, 'success');
        },
        
        showError(message) {
            this.showMessage(message, 'error');
        },
        
        showMessage(message, type = 'info') {
            // 简单的消息显示，替代复杂的事件系统
            const existingMessage = document.querySelector('.ota-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const messageEl = document.createElement('div');
            messageEl.className = `ota-message ota-message-${type}`;
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 12px 20px; border-radius: 4px; color: white;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            `;
            
            document.body.appendChild(messageEl);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 3000);
        },
        
        // 工具方法
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }
};

// 自动初始化核心系统
document.addEventListener('DOMContentLoaded', () => {
    window.ota.ui.init();
    console.log('✅ OTA核心系统已初始化 - Linus Torvalds式重构版本');
});
                console.error('多订单面板元素不存在');
                return;
            }
            
            // 隐藏主界面
            if (this.elements.workspace) {
                this.elements.workspace.classList.add('hidden');
            }
            
            // 显示多订单面板
            this.elements.multiOrderPanel.classList.remove('hidden');
            
            // 渲染订单列表
            this.renderOrderList(orders, originalText);
            
            console.log(`✅ 多订单模式已激活，共${orders.length}个订单`);
        },
        
        renderOrderList(orders, originalText) {
            const listContainer = document.getElementById('multiOrderList');
            if (!listContainer) return;
            
            listContainer.innerHTML = orders.map((order, index) => `
                <div class="order-card" data-order-index="${index}">
                    <div class="order-header">
                        <input type="checkbox" checked class="order-checkbox" id="order-${index}">
                        <label for="order-${index}">订单 ${index + 1}</label>
                    </div>
                    <div class="order-details">
                        <div><strong>客户:</strong> ${order.customer_name || '未知'}</div>
                        <div><strong>联系:</strong> ${order.customer_contact || '未知'}</div>
                        <div><strong>路线:</strong> ${order.pickup || '未知'} → ${order.destination || '未知'}</div>
                        <div><strong>时间:</strong> ${order.date || '未知'} ${order.time || '未知'}</div>
                        <div><strong>人数:</strong> ${order.passenger_number || '未知'}</div>
                    </div>
                </div>
            `).join('');
            
            // 绑定批量创建按钮
            this.bindMultiOrderEvents(orders);
        },
        
        bindMultiOrderEvents(orders) {
            // 批量创建按钮
            const batchCreateBtn = document.getElementById('batchCreateBtn');
            if (batchCreateBtn) {
                batchCreateBtn.onclick = () => this.batchCreateOrders(orders);
            }
            
            // 返回按钮
            const backBtn = document.getElementById('backToMainBtn');
            if (backBtn) {
                backBtn.onclick = () => this.hideMultiOrderMode();
            }
            
            // 关闭按钮
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            if (closeBtn) {
                closeBtn.onclick = () => this.hideMultiOrderMode();
            }
        },
        
        async batchCreateOrders(orders) {
            const selectedOrders = this.getSelectedOrders(orders);
            
            if (selectedOrders.length === 0) {
                alert('请至少选择一个订单');
                return;
            }
            
            console.log(`开始批量创建 ${selectedOrders.length} 个订单`);
            
            const results = [];
            
            for (let i = 0; i < selectedOrders.length; i++) {
                const order = selectedOrders[i];
                try {
                    const result = await window.ota.api.createOrder(order);
                    results.push({ success: true, order, result });
                    console.log(`订单 ${i + 1} 创建成功:`, result);
                } catch (error) {
                    results.push({ success: false, order, error: error.message });
                    console.error(`订单 ${i + 1} 创建失败:`, error);
                }
            }
            
            this.showBatchResults(results);
        },
        
        getSelectedOrders(orders) {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            return Array.from(checkboxes).map(cb => {
                const index = parseInt(cb.id.replace('order-', ''));
                return orders[index];
            });
        },
        
        showBatchResults(results) {
            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            
            alert(`批量创建完成！\n成功: ${successful}\n失败: ${failed}`);
            
            if (failed === 0) {
                this.hideMultiOrderMode();
            }
        },
        
        hideMultiOrderMode() {
            if (this.elements.multiOrderPanel) {
                this.elements.multiOrderPanel.classList.add('hidden');
            }
            
            if (this.elements.workspace) {
                this.elements.workspace.classList.remove('hidden');
            }
            
            console.log('✅ 已返回主界面');
        },
        
        // 工具函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    },
    
    // 初始化函数 - 简单直接
    async init() {
        console.log('🚀 OTA核心系统启动中...');
        
        try {
            // 初始化UI
            this.ui.init();
            
            // 加载核心数据
            await this.loadCoreData();
            
            console.log('✅ OTA核心系统启动完成');
            
        } catch (error) {
            console.error('❌ OTA系统启动失败:', error);
            throw error;
        }
    },
    
    async loadCoreData() {
        try {
            const coreData = await this.api.getCoreData();
            
            // 缓存核心数据
            this.coreData = coreData;
            
            // 填充下拉菜单
            this.populateDropdowns(coreData);
            
            console.log('✅ 核心数据加载完成');
            
        } catch (error) {
            console.error('核心数据加载失败:', error);
            // 不阻断启动，使用本地数据
        }
    },
    
    populateDropdowns(data) {
        // 填充服务类型
        const subCategorySelect = document.getElementById('subCategoryId');
        if (subCategorySelect && data.subCategories) {
            subCategorySelect.innerHTML = '<option value="">请选择服务类型</option>' +
                data.subCategories.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
        }
        
        // 填充车型
        const carTypeSelect = document.getElementById('carTypeId');
        if (carTypeSelect && data.carTypes) {
            carTypeSelect.innerHTML = '<option value="">请选择车型</option>' +
                data.carTypes.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
        }
        
        // 填充区域
        const regionSelect = document.getElementById('drivingRegionId');
        if (regionSelect && data.regions) {
            regionSelect.innerHTML = '<option value="">请选择行驶区域</option>' +
                data.regions.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
        }
    }
};

// 兼容性支持 - 保持现有接口工作
window.OTA = window.OTA || {};
window.OTA.core = window.ota;

// DOM加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.ota.init());
} else {
    window.ota.init();
}

console.log('✅ OTA核心模块已加载 - Linus Torvalds式重构版本');