/**
 * @OTA_TESTS 字段映射测试套件
 * 🏷️ 标签: @OTA_FIELD_MAPPING_TESTS
 * 📝 说明: 验证字段映射的完整性和正确性，确保重构后映射逻辑无误
 * ⚠️ 警告: 仅用于测试，不要在生产环境中运行
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.FieldMappingTests) {
    console.log('字段映射测试套件已存在，跳过重复加载');
} else {

/**
 * 字段映射测试套件类
 * 提供各种字段映射测试功能
 */
class FieldMappingTests {
    
    constructor() {
        this.testResults = [];
        this.logger = this.getLogger();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 运行所有字段映射测试
     * @returns {Object} 测试结果汇总
     */
    runAllTests() {
        console.group('🧪 开始运行字段映射测试套件');
        this.testResults = [];

        try {
            // 1. 测试配置加载
            this.testConfigLoading();

            // 2. 测试AI到前端字段映射
            this.testAIToFrontendMapping();

            // 3. 测试前端到API字段映射
            this.testFrontendToAPIMapping();

            // 4. 测试备用字段映射
            this.testAlternativeFieldMapping();

            // 5. 测试languages_id_array格式转换
            this.testLanguagesIdArrayConversion();

            // 6. 测试字段验证
            this.testFieldValidation();

            // 7. 测试真实订单数据
            this.testRealOrderData();

            // 生成测试报告
            const summary = this.generateTestSummary();
            console.log('📊 测试结果汇总:', summary);
            console.groupEnd();

            return summary;

        } catch (error) {
            console.error('❌ 测试套件执行失败:', error);
            console.groupEnd();
            return {
                success: false,
                error: error.message,
                totalTests: this.testResults.length,
                passedTests: this.testResults.filter(t => t.passed).length,
                failedTests: this.testResults.filter(t => !t.passed).length
            };
        }
    }

    /**
     * 测试配置加载
     */
    testConfigLoading() {
        const testName = '配置加载测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
            
            if (!config) {
                throw new Error('字段映射配置未加载');
            }

            // 检查必要的配置项
            const requiredConfigs = [
                'AI_TO_FRONTEND',
                'FRONTEND_TO_API', 
                'ALTERNATIVE_FIELDS',
                'REQUIRED_API_FIELDS',
                'REQUIRED_FRONTEND_FIELDS'
            ];

            for (const configKey of requiredConfigs) {
                if (!config[configKey]) {
                    throw new Error(`缺少配置项: ${configKey}`);
                }
            }

            this.addTestResult(testName, true, '所有配置项加载正常');

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试AI到前端字段映射
     */
    testAIToFrontendMapping() {
        const testName = 'AI到前端字段映射测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
            
            // 模拟AI返回的数据（snake_case）
            const aiData = {
                customer_name: '张三',
                customer_contact: '+60123456789',
                pickup_location: '吉隆坡国际机场',
                dropoff_location: '双子塔',
                pickup_date: '2024-01-15',
                pickup_time: '10:30',
                passenger_count: 2,
                luggage_count: 3,
                ota_price: 150.00,
                languages_id_array: [4, 2]
            };

            // 应用映射
            const frontendData = {};
            Object.entries(config.AI_TO_FRONTEND).forEach(([aiField, frontendField]) => {
                if (aiData.hasOwnProperty(aiField)) {
                    frontendData[frontendField] = aiData[aiField];
                }
            });

            // 验证映射结果
            const expectedMappings = [
                ['customer_name', 'customerName', '张三'],
                ['pickup_location', 'pickup', '吉隆坡国际机场'],
                ['dropoff_location', 'dropoff', '双子塔'],
                ['passenger_count', 'passengerCount', 2]
            ];

            for (const [aiField, frontendField, expectedValue] of expectedMappings) {
                if (frontendData[frontendField] !== expectedValue) {
                    throw new Error(`字段映射失败: ${aiField} → ${frontendField}, 期望: ${expectedValue}, 实际: ${frontendData[frontendField]}`);
                }
            }

            this.addTestResult(testName, true, `成功映射 ${Object.keys(frontendData).length} 个字段`);

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试前端到API字段映射
     */
    testFrontendToAPIMapping() {
        const testName = '前端到API字段映射测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
            
            // 模拟前端数据（camelCase）
            const frontendData = {
                customerName: '李四',
                customerContact: '+60123456789',
                pickup: '吉隆坡国际机场',
                dropoff: '双子塔',
                pickupDate: '2024-01-15',
                pickupTime: '10:30',
                passengerCount: 2,
                luggageCount: 3,
                otaPrice: 150.00
            };

            // 应用映射
            const apiData = {};
            Object.entries(config.FRONTEND_TO_API).forEach(([frontendField, apiField]) => {
                if (frontendData.hasOwnProperty(frontendField)) {
                    apiData[apiField] = frontendData[frontendField];
                }
            });

            // 验证关键字段映射
            const expectedMappings = [
                ['pickup', 'pickup_location', '吉隆坡国际机场'],
                ['dropoff', 'dropoff_location', '双子塔'],
                ['luggageCount', 'luggage_number', 3], // 注意：API使用luggage_number
                ['customerName', 'customer_name', '李四']
            ];

            for (const [frontendField, apiField, expectedValue] of expectedMappings) {
                if (apiData[apiField] !== expectedValue) {
                    throw new Error(`字段映射失败: ${frontendField} → ${apiField}, 期望: ${expectedValue}, 实际: ${apiData[apiField]}`);
                }
            }

            this.addTestResult(testName, true, `成功映射 ${Object.keys(apiData).length} 个字段`);

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试备用字段映射
     */
    testAlternativeFieldMapping() {
        const testName = '备用字段映射测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
            
            // 模拟缺少主字段但有备用字段的数据
            const orderData = {
                pickupLocation: '吉隆坡国际机场', // 备用字段
                dropoffLocation: '双子塔', // 备用字段
                phone: '+60123456789', // 备用字段
                ota_price: 150.00 // 备用字段
            };

            // 应用备用字段映射
            Object.entries(config.ALTERNATIVE_FIELDS).forEach(([primaryField, alternatives]) => {
                if (!orderData[primaryField]) {
                    for (const altField of alternatives) {
                        if (orderData[altField]) {
                            orderData[primaryField] = orderData[altField];
                            break;
                        }
                    }
                }
            });

            // 验证映射结果
            const expectedMappings = [
                ['pickup', '吉隆坡国际机场'],
                ['dropoff', '双子塔'],
                ['customerContact', '+60123456789'],
                ['price', 150.00]
            ];

            for (const [field, expectedValue] of expectedMappings) {
                if (orderData[field] !== expectedValue) {
                    throw new Error(`备用字段映射失败: ${field}, 期望: ${expectedValue}, 实际: ${orderData[field]}`);
                }
            }

            this.addTestResult(testName, true, '备用字段映射正常');

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试languages_id_array格式转换
     */
    testLanguagesIdArrayConversion() {
        const testName = 'languages_id_array格式转换测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const validator = window.OTA?.FieldMappingValidator || window.FieldMappingValidator;
            if (!validator) {
                throw new Error('字段映射验证器未加载');
            }

            // 测试数组格式转换为对象格式
            const arrayFormat = [2, 4, 3];
            const result = validator.validateLanguagesIdArrayFormat(arrayFormat);

            if (!result.isValid) {
                throw new Error(`数组格式转换失败: ${result.message}`);
            }

            const expectedObjectFormat = {"0": "2", "1": "4", "2": "3"};
            const actualObjectFormat = result.converted;

            // 验证转换结果
            Object.keys(expectedObjectFormat).forEach(key => {
                if (actualObjectFormat[key] !== expectedObjectFormat[key]) {
                    throw new Error(`对象格式不正确: 键 ${key}, 期望: ${expectedObjectFormat[key]}, 实际: ${actualObjectFormat[key]}`);
                }
            });

            this.addTestResult(testName, true, 'languages_id_array格式转换正常');

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试字段验证
     */
    testFieldValidation() {
        const testName = '字段验证测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            const validator = window.OTA?.FieldMappingValidator || window.FieldMappingValidator;
            if (!validator) {
                throw new Error('字段映射验证器未加载');
            }

            // 测试完整的API字段数据
            const completeApiData = {
                pickup_location: '吉隆坡国际机场',
                dropoff_location: '双子塔',
                pickup_date: '2024-01-15',
                pickup_time: '10:30',
                customer_name: '王五',
                ota_reference_number: 'REF123456',
                ota_price: 150.00,
                sub_category_id: 2,
                car_type_id: 5,
                driving_region_id: 1,
                languages_id_array: {"0": "2", "1": "4"}
            };

            const validationResult = validator.validateApiFields(completeApiData);
            
            if (!validationResult.isValid) {
                throw new Error(`字段验证失败: 缺少字段 ${validationResult.missingFields.join(', ')}`);
            }

            // 测试不完整的数据
            const incompleteData = {
                pickup_location: '吉隆坡国际机场'
                // 缺少其他必填字段
            };

            const incompleteValidation = validator.validateApiFields(incompleteData);
            
            if (incompleteValidation.isValid) {
                throw new Error('不完整数据验证应该失败，但却通过了');
            }

            this.addTestResult(testName, true, '字段验证功能正常');

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 测试真实订单数据
     */
    testRealOrderData() {
        const testName = '真实订单数据测试';
        console.log(`🧪 运行测试: ${testName}`);

        try {
            // 模拟真实的Gemini AI返回数据
            const realOrderData = {
                customer_name: "陈先生",
                customer_contact: "+60123456789",
                pickup_location: "吉隆坡国际机场 (KLIA)",
                dropoff_location: "Pavilion Kuala Lumpur",
                pickup_date: "2024-01-20",
                pickup_time: "14:30",
                passenger_count: 3,
                luggage_count: 2,
                ota_price: 180.50,
                ota_reference_number: "TRIP789012",
                sub_category_id: 2,
                car_type_id: 15,
                driving_region_id: 1,
                languages_id_array: [4, 2],
                extra_requirement: "需要儿童座椅",
                flight_info: "MH370"
            };

            // 测试完整的数据转换流程
            const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
            const validator = window.OTA?.FieldMappingValidator || window.FieldMappingValidator;

            // 1. AI到前端映射
            const frontendData = {};
            Object.entries(config.AI_TO_FRONTEND).forEach(([aiField, frontendField]) => {
                if (realOrderData.hasOwnProperty(aiField)) {
                    frontendData[frontendField] = realOrderData[aiField];
                }
            });

            // 2. 前端到API映射
            const apiData = {};
            Object.entries(config.FRONTEND_TO_API).forEach(([frontendField, apiField]) => {
                if (frontendData.hasOwnProperty(frontendField)) {
                    apiData[apiField] = frontendData[frontendField];
                }
            });

            // 3. 处理languages_id_array格式
            if (apiData.languages_id_array) {
                const languageResult = validator.validateLanguagesIdArrayFormat(apiData.languages_id_array);
                apiData.languages_id_array = languageResult.converted;
            }

            // 4. 验证最终API数据
            const finalValidation = validator.validateApiFields(apiData);
            
            if (!finalValidation.isValid) {
                throw new Error(`最终API数据验证失败: ${finalValidation.missingFields.join(', ')}`);
            }

            this.addTestResult(testName, true, '真实订单数据转换流程正常');

        } catch (error) {
            this.addTestResult(testName, false, error.message);
        }
    }

    /**
     * 添加测试结果
     * @param {string} testName - 测试名称
     * @param {boolean} passed - 是否通过
     * @param {string} message - 测试消息
     */
    addTestResult(testName, passed, message) {
        const result = {
            testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        };

        this.testResults.push(result);

        if (passed) {
            console.log(`✅ ${testName}: ${message}`);
        } else {
            console.error(`❌ ${testName}: ${message}`);
        }
    }

    /**
     * 生成测试汇总
     * @returns {Object} 测试汇总结果
     */
    generateTestSummary() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(t => t.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0;

        const summary = {
            success: failedTests === 0,
            totalTests,
            passedTests,
            failedTests,
            successRate: `${successRate}%`,
            details: this.testResults,
            timestamp: new Date().toISOString()
        };

        // 记录到日志
        this.logger?.log('字段映射测试完成', failedTests === 0 ? 'success' : 'error', summary);

        return summary;
    }

    /**
     * 快速测试（仅运行关键测试）
     * @returns {Object} 快速测试结果
     */
    runQuickTests() {
        console.log('🚀 运行快速字段映射测试...');
        this.testResults = [];

        this.testConfigLoading();
        this.testLanguagesIdArrayConversion();
        this.testRealOrderData();

        return this.generateTestSummary();
    }
}

// 导出测试套件
window.OTA = window.OTA || {};
window.OTA.FieldMappingTests = FieldMappingTests;

// 向后兼容
window.FieldMappingTests = FieldMappingTests;

// 提供全局测试函数
window.runFieldMappingTests = function() {
    const tests = new FieldMappingTests();
    return tests.runAllTests();
};

window.runQuickFieldMappingTests = function() {
    const tests = new FieldMappingTests();
    return tests.runQuickTests();
};

console.log('✅ 字段映射测试套件已加载');
console.log('💡 使用 runFieldMappingTests() 运行完整测试');
console.log('💡 使用 runQuickFieldMappingTests() 运行快速测试');

// 结束防重复加载检查
}
