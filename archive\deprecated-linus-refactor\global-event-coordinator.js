/**
 * 全局事件协调器
 * 统一管理所有全局事件，避免重复监听器和事件冲突
 * 解决多选组件等UI组件的事件管理问题
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 全局事件协调器类
     */
    class GlobalEventCoordinator {
        constructor() {
            this.listeners = new Map(); // 存储事件监听器
            this.components = new Map(); // 存储注册的组件
            this.initialized = false;
            this.logger = null; // 延迟获取logger避免循环依赖
        }

        /**
         * 初始化全局事件协调器
         */
        init() {
            if (this.initialized) {
                this.log('全局事件协调器已经初始化', 'debug');
                return;
            }

            this.setupGlobalListeners();
            this.initialized = true;
            this.log('全局事件协调器已初始化', 'info');
        }

        /**
         * 设置全局事件监听器
         */
        setupGlobalListeners() {
            // 全局点击事件处理
            document.addEventListener('click', (e) => {
                this.handleGlobalClick(e);
            }, { passive: false, capture: true });

            // 键盘事件处理
            document.addEventListener('keydown', (e) => {
                this.handleGlobalKeydown(e);
            }, { passive: false, capture: true });

            // 窗口大小变化事件
            window.addEventListener('resize', () => {
                this.handleGlobalResize();
            }, { passive: true });

            // 页面滚动事件
            window.addEventListener('scroll', () => {
                this.handleGlobalScroll();
            }, { passive: true });

            this.log('全局事件监听器已设置', 'info');
        }

        /**
         * 注册UI组件
         * @param {string} componentId - 组件ID
         * @param {Object} component - 组件实例
         * @param {Object} options - 选项
         */
        registerComponent(componentId, component, options = {}) {
            const registration = {
                component,
                options,
                registeredAt: Date.now(),
                ...options
            };

            this.components.set(componentId, registration);
            this.log(`已注册组件: ${componentId}`, 'info', { options });
        }

        /**
         * 注销组件
         * @param {string} componentId - 组件ID
         */
        unregisterComponent(componentId) {
            if (this.components.delete(componentId)) {
                this.log(`已注销组件: ${componentId}`, 'info');
            }
        }

        /**
         * 处理全局点击事件
         * @param {Event} e - 事件对象
         */
        handleGlobalClick(e) {
            // 检查是否需要关闭下拉菜单组件
            this.handleDropdownClose(e);
            
            // 处理其他全局点击逻辑
            this.handleModalClose(e);
        }

        /**
         * 处理下拉菜单关闭逻辑
         * @param {Event} e - 事件对象
         */
        handleDropdownClose(e) {
            // 修复：排除字段编辑器，避免干扰下拉菜单编辑功能
            if (e.target.classList.contains('field-editor') ||
                e.target.tagName === 'SELECT' ||
                e.target.closest('.editable-field[data-editing="true"]')) {
                // 如果点击的是字段编辑器，不处理下拉菜单关闭逻辑
                return;
            }

            const dropdownComponents = Array.from(this.components.entries())
                .filter(([id, reg]) => reg.options.type === 'dropdown');

            for (const [componentId, registration] of dropdownComponents) {
                const component = registration.component;
                const container = component.container || document.getElementById(componentId);

                if (container && !container.contains(e.target)) {
                    // 点击在组件外部，检查是否需要关闭
                    if (component.isOpen && typeof component.close === 'function') {
                        // 检查是否有阻止关闭的条件
                        if (!registration.options.preventAutoClose) {
                            component.close();
                            this.log(`自动关闭下拉组件: ${componentId}`, 'debug');
                        }
                    }
                }
            }
        }

        /**
         * 处理模态框关闭逻辑
         * @param {Event} e - 事件对象
         */
        handleModalClose(e) {
            const modalComponents = Array.from(this.components.entries())
                .filter(([id, reg]) => reg.options.type === 'modal');

            for (const [componentId, registration] of modalComponents) {
                const component = registration.component;
                
                if (component.isVisible && typeof component.close === 'function') {
                    // 检查点击是否在遮罩层上（而非内容区域）
                    if (e.target.classList.contains('modal-overlay') || 
                        e.target.classList.contains('modal-backdrop')) {
                        if (!registration.options.preventBackdropClose) {
                            component.close();
                            this.log(`点击遮罩关闭模态框: ${componentId}`, 'debug');
                        }
                    }
                }
            }
        }

        /**
         * 处理全局键盘事件
         * @param {Event} e - 事件对象
         */
        handleGlobalKeydown(e) {
            // ESC键处理
            if (e.key === 'Escape') {
                this.handleEscapeKey(e);
            }

            // Tab键处理
            if (e.key === 'Tab') {
                this.handleTabKey(e);
            }
        }

        /**
         * 处理ESC键
         * @param {Event} e - 事件对象
         */
        handleEscapeKey(e) {
            // 按优先级关闭组件：模态框 > 下拉菜单 > 其他
            const priorities = ['modal', 'dropdown', 'tooltip', 'other'];

            for (const priority of priorities) {
                const components = Array.from(this.components.entries())
                    .filter(([id, reg]) => reg.options.type === priority)
                    .filter(([id, reg]) => {
                        const component = reg.component;
                        return component.isOpen || component.isVisible;
                    });

                if (components.length > 0) {
                    // 关闭最新打开的组件
                    const latest = components.sort((a, b) => 
                        (b[1].lastOpenedAt || 0) - (a[1].lastOpenedAt || 0)
                    )[0];

                    const component = latest[1].component;
                    if (typeof component.close === 'function') {
                        component.close();
                        e.preventDefault();
                        e.stopPropagation();
                        this.log(`ESC键关闭组件: ${latest[0]}`, 'debug');
                        return;
                    }
                }
            }
        }

        /**
         * 处理Tab键导航
         * @param {Event} e - 事件对象
         */
        handleTabKey(e) {
            // 处理组件内的焦点管理
            for (const [componentId, registration] of this.components.entries()) {
                const component = registration.component;
                
                if ((component.isOpen || component.isVisible) && 
                    typeof component.handleTabNavigation === 'function') {
                    if (component.handleTabNavigation(e)) {
                        // 组件已处理Tab导航
                        return;
                    }
                }
            }
        }

        /**
         * 处理全局resize事件
         */
        handleGlobalResize() {
            for (const [componentId, registration] of this.components.entries()) {
                const component = registration.component;
                
                if (typeof component.onResize === 'function') {
                    component.onResize();
                }
            }
        }

        /**
         * 处理全局scroll事件
         */
        handleGlobalScroll() {
            for (const [componentId, registration] of this.components.entries()) {
                const component = registration.component;
                
                if (typeof component.onScroll === 'function') {
                    component.onScroll();
                }
            }
        }

        /**
         * 更新组件状态
         * @param {string} componentId - 组件ID
         * @param {Object} state - 状态更新
         */
        updateComponentState(componentId, state) {
            const registration = this.components.get(componentId);
            if (registration) {
                Object.assign(registration, state);
                
                // 记录打开时间
                if (state.isOpen || state.isVisible) {
                    registration.lastOpenedAt = Date.now();
                }
            }
        }

        /**
         * 获取组件状态
         * @param {string} componentId - 组件ID
         * @returns {Object|null} 组件状态
         */
        getComponentState(componentId) {
            const registration = this.components.get(componentId);
            return registration || null;
        }

        /**
         * 获取所有注册的组件
         * @returns {Array} 组件列表
         */
        getAllComponents() {
            return Array.from(this.components.entries()).map(([id, reg]) => ({
                id,
                type: reg.options.type,
                isActive: reg.component.isOpen || reg.component.isVisible || false,
                registeredAt: reg.registeredAt
            }));
        }

        /**
         * 销毁协调器
         */
        destroy() {
            // 清理所有事件监听器
            document.removeEventListener('click', this.handleGlobalClick);
            document.removeEventListener('keydown', this.handleGlobalKeydown);
            window.removeEventListener('resize', this.handleGlobalResize);
            window.removeEventListener('scroll', this.handleGlobalScroll);

            // 清理所有组件注册
            this.components.clear();
            this.listeners.clear();
            this.initialized = false;

            this.log('全局事件协调器已销毁', 'info');
        }

        /**
         * 日志输出
         * @param {string} message - 消息
         * @param {string} level - 级别
         * @param {Object} data - 数据
         */
        log(message, level = 'info', data = null) {
            if (!this.logger) {
                try {
                    this.logger = getLogger ? getLogger() : null;
                } catch (e) {
                    // Logger可能还未初始化
                }
            }

            if (this.logger) {
                this.logger.log(`[EventCoordinator] ${message}`, level, data);
            } else {
                console.log(`[EventCoordinator] ${message}`, data);
            }
        }
    }

    // 创建全局实例
    const globalEventCoordinator = new GlobalEventCoordinator();

    // 导出到OTA命名空间
    window.OTA.GlobalEventCoordinator = GlobalEventCoordinator;
    window.OTA.globalEventCoordinator = globalEventCoordinator;

    // 向后兼容
    window.GlobalEventCoordinator = GlobalEventCoordinator;
    window.globalEventCoordinator = globalEventCoordinator;

    // 🔧 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('eventCoordinator', () => globalEventCoordinator, {
                singleton: true
            });
        } catch (error) {
            console.warn('[GlobalEventCoordinator] 注册到依赖容器失败:', error.message);
        }
    }

})();