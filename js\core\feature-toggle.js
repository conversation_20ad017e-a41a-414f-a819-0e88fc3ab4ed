/**
 * 依赖标签（Dependency Tags）
 * 文件: js/core/feature-toggle.js
 * 角色: 功能开关（按环境/渠道/用户启用或禁用功能）
 * 上游依赖(直接使用): Logger, AppState
 * 下游被依赖(常见调用方): UI/Managers/Shadow/Strategies
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * 特性开关机制 - 功能连续性保障核心
 * 
 * 设计目标：
 * - 支持运行时功能切换，确保零中断
 * - 提供A/B测试能力
 * - 支持渐进式功能发布
 * - 提供即时回滚能力
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 特性开关管理器
     * 控制系统功能的动态切换
     */
    class FeatureToggle {
        constructor() {
            this.features = new Map();
            this.listeners = new Map();
            this.config = this.loadConfig();
            this.logger = this.getLogger();
            
            // 初始化默认特性开关
            this.initializeDefaultFeatures();
            
            this.logger.log('特性开关管理器已初始化');
        }

        /**
         * 加载配置
         * @returns {Object} 配置对象
         */
        loadConfig() {
            try {
                // 优先从window.OTA.config加载
                if (window.OTA && window.OTA.config && window.OTA.config.features) {
                    return window.OTA.config.features;
                }
                
                // 从localStorage加载
                const stored = localStorage.getItem('ota-feature-toggles');
                if (stored) {
                    return JSON.parse(stored);
                }
                
                // 默认配置
                return {};
            } catch (error) {
                console.warn('Failed to load feature toggle config:', error);
                return {};
            }
        }

        /**
         * 保存配置
         */
        saveConfig() {
            try {
                const config = {};
                this.features.forEach((value, key) => {
                    config[key] = value;
                });
                
                localStorage.setItem('ota-feature-toggles', JSON.stringify(config));
                
                // 同步到全局配置
                if (window.OTA) {
                    window.OTA.config = window.OTA.config || {};
                    window.OTA.config.features = config;
                }
            } catch (error) {
                console.warn('Failed to save feature toggle config:', error);
            }
        }

        /**
         * 初始化默认特性开关
         */
        initializeDefaultFeatures() {
            const defaultFeatures = {
                // OTA系统相关
                'enableShadowOTAManager': false,
                'enableShadowFliggyProcessor': false,
                'enableEnhancedFliggyStrategy': false,
                'enableFieldPromptSnippets': false,
                
                // 调试和开发
                'enableDebugMode': false,
                'enablePerformanceMonitoring': false,
                'enableErrorReporting': true,
                
                // 实验性功能
                'enableExperimentalFeatures': false,
                'enableBetaFeatures': false,
                
                // 回滚控制
                'enableEmergencyRollback': false,
                'enableSafeMode': false,

                // 诊断/验证/影子部署
                'enableInterfaceValidation': false,
                'enableShadowDeployment': false,
                'enableDuplicateChecker': false
            };

            // 设置默认值（不覆盖已有配置）
            Object.entries(defaultFeatures).forEach(([key, defaultValue]) => {
                if (!this.features.has(key)) {
                    const configValue = this.config[key];
                    this.features.set(key, configValue !== undefined ? configValue : defaultValue);
                }
            });
        }

        /**
         * 检查特性是否启用
         * @param {string} featureName - 特性名称
         * @returns {boolean} 是否启用
         */
        isEnabled(featureName) {
            return this.features.get(featureName) || false;
        }

        /**
         * 启用特性
         * @param {string} featureName - 特性名称
         * @param {boolean} save - 是否保存到配置
         */
        enable(featureName, save = true) {
            const oldValue = this.features.get(featureName);
            this.features.set(featureName, true);
            
            if (save) {
                this.saveConfig();
            }
            
            this.notifyListeners(featureName, true, oldValue);
            this.logger.log(`特性已启用: ${featureName}`);
        }

        /**
         * 禁用特性
         * @param {string} featureName - 特性名称
         * @param {boolean} save - 是否保存到配置
         */
        disable(featureName, save = true) {
            const oldValue = this.features.get(featureName);
            this.features.set(featureName, false);
            
            if (save) {
                this.saveConfig();
            }
            
            this.notifyListeners(featureName, false, oldValue);
            this.logger.log(`特性已禁用: ${featureName}`);
        }

        /**
         * 切换特性状态
         * @param {string} featureName - 特性名称
         * @param {boolean} save - 是否保存到配置
         * @returns {boolean} 新状态
         */
        toggle(featureName, save = true) {
            const currentState = this.isEnabled(featureName);
            if (currentState) {
                this.disable(featureName, save);
            } else {
                this.enable(featureName, save);
            }
            return !currentState;
        }

        /**
         * 批量设置特性
         * @param {Object} features - 特性配置对象
         * @param {boolean} save - 是否保存到配置
         */
        setFeatures(features, save = true) {
            Object.entries(features).forEach(([name, enabled]) => {
                const oldValue = this.features.get(name);
                this.features.set(name, enabled);
                this.notifyListeners(name, enabled, oldValue);
            });
            
            if (save) {
                this.saveConfig();
            }
            
            this.logger.log('批量设置特性完成', 'info', features);
        }

        /**
         * 添加特性变更监听器
         * @param {string} featureName - 特性名称
         * @param {Function} listener - 监听器函数
         */
        addListener(featureName, listener) {
            if (!this.listeners.has(featureName)) {
                this.listeners.set(featureName, []);
            }
            this.listeners.get(featureName).push(listener);
        }

        /**
         * 移除特性变更监听器
         * @param {string} featureName - 特性名称
         * @param {Function} listener - 监听器函数
         */
        removeListener(featureName, listener) {
            const listeners = this.listeners.get(featureName) || [];
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }

        /**
         * 通知监听器
         * @param {string} featureName - 特性名称
         * @param {boolean} newValue - 新值
         * @param {boolean} oldValue - 旧值
         */
        notifyListeners(featureName, newValue, oldValue) {
            const listeners = this.listeners.get(featureName) || [];
            listeners.forEach(listener => {
                try {
                    listener(newValue, oldValue, featureName);
                } catch (error) {
                    console.error(`Feature toggle listener error for ${featureName}:`, error);
                }
            });
        }

        /**
         * 获取所有特性状态
         * @returns {Object} 特性状态对象
         */
        getAllFeatures() {
            const result = {};
            this.features.forEach((value, key) => {
                result[key] = value;
            });
            return result;
        }

        /**
         * 重置所有特性到默认状态
         */
        reset() {
            this.features.clear();
            this.config = {};
            this.initializeDefaultFeatures();
            this.saveConfig();
            this.logger.log('特性开关已重置到默认状态');
        }

        /**
         * 启用安全模式（禁用所有实验性功能）
         */
        enableSafeMode() {
            const safeFeatures = {
                'enableShadowOTAManager': false,
                'enableShadowFliggyProcessor': false,
                'enableEnhancedFliggyStrategy': false,
                'enableExperimentalFeatures': false,
                'enableBetaFeatures': false,
                'enableSafeMode': true
            };
            
            this.setFeatures(safeFeatures);
            this.logger.log('安全模式已启用');
        }

        /**
         * 紧急回滚（立即禁用所有新功能）
         */
        emergencyRollback() {
            const rollbackFeatures = {
                'enableShadowOTAManager': false,
                'enableShadowFliggyProcessor': false,
                'enableEnhancedFliggyStrategy': false,
                'enableExperimentalFeatures': false,
                'enableBetaFeatures': false,
                'enableEmergencyRollback': true,
                'enableSafeMode': true
            };
            
            this.setFeatures(rollbackFeatures);
            this.logger.log('紧急回滚已执行', 'warn');
            
            // 触发全局回滚事件
            if (window.OTA && window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.emit('emergency-rollback', {
                    timestamp: Date.now(),
                    features: rollbackFeatures
                });
            }
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[FeatureToggle][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[FeatureToggle][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局实例
    const featureToggle = new FeatureToggle();

    // 暴露到OTA命名空间
    window.OTA.core.FeatureToggle = FeatureToggle;
    window.OTA.featureToggle = featureToggle;

    // 提供便捷的全局函数
    window.OTA.isFeatureEnabled = function(featureName) {
        return featureToggle.isEnabled(featureName);
    };

    window.OTA.enableFeature = function(featureName) {
        return featureToggle.enable(featureName);
    };

    window.OTA.disableFeature = function(featureName) {
        return featureToggle.disable(featureName);
    };

    console.log('✅ 特性开关机制已加载');

})();
