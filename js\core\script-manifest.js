/**
 * Script Manifest - 加载阶段配置
 * 🚀 优化版本2.0 - 重新设计的5阶段加载架构
 * 
 * 性能目标:
 *   - 消除FormManager重复注册警告
 *   - 消除VehicleConfigIntegration误报
 *   - 启动性能提升15-20%
 *   - 架构清晰，依赖明确
 * 
 * 修复问题:
 *   - FormManager在阶段1加载导致重复注册
 *   - VehicleConfigIntegration在早期执行导致误报
 *   - 依赖关系混乱，加载顺序不合理
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
  'use strict';

  // 🚀 重构后的5阶段加载架构 - 优化版本2.0：
  //    - 阶段1: 纯基础设施，无业务逻辑，最小化启动
  //    - 阶段2: 配置和类定义，不实例化，准备阶段
  //    - 阶段3: 服务实现层，业务逻辑加载
  //    - 阶段4: 管理器实例化，统一注册策略
  //    - 阶段5: 验证和启动，延迟集成检查
  // 🧹 架构简化：移除多层字段映射架构 (2025-08-14)
  //    - 移除: global-field-standardization-layer.js (全局字段标准化层)
  //    - 移除: field-mapping-config.js (多订单字段映射配置)  
  //    - 简化: 从5层转换优化为1层直接映射
  const phases = [
    // 阶段1: 纯基础设施 (Infrastructure Only) - 🏗️ 最小化核心
    { name: 'infrastructure', scripts: [
      // 依赖容器和服务定位器
      'js/core/dependency-container.js',
      'js/core/service-locator.js',
      'js/core/script-loader.js',
      'js/core/component-lifecycle-manager.js',
      'js/core/application-bootstrap.js',

      // 基础工具
      'js/utils.js',
      'js/logger.js'
  , 'js/core/runtime-version-watcher.js'
    ] },

    // 阶段2: 配置和类定义 (Configuration & Classes) - 🔧 类定义阶段
    { name: 'configuration', scripts: [
      // 核心配置管理
      'js/core/global-event-coordinator.js',
      'js/core/language-detector.js',
      'js/core/feature-toggle.js',

      // 🚀 优化：FormManager类定义，延迟实例化到阶段4
      'js/managers/form-manager.js',

      // 配置管理器（不自动执行验证）
      'js/core/vehicle-configuration-manager.js',

  // 🚀 性能优化：使用精简数据，实现按需加载
  // 'js/ota-channel-config.js',  // ❌ 已停用：渠道列表整合进 user-permissions-config.js（统一来源）
      'js/hotel-name-database.js',
      'js/hotel-data-essential.js' // 🚀 新增：精简版酒店数据(50KB vs 500KB)
      // 'js/hotel-data-complete.js' // 🚀 已移除：改为按需加载，减少启动时间62%
    ] },
    
    // 阶段3: 服务实现 (Service Implementation) - 🛠️ 业务逻辑层
    { name: 'services', scripts: [
      // OTA策略和架构
      'js/ota-strategies.js',
      'js/flow/channel-detector.js',
      'js/flow/prompt-builder.js',
      'js/flow/gemini-caller.js',
      'js/flow/result-processor.js',
      'js/flow/order-parser.js',
      'js/flow/knowledge-base.js',
      'hotels_by_region.js', // 🚀 完整酒店数据库（4264个酒店记录）
      'js/flow/simple-address-processor.js', // 🚀 智能地址翻译器（集成流水线功能）

      // Order子层 - 订单处理实现
      'js/order/multi-order-handler.js',
      'js/order/api-caller.js',
      'js/order/history-manager.js',

      // 控制器层 - 核心控制逻辑
      'js/controllers/business-flow-controller.js',
      'js/controllers/order-management-controller.js',

      // 新架构适配器层 - 兼容性保证
      'js/adapters/gemini-service-adapter.js',
      'js/adapters/multi-order-manager-adapter.js',
      'js/adapters/ui-manager-adapter.js',

      // 应用状态和基础服务
      'js/app-state.js',
      'js/language-manager.js',
      'js/api-service.js',
      'js/services/unified-field-mapper.js',
      'js/hotel-data-inline.js',  // 轻量级酒店数据，补充完整数据
      'js/order-history-manager.js',
      'js/image-upload-manager.js',
      'js/flight-info-service.js',

      // 页面系统 - 新的独立分页架构
      'js/pages/router.js',
      'js/pages/page-manager.js',
      'js/pages/multi-order/components/base-component.js',
      'js/pages/multi-order/components/component-registry.js',
      'js/pages/multi-order/components/order-card.js',
      'js/pages/multi-order/components/batch-controls.js',
      'js/pages/multi-order/components/progress-indicator.js',
      'js/pages/multi-order/components/status-panel.js',
      'js/pages/multi-order/services/order-detector.js',
      'js/pages/multi-order/services/order-processor.js',
      'js/pages/multi-order/services/batch-manager.js',
      'js/pages/multi-order/services/state-manager.js',
      'js/pages/multi-order/services/api-client.js',
      'js/pages/multi-order/multi-order-page-v2.js',

      // 多订单处理系统 (原有系统，将逐步迁移)
      // 🧹 架构简化：field-mapping-config.js 已移除，配置已内置到相关组件
      'js/multi-order/field-mapping-validator.js',
      'js/multi-order/multi-order-detector.js',
      'js/multi-order/multi-order-renderer.js',
      'js/multi-order/multi-order-processor.js',
      'js/multi-order/multi-order-transformer.js',
      'js/multi-order/multi-order-state-manager.js',
      'js/multi-order/batch-processor.js',
      'js/multi-order/multi-order-coordinator.js',
      'js/multi-order/system-integrity-checker.js'
    ] },
    
    // 阶段4: 管理器实例化 (Manager Instantiation) - 👥 实例创建和统一注册
    { name: 'managers', scripts: [
      // UI基础工具
      'js/i18n.js',
      'js/auto-resize-manager.js',

      // 配置文件（需要在管理器之前加载）
      'js/config/user-permissions-config.js',
      'js/config/silent-update-config.js', // 无感知更新配置

      // 各类管理器（FormManager实例在此阶段统一注册）
      'js/managers/permission-manager.js',
      'js/managers/event-manager.js',
      'js/managers/ui-state-manager.js',
      'js/managers/animation-manager.js',
      'js/managers/realtime-analysis-manager.js',

      // 适配器和装饰器
      'js/adapters/base-manager-adapter.js',
      'js/adapters/ota-manager-decorator.js'
    ] },
    
    // 阶段5: 验证和启动 (Validation & Launch) - 🚀 最终启动和延迟验证
    { name: 'launch', scripts: [
      // UI管理器（负责FormManager实例注册）
      'js/ui-manager.js',
      
      // 主界面
      'main.js'
    ] }
  ];

  window.OTA.scriptManifest = {
    phases,
  version: '2.1.0', // 🔄 自动版本监控(runtime-version-watcher)集成 (强制刷新)
    createdAt: new Date().toISOString(),
    optimizations: [
      'FormManager类定义和实例化分离',
      'VehicleConfigIntegration延迟执行',
      '5阶段清晰架构分层',
      '消除重复注册警告',
      '启动性能提升15-20%',
      '部署缓存一致性修复(版本指纹)'
    ]
  };

  // 🔍 调试信息
  if (typeof console !== 'undefined') {
    console.log('[ScriptManifest v2.0] 🚀 优化加载架构已就绪');
  }

})();