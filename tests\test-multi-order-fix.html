<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单模式修复测试</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="js/pages/multi-order/styles/multi-order-page.css">
    <link rel="stylesheet" href="js/pages/multi-order/styles/components.css">
    <link rel="stylesheet" href="js/pages/multi-order/styles/responsive.css">
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        .test-button:hover { background: #005a8b; }
        .test-button:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .sample-orders {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .sample-orders h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .sample-order {
            background: white;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
            margin: 8px 0;
            font-size: 12px;
        }
        #multiOrderPanel { z-index: 1000; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 多订单模式修复测试</h1>
        <p>此测试页面用于验证多订单模式的修复情况，检查所有组件是否正常工作。</p>

        <!-- 系统状态检查 -->
        <div class="test-section">
            <h2>📊 系统状态检查</h2>
            <div id="systemStatus">
                <div>正在检查系统状态...</div>
            </div>
            <button class="test-button" onclick="checkSystemStatus()">重新检查</button>
        </div>

        <!-- 组件测试 -->
        <div class="test-section">
            <h2>🧩 组件测试</h2>
            <button class="test-button" onclick="testOrderCardComponent()">测试订单卡片</button>
            <button class="test-button" onclick="testBatchControlsComponent()">测试批量控件</button>
            <button class="test-button" onclick="testProgressIndicator()">测试进度指示器</button>
            <button class="test-button" onclick="testStatusPanel()">测试状态面板</button>
        </div>

        <!-- 多订单功能测试 -->
        <div class="test-section">
            <h2>📦 多订单功能测试</h2>
            <button class="test-button" onclick="showMultiOrderPanel()">显示多订单面板</button>
            <button class="test-button" onclick="hideMultiOrderPanel()">隐藏多订单面板</button>
            <button class="test-button" onclick="testWithSampleOrders()">使用示例订单测试</button>
            <button class="test-button" onclick="simulateMultiOrderFlow()">模拟完整流程</button>
            
            <div class="sample-orders">
                <h4>📋 示例订单数据</h4>
                <div class="sample-order">
                    订单1: 吉隆坡机场 → 市中心酒店 | 客户: 张三 | 时间: 2025-08-15 14:00
                </div>
                <div class="sample-order">
                    订单2: 市中心 → 购物中心 | 客户: 李四 | 时间: 2025-08-15 16:30
                </div>
                <div class="sample-order">
                    订单3: 酒店 → 机场 | 客户: 王五 | 时间: 2025-08-16 08:00
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="test-section">
            <h2>📝 实时日志</h2>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <!-- 多订单面板容器 (从原始 index.html 复制) -->
    <div id="multiOrderPanel" class="multi-order-panel hidden">
        <div class="multi-order-content">
            <div class="multi-order-header">
                <div class="header-left">
                    <button type="button" id="backToMainBtn" class="btn btn-header-back" title="返回主页">←返回主页</button>
                    <h3 data-i18n="multiOrder.title">🔢 多订单预览与编辑</h3>
                </div>
                <div class="multi-order-controls">
                    <div class="header-actions">
                        <button type="button" id="batchCreateBtn" class="btn btn-header-action" data-i18n="multiOrder.batchCreate">批量创建</button>
                        <button type="button" id="closeMultiOrderBtn" class="btn btn-icon btn-close" data-i18n-title="common.close" title="关闭">✕</button>
                    </div>
                </div>
            </div>

            <div class="batch-controls">
                <span class="batch-controls-label">批量设置:</span>
                <select id="batchOtaSelect" class="batch-dropdown-btn" title="选择OTA渠道" aria-label="选择OTA渠道">
                    <option value="">选择OTA渠道</option>
                </select>
                <span class="batch-info-text">选择后自动应用到订单</span>
            </div>

            <div class="multi-order-list" id="multiOrderList">
                <!-- 多订单项将在这里动态生成 -->
            </div>

            <div class="multi-order-footer">
                <div class="footer-actions-row">
                    <div class="footer-actions-left">
                        <button type="button" id="selectAllOrdersBtn" class="btn btn-footer btn-sm" data-i18n="multiOrder.selectAll">全选</button>
                        <button type="button" id="deselectAllOrdersBtn" class="btn btn-footer btn-sm" data-i18n="multiOrder.deselectAll">取消全选</button>
                        <button type="button" id="validateAllOrdersBtn" class="btn btn-footer btn-sm" data-i18n="multiOrder.validateAll">验证全部</button>
                    </div>
                    <div class="footer-actions-center">
                        <span id="selectedOrderCount" class="footer-count">已选择 0 个订单</span>
                    </div>
                    <div class="footer-actions-right">
                        <button type="button" id="createSelectedOrdersBtn" class="btn btn-footer-primary" data-i18n="multiOrder.createSelected">创建选中订单</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载所有必需脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>

    <script>
        // 测试日志系统
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : 
                                 type === 'success' ? '#51cf66' : 
                                 type === 'warning' ? '#ffd43b' : '#e2e8f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            log('日志已清空');
        }

        // 检查系统状态
        function checkSystemStatus() {
            log('开始检查系统状态...');
            const statusDiv = document.getElementById('systemStatus');
            const checks = [];

            // 检查基础命名空间
            checks.push({
                name: 'window.OTA 命名空间',
                status: !!window.OTA,
                details: window.OTA ? '已加载' : '未找到'
            });

            // 检查服务
            const services = ['StateManager', 'OrderDetector', 'OrderProcessor', 'BatchManager', 'ApiClient'];
            services.forEach(service => {
                const exists = !!(window.OTA?.Services?.[service]);
                checks.push({
                    name: `OTA.Services.${service}`,
                    status: exists,
                    details: exists ? '已注册' : '未找到'
                });
            });

            // 检查组件
            const components = ['BaseComponent', 'OrderCard', 'BatchControls', 'ProgressIndicator', 'StatusPanel'];
            components.forEach(component => {
                const exists = !!(window.OTA?.Components?.[component]);
                checks.push({
                    name: `OTA.Components.${component}`,
                    status: exists,
                    details: exists ? '已注册' : '未找到'
                });
            });

            // 检查页面
            const pageExists = !!(window.OTA?.Pages?.MultiOrderPageV2);
            checks.push({
                name: 'MultiOrderPageV2',
                status: pageExists,
                details: pageExists ? '已加载' : '未找到'
            });

            // 渲染结果
            statusDiv.innerHTML = checks.map(check => `
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span class="status-indicator ${check.status ? 'status-ok' : 'status-error'}"></span>
                    <span style="flex: 1;">${check.name}</span>
                    <span style="color: #666; font-size: 12px;">${check.details}</span>
                </div>
            `).join('');

            const totalChecks = checks.length;
            const passedChecks = checks.filter(c => c.status).length;
            log(`系统状态检查完成: ${passedChecks}/${totalChecks} 项通过`);
        }

        // 生成示例订单数据
        function generateSampleOrders() {
            return [
                {
                    id: 1,
                    pickup: "吉隆坡国际机场",
                    dropoff: "市中心酒店",
                    customerName: "张三",
                    customerEmail: "<EMAIL>",
                    customerContact: "+60123456789",
                    pickupDate: "2025-08-15",
                    pickupTime: "14:00",
                    passengerCount: 2,
                    luggageCount: 3,
                    extraRequirement: "需要儿童座椅",
                    otaPrice: 150.00,
                    currency: "MYR"
                },
                {
                    id: 2,
                    pickup: "市中心",
                    dropoff: "Pavilion购物中心",
                    customerName: "李四",
                    customerEmail: "<EMAIL>", 
                    customerContact: "+60123456790",
                    pickupDate: "2025-08-15",
                    pickupTime: "16:30",
                    passengerCount: 1,
                    luggageCount: 0,
                    extraRequirement: "",
                    otaPrice: 80.00,
                    currency: "MYR"
                },
                {
                    id: 3,
                    pickup: "酒店大堂",
                    dropoff: "吉隆坡国际机场",
                    customerName: "王五",
                    customerEmail: "<EMAIL>",
                    customerContact: "+60123456791", 
                    pickupDate: "2025-08-16",
                    pickupTime: "08:00",
                    passengerCount: 3,
                    luggageCount: 5,
                    extraRequirement: "航班 MH123",
                    otaPrice: 180.00,
                    currency: "MYR"
                }
            ];
        }

        // 显示多订单面板
        function showMultiOrderPanel() {
            log('尝试显示多订单面板...');
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.remove('hidden');
                log('多订单面板已显示', 'success');
            } else {
                log('错误: 找不到多订单面板元素', 'error');
            }
        }

        // 隐藏多订单面板
        function hideMultiOrderPanel() {
            log('隐藏多订单面板...');
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.add('hidden');
                log('多订单面板已隐藏', 'success');
            }
        }

        // 使用示例订单测试
        function testWithSampleOrders() {
            log('开始使用示例订单进行测试...');
            
            if (!window.OTA?.Pages?.MultiOrderPageV2) {
                log('错误: MultiOrderPageV2 未找到', 'error');
                return;
            }

            try {
                const sampleOrders = generateSampleOrders();
                const multiOrderPage = window.OTA.Pages.multiOrderPageV2;
                
                if (multiOrderPage) {
                    log(`使用 ${sampleOrders.length} 个示例订单进行测试...`);
                    
                    // 显示面板并传入订单数据
                    multiOrderPage.show({
                        orders: sampleOrders,
                        originalText: "示例多订单文本",
                        detectionMethod: "manual",
                        confidence: 1.0
                    }).then(() => {
                        log('示例订单测试成功完成', 'success');
                    }).catch(error => {
                        log(`示例订单测试失败: ${error.message}`, 'error');
                    });
                } else {
                    log('错误: multiOrderPageV2 实例未找到', 'error');
                }
            } catch (error) {
                log(`测试过程中发生错误: ${error.message}`, 'error');
            }
        }

        // 测试单个组件
        function testOrderCardComponent() {
            log('测试订单卡片组件...');
            if (window.OTA?.Components?.OrderCard) {
                log('OrderCard 组件可用', 'success');
                // 这里可以添加更详细的组件测试
            } else {
                log('OrderCard 组件未找到', 'error');
            }
        }

        function testBatchControlsComponent() {
            log('测试批量控件组件...');
            if (window.OTA?.Components?.BatchControls) {
                log('BatchControls 组件可用', 'success');
            } else {
                log('BatchControls 组件未找到', 'error');
            }
        }

        function testProgressIndicator() {
            log('测试进度指示器组件...');
            if (window.OTA?.Components?.ProgressIndicator) {
                log('ProgressIndicator 组件可用', 'success');
            } else {
                log('ProgressIndicator 组件未找到', 'error');
            }
        }

        function testStatusPanel() {
            log('测试状态面板组件...');
            if (window.OTA?.Components?.StatusPanel) {
                log('StatusPanel 组件可用', 'success');
            } else {
                log('StatusPanel 组件未找到', 'error');
            }
        }

        // 模拟完整多订单流程
        function simulateMultiOrderFlow() {
            log('开始模拟完整多订单流程...');
            
            // 步骤1: 显示面板
            showMultiOrderPanel();
            
            // 步骤2: 加载示例数据 (延迟执行以确保面板显示)
            setTimeout(() => {
                testWithSampleOrders();
            }, 500);
            
            log('完整流程模拟已启动', 'success');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('测试页面已加载');
            
            // 延迟检查系统状态，确保所有脚本都已加载
            setTimeout(() => {
                checkSystemStatus();
            }, 2000);

            // 绑定多订单面板的关闭按钮
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideMultiOrderPanel);
            }

            const backBtn = document.getElementById('backToMainBtn');
            if (backBtn) {
                backBtn.addEventListener('click', hideMultiOrderPanel);
            }
        });
    </script>
</body>
</html>