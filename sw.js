/**
 * Service Worker - Linus重构版 + 无感知更新支持
 *
 * 专注于缓存、离线支持、性能优化和无感知更新
 * "做一件事并做好" - Unix哲学
 */

// ⚠️ 每次前端脚本架构或 manifest.version 变更时递增主缓存版本，强制客户端获取最新资源
const CACHE_NAME = 'ota-linus-v2.1.0-silent'; // 更新版本支持无感知刷新
const STATIC_CACHE = 'ota-static-v1';
const DYNAMIC_CACHE = 'ota-dynamic-v1';
const API_CACHE = 'ota-api-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/js/core/script-manifest.js',
    '/js/core/script-loader.js',
    '/js/core/dependency-container.js',
    '/js/core/service-locator.js',
    '/js/core/application-bootstrap.js',
    '/js/utils.js',
    '/js/logger.js',
    '/js/compatibility-bridge.js',
    '/css/main.css',
    '/manifest.json'
];

// 需要缓存的API端点（相对路径）
const CACHEABLE_APIS = [
    '/api/v1/sub-categories',
    '/api/v1/car-types',
    '/api/v1/driving-regions'
];

// 缓存策略配置
const CACHE_STRATEGIES = {
    // 静态资源：缓存优先
    static: 'cache-first',
    // API数据：网络优先，失败时使用缓存
    api: 'network-first', 
    // 页面：网络优先
    pages: 'network-first',
    // 图片：缓存优先
    images: 'cache-first'
};

// 缓存时间配置（毫秒）
const CACHE_TTL = {
    static: 7 * 24 * 60 * 60 * 1000, // 7天
    api: 60 * 60 * 1000, // 1小时
    dynamic: 24 * 60 * 60 * 1000, // 1天
    images: 30 * 24 * 60 * 60 * 1000 // 30天
};

self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('📦 缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ Service Worker 安装完成');
                // 强制激活新的 Service Worker
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Service Worker 安装失败:', error);
            })
    );
});

self.addEventListener('activate', (event) => {
    console.log('⚡ Service Worker 激活中...');
    
    event.waitUntil(
        Promise.all([
            // 清理旧缓存
            cleanupOldCaches(),
            // 立即控制所有客户端
            self.clients.claim()
        ]).then(() => {
            console.log('✅ Service Worker 已激活');
        })
    );
});

self.addEventListener('fetch', (event) => {
    const request = event.request;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== self.location.origin) {
        return;
    }

    // 根据请求类型选择缓存策略
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request, STATIC_CACHE));
    } else if (isAPIRequest(request)) {
        event.respondWith(networkFirst(request, API_CACHE));
    } else if (isImageRequest(request)) {
        event.respondWith(cacheFirst(request, DYNAMIC_CACHE));
    } else if (isPageRequest(request)) {
        event.respondWith(networkFirst(request, DYNAMIC_CACHE));
    }
});

// 消息处理
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_INFO':
            getCacheInfo().then(info => {
                event.ports[0].postMessage(info);
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache(data.cacheName).then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
            
        case 'PRECACHE_URLS':
            precacheUrls(data.urls).then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;

        case 'SILENT_UPDATE_CHECK':
            checkForSilentUpdate().then(result => {
                event.ports[0].postMessage(result);
            });
            break;

        case 'PREPARE_SILENT_UPDATE':
            prepareSilentUpdate().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
    }
});

// 缓存策略实现

// 缓存优先策略
async function cacheFirst(request, cacheName) {
    try {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // 检查缓存是否过期
            if (isCacheValid(cachedResponse, getCacheTTL(request))) {
                return cachedResponse;
            }
        }
        
        // 缓存未命中或已过期，从网络获取
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // 添加时间戳并缓存
            const responseToCache = addTimestamp(networkResponse.clone());
            cache.put(request, responseToCache);
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('缓存优先策略失败:', error);
        
        // 网络失败，尝试返回过期缓存
        const cache = await caches.open(cacheName);
        const staleResponse = await cache.match(request);
        
        if (staleResponse) {
            console.warn('返回过期缓存:', request.url);
            return staleResponse;
        }
        
        // 返回离线页面或错误响应
        return new Response('离线状态，内容不可用', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// 网络优先策略
async function networkFirst(request, cacheName) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(cacheName);
            const responseToCache = addTimestamp(networkResponse.clone());
            cache.put(request, responseToCache);
        }
        
        return networkResponse;
        
    } catch (error) {
        console.warn('网络请求失败，尝试缓存:', request.url);
        
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 如果是API请求，返回离线数据
        if (isAPIRequest(request)) {
            return createOfflineAPIResponse(request);
        }
        
        throw error;
    }
}

// 工具函数

function isStaticAsset(request) {
    const url = new URL(request.url);
    return STATIC_ASSETS.some(asset => url.pathname === asset) ||
           url.pathname.match(/\.(js|css|woff2?|png|jpg|jpeg|gif|svg|ico)$/);
}

function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.startsWith('/api/') ||
           CACHEABLE_APIS.some(api => url.pathname.startsWith(api));
}

function isImageRequest(request) {
    return request.destination === 'image' ||
           request.url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/);
}

function isPageRequest(request) {
    return request.mode === 'navigate' ||
           (request.method === 'GET' && request.headers.get('accept').includes('text/html'));
}

function getCacheTTL(request) {
    if (isStaticAsset(request)) return CACHE_TTL.static;
    if (isAPIRequest(request)) return CACHE_TTL.api;
    if (isImageRequest(request)) return CACHE_TTL.images;
    return CACHE_TTL.dynamic;
}

function addTimestamp(response) {
    const headers = new Headers(response.headers);
    headers.set('sw-cached-at', Date.now().toString());
    
    return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: headers
    });
}

function isCacheValid(response, ttl) {
    const cachedAt = response.headers.get('sw-cached-at');
    if (!cachedAt) return false;
    
    const age = Date.now() - parseInt(cachedAt);
    return age < ttl;
}

async function cleanupOldCaches() {
    const cacheNames = await caches.keys();
    const validCaches = [STATIC_CACHE, DYNAMIC_CACHE, API_CACHE];
    
    const deletePromises = cacheNames
        .filter(cacheName => !validCaches.includes(cacheName))
        .map(cacheName => {
            console.log('🗑️ 删除旧缓存:', cacheName);
            return caches.delete(cacheName);
        });
    
    return Promise.all(deletePromises);
}

async function getCacheInfo() {
    const cacheNames = await caches.keys();
    const info = {};
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        info[cacheName] = {
            count: keys.length,
            urls: keys.map(request => request.url)
        };
    }
    
    return info;
}

async function clearCache(cacheName) {
    if (cacheName) {
        return caches.delete(cacheName);
    } else {
        const cacheNames = await caches.keys();
        return Promise.all(cacheNames.map(name => caches.delete(name)));
    }
}

async function precacheUrls(urls) {
    const cache = await caches.open(DYNAMIC_CACHE);
    return cache.addAll(urls);
}

function createOfflineAPIResponse(request) {
    const url = new URL(request.url);
    
    // 为不同API端点提供离线数据
    const offlineData = {
        '/api/v1/sub-categories': [
            { id: 1, name: '机场接送' },
            { id: 2, name: '市内包车' }
        ],
        '/api/v1/car-types': [
            { id: 1, name: '标准轿车' },
            { id: 2, name: '商务车' }
        ],
        '/api/v1/driving-regions': [
            { id: 1, name: '吉隆坡' },
            { id: 2, name: '槟城' }
        ]
    };
    
    const data = offlineData[url.pathname] || { error: 'Offline data not available' };
    
    return new Response(JSON.stringify(data), {
        status: 200,
        headers: {
            'Content-Type': 'application/json',
            'X-Offline-Response': 'true'
        }
    });
}

// 性能监控
self.addEventListener('fetch', (event) => {
    // 记录缓存命中率
    if (event.request.url.includes('/api/')) {
        recordCacheMetrics(event.request);
    }
});

let cacheMetrics = {
    hits: 0,
    misses: 0,
    networkErrors: 0
};

function recordCacheMetrics(request) {
    // 这里可以实现更详细的指标收集
    // 例如：响应时间、缓存命中率等
}

// 定期清理和优化缓存
setInterval(async () => {
    try {
        await optimizeCaches();
    } catch (error) {
        console.error('缓存优化失败:', error);
    }
}, 24 * 60 * 60 * 1000); // 每24小时执行一次

async function optimizeCaches() {
    console.log('🔧 开始缓存优化...');
    
    const cacheNames = await caches.keys();
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            
            if (response) {
                const ttl = getCacheTTL(request);
                if (!isCacheValid(response, ttl)) {
                    console.log('🗑️ 删除过期缓存:', request.url);
                    await cache.delete(request);
                }
            }
        }
    }
    
    console.log('✅ 缓存优化完成');
}

// 无感知更新支持函数
async function checkForSilentUpdate() {
    try {
        // 检查是否可以进行静默更新
        const cacheNames = await caches.keys();
        const hasValidCache = cacheNames.some(name => name.includes('ota-linus'));

        return {
            canSilentUpdate: hasValidCache,
            cacheCount: cacheNames.length,
            estimatedUpdateTime: 2000 // 预估2秒更新时间
        };
    } catch (error) {
        return {
            canSilentUpdate: false,
            error: error.message
        };
    }
}

async function prepareSilentUpdate() {
    try {
        // 预缓存新版本资源
        const cache = await caches.open('ota-silent-update-cache');

        // 预加载关键资源
        const criticalAssets = [
            '/js/core/runtime-version-watcher.js',
            '/js/core/script-manifest.js',
            '/main.js'
        ];

        // 后台预加载
        for (const asset of criticalAssets) {
            try {
                const response = await fetch(asset);
                if (response.ok) {
                    await cache.put(asset, response);
                }
            } catch (e) {
                console.warn('预加载失败:', asset, e.message);
            }
        }

        console.log('🔄 静默更新资源预加载完成');
        return true;
    } catch (error) {
        console.error('预加载失败:', error);
        return false;
    }
}

console.log('🚀 Service Worker 脚本加载完成 - Linus重构版 + 无感知更新支持');