/**
 * OTA核心系统 - 生产环境优化版本
 * <PERSON><PERSON>式重构 + 生产环境优化
 * 
 * 优化特性：
 * - 移除开发调试代码
 * - 压缩日志输出
 * - 优化性能关键路径
 * - 减少内存分配
 */

'use strict';

// 生产环境全局配置
const PRODUCTION_CONFIG = {
    DEBUG: false,
    LOG_LEVEL: 'error', // 只记录错误
    PERFORMANCE_MONITORING: true,
    CACHE_ENABLED: true
};

// 简化的全局对象 - 生产优化版本
window.ota = {
    _cache: new Map(), // 内部缓存
    
    config: {
        api: {
            baseURL: 'https://gomyhire.com.my/api',
            timeout: 30000
        },
        gemini: {
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent',
            timeout: 45000
        }
    },
    
    // 订单历史 - 优化版本
    history: {
        storageKey: 'ota_order_history',
        maxSize: 1000,
        
        save(order, userEmail) {
            try {
                const key = `${this.storageKey}_${userEmail}`;
                const history = JSON.parse(localStorage.getItem(key) || '[]');
                
                order.created_at = new Date().toISOString();
                order.id = order.id || Date.now().toString();
                
                history.unshift(order);
                if (history.length > this.maxSize) {
                    history.splice(this.maxSize);
                }
                
                localStorage.setItem(key, JSON.stringify(history));
                return true;
            } catch (e) {
                console.error('History save failed:', e);
                return false;
            }
        },
        
        get(userEmail, limit = 50) {
            try {
                const key = `${this.storageKey}_${userEmail}`;
                const history = JSON.parse(localStorage.getItem(key) || '[]');
                return history.slice(0, limit);
            } catch (e) {
                console.error('History get failed:', e);
                return [];
            }
        }
    },
    
    // 酒店数据 - 高性能版本
    hotels: {
        // 预编译的酒店映射表
        _map: new Map([
            ['香格里拉', 'Shangri-La Hotel Kuala Lumpur'],
            ['希尔顿', 'Hilton Kuala Lumpur'],
            ['万豪', 'JW Marriott Kuala Lumpur'],
            ['丽思卡尔顿', 'The Ritz-Carlton Kuala Lumpur'],
            ['洲际', 'InterContinental Kuala Lumpur'],
            ['槟城香格里拉', 'Shangri-La Hotel Penang'],
            ['东方大酒店', 'Eastern & Oriental Hotel'],
            ['马六甲香格里拉', 'Shangri-La Hotel Malacca']
        ]),
        
        normalize(name) {
            if (!name) return name;
            
            // 缓存查找
            const cacheKey = `hotel_${name}`;
            if (window.ota._cache.has(cacheKey)) {
                return window.ota._cache.get(cacheKey);
            }
            
            // 精确匹配
            const normalized = this._map.get(name);
            if (normalized) {
                window.ota._cache.set(cacheKey, normalized);
                return normalized;
            }
            
            // 模糊匹配
            for (const [key, value] of this._map) {
                if (name.includes(key) || key.includes(name)) {
                    window.ota._cache.set(cacheKey, value);
                    return value;
                }
            }
            
            // 缓存原值
            window.ota._cache.set(cacheKey, name);
            return name;
        }
    },
    
    // 渠道检测 - 性能优化版本
    channelDetector: {
        // 预编译正则表达式
        _patterns: {
            fliggy: /订单编号[：:\s]*\d{19}/,
            jingge: /jingge|jinggeshop|精格|精格商铺/i,
            reference: /^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i
        },
        
        detect(text) {
            if (!text) return null;
            
            // 缓存检查
            const cacheKey = `channel_${text.slice(0, 50)}`;
            if (window.ota._cache.has(cacheKey)) {
                return window.ota._cache.get(cacheKey);
            }
            
            let result = null;
            
            if (this._patterns.fliggy.test(text)) {
                result = { channel: 'fliggy', confidence: 0.9 };
            } else if (this._patterns.jingge.test(text)) {
                result = { channel: 'jingge', confidence: 0.85 };
            } else if (this._patterns.reference.test(text)) {
                result = { channel: 'generic', confidence: 0.7 };
            }
            
            // 缓存结果
            window.ota._cache.set(cacheKey, result);
            return result;
        }
    },
    
    // Gemini API - 优化版本
    gemini: {
        async parseOrder(text, channel = null) {
            const apiKey = window.GEMINI_API_KEY;
            if (!apiKey) throw new Error('Gemini API key required');
            
            const prompt = this._buildPrompt(text, channel);
            
            try {
                const response = await fetch(`${window.ota.config.gemini.endpoint}?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 4096
                        }
                    }),
                    signal: AbortSignal.timeout(window.ota.config.gemini.timeout)
                });
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                const data = await response.json();
                const resultText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!resultText) {
                    throw new Error('Empty response');
                }
                
                return JSON.parse(resultText);
                
            } catch (error) {
                if (PRODUCTION_CONFIG.DEBUG) {
                    console.error('Gemini API failed:', error);
                }
                throw error;
            }
        },
        
        _buildPrompt(text, channel) {
            return `请解析以下${channel?.channel || ''}订单信息，返回JSON格式...`; // 简化版prompt
        }
    },
    
    // API服务 - 优化版本
    api: {
        async request(endpoint, options = {}) {
            const url = `${window.ota.config.api.baseURL}${endpoint}`;
            const token = localStorage.getItem('access_token');
            
            const config = {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` }),
                    ...options.headers
                },
                signal: AbortSignal.timeout(window.ota.config.api.timeout)
            };
            
            if (options.body) {
                config.body = JSON.stringify(options.body);
            }
            
            try {
                const response = await fetch(url, config);
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                return await response.json();
                
            } catch (error) {
                if (PRODUCTION_CONFIG.DEBUG) {
                    console.error(`API request failed [${endpoint}]:`, error);
                }
                throw error;
            }
        },
        
        createOrder(orderData) {
            return this.request('/v1/order-jobs', {
                method: 'POST',
                body: orderData
            });
        },
        
        getOrderHistory(params = {}) {
            const query = new URLSearchParams(params).toString();
            return this.request(`/v1/order-jobs?${query}`);
        }
    },
    
    // 多订单处理 - 简化高性能版本
    multiOrder: {
        isActive: false,
        orders: [],
        
        detect(text) {
            if (!text || text.length < 50) return false;
            
            // 快速模式检测
            const patterns = [
                /订单[编号]*[：:\s]*[\d\w]+/g,
                /\d{2}:\d{2}/g,
                /\d{4}-\d{2}-\d{2}/g
            ];
            
            let totalMatches = 0;
            for (const pattern of patterns) {
                const matches = text.match(pattern);
                totalMatches += matches ? matches.length : 0;
                if (totalMatches >= 3) return true; // 早期退出优化
            }
            
            return false;
        },
        
        activate(orders, originalText) {
            this.isActive = true;
            this.orders = orders;
            this._showUI();
        },
        
        async batchCreate(orders) {
            const results = [];
            
            for (let i = 0; i < orders.length; i++) {
                try {
                    const result = await window.ota.api.createOrder(orders[i]);
                    results.push({ success: true, order: orders[i], result });
                    
                    // 保存历史
                    const userEmail = localStorage.getItem('user_email');
                    if (userEmail) {
                        window.ota.history.save(orders[i], userEmail);
                    }
                    
                } catch (error) {
                    results.push({ success: false, order: orders[i], error });
                }
                
                // 延迟避免API限制
                if (i < orders.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            }
            
            this._showResults(results);
            return results;
        },
        
        _showUI() {
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) return;
            
            panel.innerHTML = `
                <h3>检测到多个订单 (${this.orders.length})</h3>
                <div class="multi-order-list">
                    ${this.orders.map((order, i) => `
                        <div class="multi-order-item">
                            <input type="checkbox" checked>
                            <div><strong>${order.customer_name || '未知'}</strong></div>
                            <div>${order.pickup || '未知'} → ${order.destination || '未知'}</div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="window.ota.multiOrder.batchCreate(window.ota.multiOrder.orders)">批量创建</button>
            `;
            
            panel.classList.remove('hidden');
        },
        
        _showResults(results) {
            const success = results.filter(r => r.success).length;
            alert(`批量创建完成: 成功${success}个，失败${results.length - success}个`);
            this.isActive = false;
        }
    },
    
    // UI管理器 - 最小化版本
    ui: {
        init() {
            this._bindEvents();
            if (PRODUCTION_CONFIG.DEBUG) {
                console.log('UI initialized');
            }
        },
        
        _bindEvents() {
            const orderInput = document.getElementById('orderInput');
            if (orderInput) {
                orderInput.addEventListener('input', 
                    this._debounce(e => this._analyzeInput(e.target.value), 1000)
                );
            }
        },
        
        async _analyzeInput(text) {
            if (!text || text.length < 20) return;
            
            try {
                if (window.ota.multiOrder.detect(text)) {
                    const result = await window.ota.gemini.parseOrder(text);
                    if (result.isMultiOrder) {
                        window.ota.multiOrder.activate(result.orders, text);
                        return;
                    }
                }
                
                const channel = window.ota.channelDetector.detect(text);
                const result = await window.ota.gemini.parseOrder(text, channel);
                this._fillForm(result);
                
            } catch (error) {
                if (PRODUCTION_CONFIG.DEBUG) {
                    console.error('Analysis failed:', error);
                }
            }
        },
        
        _fillForm(data) {
            const fields = {
                customerName: data.customer_name,
                pickup: data.pickup,
                dropoff: data.destination,
                pickupDate: data.date,
                pickupTime: data.time
            };
            
            Object.entries(fields).forEach(([id, value]) => {
                const el = document.getElementById(id);
                if (el && value) {
                    el.value = id.includes('pickup') || id.includes('dropoff') ? 
                        window.ota.hotels.normalize(value) : value;
                }
            });
        },
        
        _debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }
    }
};

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.ota.ui.init();
        if (PRODUCTION_CONFIG.PERFORMANCE_MONITORING) {
            console.log('✅ Production OTA system ready');
        }
    });
} else {
    window.ota.ui.init();
}

// 性能监控
if (PRODUCTION_CONFIG.PERFORMANCE_MONITORING) {
    window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`⚡ Load time: ${loadTime.toFixed(0)}ms`);
    });
}