/**
 * 订单处理服务
 * 文件: js/pages/multi-order/services/order-processor.js
 * 角色: 新的独立订单处理服务，迁移自multi-order-processor.js
 * 
 * @ORDER_PROCESSOR 订单处理服务
 * 🏷️ 标签: @OTA_ORDER_PROCESSOR_V2
 * 📝 说明: 负责订单解析、数据映射和校验功能
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Services = window.OTA.Services || {};

(function() {
    'use strict';

    /**
     * 订单处理服务类
     * 负责订单的解析、验证和数据处理
     */
    class OrderProcessor {
        constructor(config = {}) {
            this.config = {
                maxOrdersPerBatch: config.maxOrdersPerBatch || 5,
                batchDelay: config.batchDelay || 800,
                maxRetries: config.maxRetries || 3,
                timeout: config.timeout || 30000,
                enableValidation: config.enableValidation !== false,
                enableTransformation: config.enableTransformation !== false,
                ...config
            };
            
            this.logger = this.getLogger();
            
            // 处理统计
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                lastProcessTime: null
            };

            this.logger.log('⚙️ 订单处理服务已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 处理订单数组
         * @param {Array} orders - 订单数组
         * @param {Object} options - 处理选项
         * @returns {Promise<Array>} 处理后的订单数组
         */
        async processOrders(orders, options = {}) {
            if (!Array.isArray(orders)) {
                throw new Error('订单数据必须是数组');
            }

            this.logger.log(`⚙️ 开始处理 ${orders.length} 个订单`, 'info');

            const processedOrders = [];
            const { originalText = '', detectionMethod = 'ai' } = options;

            for (let i = 0; i < orders.length; i++) {
                try {
                    const order = orders[i];
                    this.logger.log(`📋 处理订单 ${i + 1}/${orders.length}`, 'info');

                    // 处理单个订单
                    const processedOrder = await this.processSingleOrder(order, {
                        index: i,
                        originalText,
                        detectionMethod
                    });

                    processedOrders.push(processedOrder);
                    this.stats.successfulProcessed++;

                } catch (error) {
                    this.logger.logError(`订单 ${i + 1} 处理失败`, error);
                    
                    // 添加错误信息到订单
                    processedOrders.push({
                        ...orders[i],
                        _error: error.message,
                        _processed: false,
                        _index: i
                    });
                    
                    this.stats.failedProcessed++;
                }
            }

            this.stats.totalProcessed += orders.length;
            this.stats.lastProcessTime = Date.now();

            this.logger.log(`✅ 订单处理完成: 成功 ${this.stats.successfulProcessed}, 失败 ${this.stats.failedProcessed}`, 'success');

            return processedOrders;
        }

        /**
         * 处理单个订单
         * @param {Object} order - 订单对象
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理后的订单对象
         */
        async processSingleOrder(order, options = {}) {
            if (!order || typeof order !== 'object') {
                throw new Error('无效的订单数据');
            }

            const { index = 0, originalText = '', detectionMethod = 'ai' } = options;

            // 1. 数据转换
            let processedOrder = this.config.enableTransformation 
                ? await this.transformOrderData(order)
                : { ...order };

            // 2. 数据验证
            if (this.config.enableValidation) {
                const validationResult = await this.validateOrderData(processedOrder);
                if (!validationResult.isValid) {
                    throw new Error(`订单验证失败: ${validationResult.errors.join(', ')}`);
                }
                processedOrder._validation = validationResult;
            }

            // 3. 添加元数据
            processedOrder._metadata = {
                index,
                originalText,
                detectionMethod,
                processedAt: Date.now(),
                processorVersion: '2.0.0'
            };

            // 4. 标记为已处理
            processedOrder._processed = true;

            return processedOrder;
        }

        /**
         * 转换订单数据
         * @param {Object} order - 原始订单数据
         * @returns {Promise<Object>} 转换后的订单数据
         */
        async transformOrderData(order) {
            try {
                // 获取转换器服务
                const transformer = this.getTransformerService();
                
                if (transformer && typeof transformer.transformOrder === 'function') {
                    return await transformer.transformOrder(order);
                }

                // 如果没有转换器，执行基本的字段标准化
                return this.basicFieldNormalization(order);

            } catch (error) {
                this.logger.logError('订单数据转换失败', error);
                // 转换失败时返回原始数据
                return { ...order };
            }
        }

        /**
         * 基本字段标准化
         * @param {Object} order - 订单对象
         * @returns {Object} 标准化后的订单对象
         */
        basicFieldNormalization(order) {
            const normalized = { ...order };

            // 标准化客户姓名
            if (normalized.customer_name) {
                normalized.customer_name = normalized.customer_name.toString().trim();
            }

            // 标准化电话号码
            if (normalized.customer_phone) {
                normalized.customer_phone = normalized.customer_phone.toString().replace(/\D/g, '');
            }

            // 标准化地址
            if (normalized.pickup_location) {
                normalized.pickup_location = normalized.pickup_location.toString().trim();
            }
            if (normalized.dropoff_location) {
                normalized.dropoff_location = normalized.dropoff_location.toString().trim();
            }

            // 标准化价格
            if (normalized.total_price) {
                const price = parseFloat(normalized.total_price.toString().replace(/[^\d.]/g, ''));
                if (!isNaN(price)) {
                    normalized.total_price = price;
                }
            }

            // 标准化时间格式
            if (normalized.pickup_time) {
                normalized.pickup_time = this.normalizeTimeFormat(normalized.pickup_time);
            }

            return normalized;
        }

        /**
         * 标准化时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间字符串
         */
        normalizeTimeFormat(timeStr) {
            if (!timeStr) return timeStr;

            try {
                // 尝试解析各种时间格式
                const timePatterns = [
                    /(\d{1,2}):(\d{2})/,  // HH:MM
                    /(\d{1,2})点(\d{2})/,  // X点XX
                    /(\d{1,2})时(\d{2})/,  // X时XX
                ];

                for (const pattern of timePatterns) {
                    const match = timeStr.match(pattern);
                    if (match) {
                        const hours = parseInt(match[1]).toString().padStart(2, '0');
                        const minutes = parseInt(match[2]).toString().padStart(2, '0');
                        return `${hours}:${minutes}`;
                    }
                }

                return timeStr;
            } catch (error) {
                this.logger.logError('时间格式标准化失败', error);
                return timeStr;
            }
        }

        /**
         * 验证订单数据
         * @param {Object} order - 订单对象
         * @returns {Promise<Object>} 验证结果
         */
        async validateOrderData(order) {
            const errors = [];
            const warnings = [];

            // 必填字段验证
            const requiredFields = [
                'customer_name',
                'pickup_location',
                'dropoff_location'
            ];

            requiredFields.forEach(field => {
                if (!order[field] || order[field].toString().trim() === '') {
                    errors.push(`缺少必填字段: ${field}`);
                }
            });

            // 电话号码验证
            if (order.customer_phone) {
                const phonePattern = /^1[3-9]\d{9}$/;
                if (!phonePattern.test(order.customer_phone)) {
                    warnings.push('电话号码格式可能不正确');
                }
            }

            // 价格验证
            if (order.total_price) {
                const price = parseFloat(order.total_price);
                if (isNaN(price) || price <= 0) {
                    warnings.push('价格格式可能不正确');
                }
            }

            // 时间验证
            if (order.pickup_time) {
                const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
                if (!timePattern.test(order.pickup_time)) {
                    warnings.push('时间格式可能不正确');
                }
            }

            return {
                isValid: errors.length === 0,
                errors,
                warnings,
                validatedAt: Date.now()
            };
        }

        /**
         * 获取转换器服务
         * @returns {Object|null} 转换器服务实例
         */
        getTransformerService() {
            return window.OTA?.multiOrderTransformer || 
                   window.OTA?.Services?.transformer ||
                   null;
        }

        /**
         * 批量验证订单
         * @param {Array} orders - 订单数组
         * @returns {Promise<Object>} 批量验证结果
         */
        async batchValidateOrders(orders) {
            if (!Array.isArray(orders)) {
                throw new Error('订单数据必须是数组');
            }

            const results = [];
            let validCount = 0;
            let invalidCount = 0;

            for (let i = 0; i < orders.length; i++) {
                try {
                    const validationResult = await this.validateOrderData(orders[i]);
                    results.push({
                        index: i,
                        order: orders[i],
                        validation: validationResult
                    });

                    if (validationResult.isValid) {
                        validCount++;
                    } else {
                        invalidCount++;
                    }
                } catch (error) {
                    results.push({
                        index: i,
                        order: orders[i],
                        validation: {
                            isValid: false,
                            errors: [error.message],
                            warnings: []
                        }
                    });
                    invalidCount++;
                }
            }

            return {
                totalCount: orders.length,
                validCount,
                invalidCount,
                results,
                summary: {
                    validPercentage: Math.round((validCount / orders.length) * 100),
                    hasErrors: invalidCount > 0
                }
            };
        }

        /**
         * 获取处理统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                config: this.config
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                lastProcessTime: null
            };
            this.logger.log('📊 处理统计信息已重置', 'info');
        }

        /**
         * 更新配置
         * @param {Object} newConfig - 新配置
         */
        updateConfig(newConfig) {
            this.config = {
                ...this.config,
                ...newConfig
            };
            this.logger.log('🔧 处理服务配置已更新', 'info');
        }

        /**
         * 销毁服务
         */
        destroy() {
            // 清理资源
            this.config = null;
            this.stats = null;
            this.logger.log('🗑️ 订单处理服务已销毁', 'info');
        }
    }

    // 创建全局订单处理服务实例
    const orderProcessor = new OrderProcessor();

    // 暴露到OTA命名空间
    window.OTA.Services.OrderProcessor = OrderProcessor;
    window.OTA.Services.orderProcessor = orderProcessor;

    // 向后兼容
    window.orderProcessor = orderProcessor;

    console.log('✅ 订单处理服务已加载');

})();
