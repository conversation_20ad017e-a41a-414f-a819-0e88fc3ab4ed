/**
 * 简化UI管理器 - <PERSON><PERSON>式重构版本
 * 
 * 消除了UIMana<PERSON>, UIManagerAdapter, FormManager等适配器废话
 * 直接操作DOM，简单高效
 */

'use strict';

// 简化的UI管理器，消除所有适配器
window.ota.ui.enhanced = {
    
    // 表单提交处理
    bindOrderForm() {
        const orderForm = document.getElementById('orderForm');
        if (!orderForm) return;
        
        orderForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleOrderSubmit();
        });
        
        // 绑定创建订单按钮
        const createBtn = document.getElementById('createOrder');
        if (createBtn) {
            createBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleOrderSubmit();
            });
        }
    },
    
    async handleOrderSubmit() {
        const orderData = this.collectOrderData();
        
        if (!this.validateOrderData(orderData)) {
            return;
        }
        
        try {
            console.log('🚀 创建订单中...');
            
            const result = await window.ota.api.createOrder(orderData);
            
            console.log('✅ 订单创建成功:', result);
            alert(`订单创建成功！\n订单ID: ${result.id || '未知'}`);
            
            // 清空表单
            this.resetForm();
            
        } catch (error) {
            console.error('❌ 订单创建失败:', error);
            alert(`订单创建失败: ${error.message}`);
        }
    },
    
    collectOrderData() {
        const formData = new FormData(document.getElementById('orderForm'));
        const data = {};
        
        // 收集基本字段
        const fields = [
            'customerName', 'customerContact', 'customerEmail',
            'pickup', 'dropoff', 'pickupDate', 'pickupTime',
            'passengerCount', 'luggageCount', 'otaReferenceNumber',
            'flightInfo', 'extraRequirement', 'otaPrice', 'driverFee',
            'currency', 'subCategoryId', 'carTypeId', 'drivingRegionId'
        ];
        
        // 🚀 简化：直接使用 snake_case 字段名，不再进行转换
        const snakeFields = [
            'customer_name', 'customer_contact', 'customer_email',
            'pickup', 'destination', 'date', 'time',
            'passenger_number', 'luggage_number', 'ota_reference_number',
            'flight_info', 'extra_requirement', 'ota_price', 'driver_fee',
            'currency', 'sub_category_id', 'car_type_id', 'driving_region_id'
        ];

        snakeFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                data[field] = element.value || null;
            }
        });
        
        // 处理特殊字段
        data.passenger_number = parseInt(data.passenger_count) || 1;
        data.luggage_number = parseInt(data.luggage_count) || 0;
        data.ota_price = parseFloat(data.ota_price) || null;
        data.driver_fee = parseFloat(data.driver_fee) || null;
        
        // 处理布尔字段
        data.baby_chair = document.getElementById('babyChairMain')?.checked || false;
        data.tour_guide = document.getElementById('tourGuideMain')?.checked || false;
        data.meet_and_greet = document.getElementById('meetAndGreetMain')?.checked || false;
        
        // 处理语言数组
        const languageCheckboxes = document.querySelectorAll('input[name="languagesIdArray"]:checked');
        data.languages_id_array = Array.from(languageCheckboxes).map(cb => parseInt(cb.value));
        
        // 添加负责人ID
        const userEmail = localStorage.getItem('user_email');
        if (userEmail) {
            data.incharge_by_backend_user_id = this.getBackendUserIdByEmail(userEmail);
        }
        
        return data;
    },
    
    validateOrderData(data) {
        const required = ['customer_name', 'pickup', 'destination', 'date', 'time'];
        const missing = required.filter(field => !data[field]);

        if (missing.length > 0) {
            // 🚀 简化：直接显示字段名，不再进行格式转换
            alert(`请填写必填字段: ${missing.join(', ')}`);
            return false;
        }

        return true;
    },
    
    resetForm() {
        document.getElementById('orderForm').reset();
        console.log('✅ 表单已重置');
    },
    
    // 加载下拉菜单数据
    async populateDropdowns() {
        try {
            const coreData = await window.ota.api.getCoreData();
            
            // OTA渠道
            this.populateOtaChannels();
            
            // 服务类型
            this.populateSelect('subCategoryId', coreData.subCategories, '请选择服务类型');
            
            // 车型
            this.populateSelect('carTypeId', coreData.carTypes, '请选择车型');
            
            // 区域
            this.populateSelect('drivingRegionId', coreData.regions, '请选择行驶区域');
            
            console.log('✅ 下拉菜单数据已加载');
            
        } catch (error) {
            console.error('下拉菜单数据加载失败:', error);
        }
    },
    
    populateSelect(selectId, options, placeholder) {
        const select = document.getElementById(selectId);
        if (!select || !options) return;
        
        select.innerHTML = `<option value="">${placeholder}</option>` +
            options.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
    },
    
    populateOtaChannels() {
        const otaSelect = document.getElementById('ota');
        if (!otaSelect) return;
        
        // 简化的OTA选项
        const otaOptions = [
            { value: 'fliggy', text: 'Fliggy 飞猪' },
            { value: 'jingge', text: 'JingGe 精格' },
            { value: 'ctrip', text: 'Ctrip 携程' },
            { value: 'booking', text: 'Booking' },
            { value: 'agoda', text: 'Agoda' },
            { value: 'other', text: '其他' }
        ];
        
        otaSelect.innerHTML = '<option value="">选择OTA渠道</option>' +
            otaOptions.map(opt => `<option value="${opt.value}">${opt.text}</option>`).join('');
    },
    
    // 🚀 注意：字段转换方法已移除，现在统一使用 snake_case 格式
    
    getBackendUserIdByEmail(email) {
        // 简化的用户映射
        const userMap = {
            '<EMAIL>': 310,
            '<EMAIL>': 37,
            '<EMAIL>': 420,
            '<EMAIL>': 533,
            '<EMAIL>': 1201
        };
        
        return userMap[email] || 1; // 默认为Super Admin
    },
    
    // 初始化函数
    init() {
        this.bindOrderForm();
        this.populateDropdowns();
        
        console.log('✅ 简化UI管理器初始化完成');
    }
};

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.ota.ui.enhanced.init();
    });
} else {
    window.ota.ui.enhanced.init();
}

console.log('✅ 简化UI管理器已加载');