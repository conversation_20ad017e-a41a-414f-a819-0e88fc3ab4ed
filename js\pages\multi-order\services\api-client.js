/**
 * API客户端服务
 * 文件: js/pages/multi-order/services/api-client.js
 * 角色: 新的独立API客户端服务，封装现有的API服务
 * 
 * @API_CLIENT API客户端服务
 * 🏷️ 标签: @OTA_API_CLIENT_V2
 * 📝 说明: 为多订单页面提供统一的API接口，封装现有API服务
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Services = window.OTA.Services || {};

(function() {
    'use strict';

    /**
     * API客户端服务类
     * 封装现有的API服务，为多订单页面提供统一接口
     */
    class ApiClient {
        constructor(config = {}) {
            this.config = {
                timeout: config.timeout || 30000,
                maxRetries: config.maxRetries || 3,
                retryDelay: config.retryDelay || 1000,
                enableLogging: config.enableLogging !== false,
                enableMetrics: config.enableMetrics !== false,
                ...config
            };

            this.logger = this.getLogger();
            
            // API调用统计
            this.metrics = {
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                averageResponseTime: 0,
                lastCallTime: null
            };

            this.logger.log('🌐 API客户端服务已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            if (!this.config.enableLogging) {
                return {
                    log: () => {},
                    logError: () => {}
                };
            }

            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 创建订单
         * @param {Object} orderData - 订单数据
         * @param {Object} options - 选项
         * @returns {Promise<Object>} API响应
         */
        async createOrder(orderData, options = {}) {
            const startTime = Date.now();
            
            try {
                this.logger.log('📤 发送创建订单请求', 'info', {
                    客户: orderData.customer_name,
                    路线: `${orderData.pickup_location} → ${orderData.dropoff_location}`
                });

                // 获取API服务
                const apiService = this.getApiService();
                if (!apiService) {
                    throw new Error('API服务不可用');
                }

                // 准备请求数据
                const requestData = this.prepareOrderData(orderData);

                // 执行API调用（带重试）
                const response = await this.executeWithRetry(
                    () => apiService.createOrder(requestData),
                    options.retries || this.config.maxRetries
                );

                // 验证响应
                this.validateResponse(response);

                // 更新统计
                this.updateMetrics(true, Date.now() - startTime);

                this.logger.log('✅ 订单创建成功', 'success', {
                    订单ID: response.id || response.orderId,
                    响应时间: `${Date.now() - startTime}ms`
                });

                return {
                    success: true,
                    id: response.id || response.orderId,
                    data: response,
                    responseTime: Date.now() - startTime
                };

            } catch (error) {
                this.updateMetrics(false, Date.now() - startTime);
                this.logger.logError('订单创建失败', error);
                
                throw new Error(`订单创建失败: ${error.message}`);
            }
        }

        /**
         * 批量创建订单
         * @param {Array} orders - 订单数组
         * @param {Object} options - 选项
         * @returns {Promise<Array>} 批量创建结果
         */
        async createOrdersBatch(orders, options = {}) {
            if (!Array.isArray(orders) || orders.length === 0) {
                throw new Error('订单数组不能为空');
            }

            this.logger.log(`📦 开始批量创建 ${orders.length} 个订单`, 'info');

            const results = [];
            const { 
                onProgress = null, 
                onOrderComplete = null,
                concurrency = 1,
                delay = this.config.retryDelay 
            } = options;

            for (let i = 0; i < orders.length; i++) {
                try {
                    // 调用进度回调
                    if (onProgress) {
                        onProgress({
                            current: i + 1,
                            total: orders.length,
                            percentage: Math.round(((i + 1) / orders.length) * 100)
                        });
                    }

                    // 创建单个订单
                    const result = await this.createOrder(orders[i], {
                        retries: options.retries
                    });

                    results.push({
                        index: i,
                        order: orders[i],
                        result,
                        success: true
                    });

                    // 调用完成回调
                    if (onOrderComplete) {
                        onOrderComplete(i, result);
                    }

                    // 添加延迟避免API过载
                    if (i < orders.length - 1) {
                        await this.delay(delay);
                    }

                } catch (error) {
                    const errorResult = {
                        index: i,
                        order: orders[i],
                        error: error.message,
                        success: false
                    };

                    results.push(errorResult);

                    if (onOrderComplete) {
                        onOrderComplete(i, errorResult);
                    }
                }
            }

            const summary = this.generateBatchSummary(results);
            this.logger.log('📦 批量创建完成', 'info', summary);

            return {
                results,
                summary
            };
        }

        /**
         * 准备订单数据
         * @param {Object} orderData - 原始订单数据
         * @returns {Object} 准备好的订单数据
         */
        prepareOrderData(orderData) {
            // 移除内部字段
            const cleanData = { ...orderData };
            delete cleanData._processed;
            delete cleanData._metadata;
            delete cleanData._validation;
            delete cleanData._original;
            delete cleanData._batchProcessing;

            // 确保必填字段存在
            const requiredFields = ['customer_name', 'pickup_location', 'dropoff_location'];
            requiredFields.forEach(field => {
                if (!cleanData[field]) {
                    throw new Error(`缺少必填字段: ${field}`);
                }
            });

            // 数据清理和格式化
            if (cleanData.customer_phone) {
                cleanData.customer_phone = cleanData.customer_phone.toString().replace(/\D/g, '');
            }

            if (cleanData.total_price) {
                const price = parseFloat(cleanData.total_price.toString().replace(/[^\d.]/g, ''));
                if (!isNaN(price)) {
                    cleanData.total_price = price;
                }
            }

            return cleanData;
        }

        /**
         * 带重试的执行
         * @param {Function} apiCall - API调用函数
         * @param {number} maxRetries - 最大重试次数
         * @returns {Promise<Object>} API响应
         */
        async executeWithRetry(apiCall, maxRetries) {
            let lastError;
            
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    // 添加超时控制
                    const response = await Promise.race([
                        apiCall(),
                        this.createTimeoutPromise(this.config.timeout)
                    ]);
                    
                    return response;
                    
                } catch (error) {
                    lastError = error;
                    
                    if (attempt < maxRetries) {
                        const delay = this.config.retryDelay * Math.pow(2, attempt); // 指数退避
                        this.logger.log(`🔄 API调用失败，${delay}ms后重试 (${attempt + 1}/${maxRetries})`, 'warning');
                        await this.delay(delay);
                    }
                }
            }
            
            throw lastError;
        }

        /**
         * 创建超时Promise
         * @param {number} timeout - 超时时间
         * @returns {Promise} 超时Promise
         */
        createTimeoutPromise(timeout) {
            return new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`API调用超时 (${timeout}ms)`));
                }, timeout);
            });
        }

        /**
         * 验证API响应
         * @param {Object} response - API响应
         */
        validateResponse(response) {
            if (!response) {
                throw new Error('API响应为空');
            }

            // 检查是否有错误标识
            if (response.error || response.success === false) {
                throw new Error(response.message || response.error || 'API返回错误');
            }

            // 检查是否有订单ID
            if (!response.id && !response.orderId) {
                throw new Error('API响应缺少订单ID');
            }
        }

        /**
         * 生成批量处理摘要
         * @param {Array} results - 处理结果
         * @returns {Object} 摘要信息
         */
        generateBatchSummary(results) {
            const successful = results.filter(r => r.success);
            const failed = results.filter(r => !r.success);
            
            return {
                total: results.length,
                successful: successful.length,
                failed: failed.length,
                successRate: results.length > 0 ? Math.round((successful.length / results.length) * 100) : 0,
                errors: failed.map(f => ({
                    index: f.index,
                    error: f.error
                }))
            };
        }

        /**
         * 更新API调用统计
         * @param {boolean} success - 是否成功
         * @param {number} responseTime - 响应时间
         */
        updateMetrics(success, responseTime) {
            if (!this.config.enableMetrics) return;

            this.metrics.totalCalls++;
            this.metrics.lastCallTime = Date.now();

            if (success) {
                this.metrics.successfulCalls++;
            } else {
                this.metrics.failedCalls++;
            }

            // 更新平均响应时间
            this.metrics.averageResponseTime = Math.round(
                (this.metrics.averageResponseTime * (this.metrics.totalCalls - 1) + responseTime) / this.metrics.totalCalls
            );
        }

        /**
         * 获取API服务实例
         * @returns {Object|null} API服务实例
         */
        getApiService() {
            // 尝试多种方式获取API服务
            return window.getApiService?.() || 
                   window.OTA?.apiService || 
                   window.getService?.('apiService') ||
                   window.getAPIService?.() ||
                   null;
        }

        /**
         * 延迟函数
         * @param {number} ms - 毫秒数
         * @returns {Promise<void>}
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取API调用统计
         * @returns {Object} 统计信息
         */
        getMetrics() {
            return {
                ...this.metrics,
                successRate: this.metrics.totalCalls > 0 
                    ? Math.round((this.metrics.successfulCalls / this.metrics.totalCalls) * 100) 
                    : 0,
                failureRate: this.metrics.totalCalls > 0 
                    ? Math.round((this.metrics.failedCalls / this.metrics.totalCalls) * 100) 
                    : 0
            };
        }

        /**
         * 重置统计信息
         */
        resetMetrics() {
            this.metrics = {
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                averageResponseTime: 0,
                lastCallTime: null
            };
            this.logger.log('📊 API统计信息已重置', 'info');
        }

        /**
         * 测试API连接
         * @returns {Promise<boolean>} 连接状态
         */
        async testConnection() {
            try {
                const apiService = this.getApiService();
                if (!apiService) {
                    return false;
                }

                // 如果API服务有测试方法，调用它
                if (typeof apiService.testConnection === 'function') {
                    return await apiService.testConnection();
                }

                // 否则认为服务可用
                return true;

            } catch (error) {
                this.logger.logError('API连接测试失败', error);
                return false;
            }
        }

        /**
         * 更新配置
         * @param {Object} newConfig - 新配置
         */
        updateConfig(newConfig) {
            this.config = {
                ...this.config,
                ...newConfig
            };
            this.logger.log('🔧 API客户端配置已更新', 'info');
        }

        /**
         * 销毁服务
         */
        destroy() {
            // 清理资源
            this.config = null;
            this.metrics = null;
            this.logger.log('🗑️ API客户端服务已销毁', 'info');
        }
    }

    // 创建全局API客户端服务实例
    const apiClient = new ApiClient();

    // 暴露到OTA命名空间
    window.OTA.Services.ApiClient = ApiClient;
    window.OTA.Services.apiClient = apiClient;

    // 向后兼容
    window.apiClient = apiClient;

    console.log('✅ API客户端服务已加载');

})();
