/**
 * 多订单页面样式
 * 文件: js/pages/multi-order/styles/multi-order-page.css
 * 角色: 新的独立多订单页面样式，支持详细进度展示和结果报告
 * 
 * @MULTI_ORDER_PAGE_STYLES 多订单页面样式
 * 🏷️ 标签: @OTA_MULTI_ORDER_PAGE_STYLES
 * 📝 说明: 独立页面模式的样式定义
 * <AUTHOR>
 * @version 2.0.0
 */

/* ================================
   进度展示区域样式
   ================================ */

.progress-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
    box-shadow: var(--shadow-sm);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.progress-header h4 {
    margin: 0;
    color: var(--color-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.progress-summary {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 总体进度条 */
.overall-progress {
    margin-bottom: var(--spacing-lg);
}

.progress-bar-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.progress-bar {
    flex: 1;
    height: 24px;
    background: var(--bg-tertiary);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--color-success-gradient);
    border-radius: 12px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%
    );
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    min-width: 60px;
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* 实时统计 */
.progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-success {
    border-left: 4px solid var(--color-success);
}

.stat-failed {
    border-left: 4px solid var(--color-error);
}

.stat-remaining {
    border-left: 4px solid var(--color-warning);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* 订单状态列表 */
.order-status-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
}

.order-status-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.order-status-item:last-child {
    border-bottom: none;
}

.order-status-item:hover {
    background: var(--bg-secondary);
}

.order-status-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.8rem;
}

.status-waiting .order-status-icon {
    background: var(--color-info-light);
    color: var(--color-info);
}

.status-processing .order-status-icon {
    background: var(--color-warning-light);
    color: var(--color-warning);
    animation: pulse 1.5s infinite;
}

.status-success .order-status-icon {
    background: var(--color-success-light);
    color: var(--color-success);
}

.status-failed .order-status-icon {
    background: var(--color-error-light);
    color: var(--color-error);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.order-status-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-status-title {
    font-weight: 500;
    color: var(--text-primary);
}

.order-status-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* ================================
   结果报告区域样式
   ================================ */

.results-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
    box-shadow: var(--shadow-sm);
}

.results-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.results-header h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--color-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.results-summary {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-md);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.success-orders,
.failed-orders {
    margin-bottom: var(--spacing-lg);
}

.success-orders h5,
.failed-orders h5 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1rem;
    font-weight: 600;
}

.success-orders h5 {
    color: var(--color-success);
}

.failed-orders h5 {
    color: var(--color-error);
}

.success-list,
.failed-list {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.result-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item-info {
    flex: 1;
}

.result-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.result-item-details {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.result-item-status {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-weight: 500;
}

.status-success {
    background: var(--color-success-light);
    color: var(--color-success);
}

.status-failed {
    background: var(--color-error-light);
    color: var(--color-error);
}

.results-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* ================================
   响应式设计
   ================================ */

@media (max-width: 768px) {
    .progress-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        padding: var(--spacing-sm);
    }
    
    .results-actions {
        flex-direction: column;
    }
    
    .results-actions .btn {
        width: 100%;
    }
}

/* ================================
   页面模式特定样式
   ================================ */

/* 当处于页面模式时，调整多订单面板样式 */
.multi-order-panel.page-mode {
    position: static;
    width: 100%;
    height: auto;
    background: transparent;
    padding: 0;
    z-index: auto;
}

.multi-order-panel.page-mode .multi-order-content {
    max-width: none;
    width: 100%;
    margin: 0;
    background: var(--bg-primary);
    border: none;
    border-radius: 0;
    box-shadow: none;
}

/* 隐藏/显示工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* ================================
   加载指示器样式
   ================================ */

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    margin: var(--spacing-lg) 0;
    border: 2px dashed var(--border-color);
    box-shadow: var(--shadow-sm);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
    font-weight: 500;
}

.loading-progress {
    width: 100%;
    max-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.loading-progress .progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.loading-progress .progress-fill {
    height: 100%;
    background: var(--color-primary-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.loading-progress .progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 响应式加载指示器 */
@media (max-width: 768px) {
    .loading-indicator {
        padding: 30px 15px;
        margin: var(--spacing-md) 0;
    }

    .loading-spinner {
        width: 32px;
        height: 32px;
        border-width: 3px;
    }

    .loading-message {
        font-size: 0.9rem;
    }

    .loading-progress {
        max-width: 250px;
    }
}
