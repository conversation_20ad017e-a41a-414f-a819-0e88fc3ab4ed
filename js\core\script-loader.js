// Script Loader - phased, ordered, resilient loader for classic scripts
// Notes:
// - Preserves strict order within phases; supports optional parallel groups
// - Skips scripts already present in DOM; logs timing; retries on transient errors
// - No external deps; CSP-friendly (self origin)

window.OTA = window.OTA || {};

(function() {
    'use strict';

    const now = () => (typeof performance !== 'undefined' && performance.now) ? performance.now() : Date.now();

    class ScriptLoader {
        constructor() {
            this.loaded = new Set();
            this.timings = [];
            this.errors = [];
            this.maxRetries = 1;
            this.basePath = '';
            this.started = false;
            this.phaseStats = [];
        }

        markLoaded(src) {
            this.loaded.add(new URL(src, location.href).href);
        }

        isAlreadyInDOM(src) {
            const target = new URL(src, location.href).href;
            // Check tracked and DOM
            if (this.loaded.has(target)) return true;
            const scripts = document.getElementsByTagName('script');
            for (let i = 0; i < scripts.length; i++) {
                const s = scripts[i].getAttribute('src');
                if (!s) continue;
                try {
                    if (new URL(s, location.href).href === target) return true;
                } catch (_) { /* ignore */ }
            }
            return false;
        }

        preload(sources) {
            try {
                sources.forEach(src => {
                    if (this.isAlreadyInDOM(src)) return;
                    const link = document.createElement('link');
                    link.rel = 'preload';
                    link.as = 'script';
                    link.href = src;
                    document.head.appendChild(link);
                });
            } catch (_) { /* best effort */ }
        }

        loadScript(src, options = {}) {
            let fullSrc = this.basePath ? this.basePath + src : src;
            // 🔄 版本指纹: 给未带查询参数的脚本追加 ?v=<manifest.version> 以解决 Netlify 长缓存 & SW 缓存导致的不同步问题
            try {
                const manifest = window.OTA && window.OTA.scriptManifest;
                if (manifest && manifest.version && !/\?.+/.test(fullSrc)) {
                    fullSrc = `${fullSrc}?v=${encodeURIComponent(manifest.version)}`;
                }
            } catch(_) { /* 忽略版本追加错误 */ }
            if (this.isAlreadyInDOM(fullSrc)) {
                this.markLoaded(fullSrc);
                return Promise.resolve({ src: fullSrc, skipped: true });
            }

            const attempt = (retry) => new Promise((resolve, reject) => {
                const start = now();
                const script = document.createElement('script');
                script.src = fullSrc;
                // Ensure execution order for dynamically inserted scripts
                script.async = false;
                if (options.defer) script.defer = true; // hint; not required for dynamic

                script.onload = () => {
                    const duration = now() - start;
                    this.timings.push({ src: fullSrc, duration, retry });
                    this.markLoaded(fullSrc);
                    resolve({ src: fullSrc, duration, retry });
                };
                script.onerror = () => {
                    if (retry < this.maxRetries) {
                        // Brief backoff
                        setTimeout(() => {
                            attempt(retry + 1).then(resolve).catch(reject);
                        }, 50 * (retry + 1));
                    } else {
                        const err = new Error(`Failed to load script: ${fullSrc}`);
                        this.errors.push({ src: fullSrc, error: err.message });
                        reject(err);
                    }
                };

                // Insert at end of head for determinism
                (document.head || document.documentElement).appendChild(script);
            });

            return attempt(0);
        }

        async loadGroup(scripts, { parallel = false } = {}) {
            if (!Array.isArray(scripts) || scripts.length === 0) return;
            if (parallel) {
                this.preload(scripts);
                await Promise.all(scripts.map(src => this.loadScript(src)));
            } else {
                for (const src of scripts) {
                    await this.loadScript(src);
                }
            }
        }

        async loadPhases(manifest) {
            if (!manifest || !Array.isArray(manifest.phases)) throw new Error('Invalid script manifest');
            this.started = true;
            const t0 = now();
            this.phaseStats = [];
            for (const phase of manifest.phases) {
                const groupName = phase.name || 'unnamed';
                const scripts = phase.scripts || [];
                const parallel = !!phase.parallel;
                const phaseStart = now();
                if (scripts.length === 0) continue;
                console.log(`🔧 Loading phase: ${groupName} (${scripts.length} scripts)${parallel ? ' [parallel]' : ''}`);
                await this.loadGroup(scripts, { parallel });
                const phaseDur = now() - phaseStart;
                console.log(`✅ Phase complete: ${groupName} in ${phaseDur.toFixed(1)}ms`);
                this.phaseStats.push({ name: groupName, scripts: scripts.slice(), duration: phaseDur, parallel });
            }
            const total = now() - t0;
            console.log(`🚀 All scripts loaded in ${total.toFixed(1)}ms`);
            const result = { duration: total, errors: this.errors.slice(), timings: this.timings.slice(), phases: this.phaseStats.slice(), startedAt: Date.now() - total };
            try {
                window.OTA = window.OTA || {};
                window.OTA.loaderStats = result;
            } catch (_) { /* ignore */ }
            return result;
        }

        async loadAll() {
            const manifest = window.OTA && window.OTA.scriptManifest;
            if (!manifest) throw new Error('scriptManifest not found');
            return this.loadPhases(manifest);
        }
    }

    // Expose
    const loader = new ScriptLoader();
    window.OTA.ScriptLoader = loader;
    window.OTA.loadAllScripts = () => loader.loadAll();

    // Optional auto-start: if manifest is already present and document is not loading, start immediately
    function maybeAutoStart() {
        if (loader.started) return;
        if (window.OTA && window.OTA.scriptManifest) {
            loader.loadAll().catch(err => console.error('ScriptLoader failed:', err));
        }
    }

    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        // Give manifest a tick to attach
        setTimeout(maybeAutoStart, 0);
    } else {
        document.addEventListener('DOMContentLoaded', () => setTimeout(maybeAutoStart, 0));
    }

    console.log('✅ ScriptLoader ready');
})();
