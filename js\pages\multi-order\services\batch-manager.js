/**
 * 批量管理服务
 * 文件: js/pages/multi-order/services/batch-manager.js
 * 角色: 新的独立批量管理服务，整合自batch-processor.js
 * 
 * @BATCH_MANAGER 批量管理服务
 * 🏷️ 标签: @OTA_BATCH_MANAGER_V2
 * 📝 说明: 负责批量处理多个订单，简化的一次性批量处理
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Services = window.OTA.Services || {};

(function() {
    'use strict';

    /**
     * 批量管理服务类
     * 负责批量处理订单，简化版本，移除暂停/恢复功能
     */
    class BatchManager {
        constructor(config = {}) {
            this.config = {
                maxConcurrentRequests: config.maxConcurrentRequests || 3,
                requestDelay: config.requestDelay || 1000,
                maxRetries: config.maxRetries || 2,
                timeout: config.timeout || 30000,
                enableProgressTracking: config.enableProgressTracking !== false,
                enableDetailedLogging: config.enableDetailedLogging !== false,
                ...config
            };

            this.state = {
                isProcessing: false,
                currentBatch: null,
                startTime: null,
                progress: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    processing: 0
                },
                results: []
            };

            this.logger = this.getLogger();
            this.logger.log('🔄 批量管理服务已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 处理批量订单
         * @param {Array} orders - 订单列表
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processBatch(orders, options = {}) {
            if (this.state.isProcessing) {
                throw new Error('批量处理正在进行中，请等待完成');
            }

            if (!Array.isArray(orders) || orders.length === 0) {
                throw new Error('订单列表不能为空');
            }

            try {
                this.initializeBatch(orders, options);
                
                this.logger.log(`🚀 开始批量处理 ${orders.length} 个订单`, 'info');

                // 创建处理任务
                const processingTasks = orders.map((order, index) => ({
                    order,
                    index,
                    retryCount: 0,
                    status: 'pending'
                }));

                // 执行批量处理
                const results = await this.processTasksSequentially(processingTasks, options);

                // 生成处理摘要
                const summary = this.generateBatchSummary(results);
                
                this.logger.log('✅ 批量处理完成', 'success', summary);
                
                return {
                    success: true,
                    summary,
                    results,
                    totalTime: Date.now() - this.state.startTime,
                    processedAt: new Date().toISOString()
                };

            } catch (error) {
                this.logger.logError('批量处理失败', error);
                return {
                    success: false,
                    error: error.message,
                    partialResults: this.state.results,
                    totalTime: Date.now() - this.state.startTime
                };
            } finally {
                this.cleanup();
            }
        }

        /**
         * 初始化批量处理
         * @param {Array} orders - 订单列表
         * @param {Object} options - 选项
         */
        initializeBatch(orders, options) {
            this.state.isProcessing = true;
            this.state.currentBatch = { orders, options };
            this.state.startTime = Date.now();
            this.state.progress = {
                total: orders.length,
                completed: 0,
                failed: 0,
                processing: 0
            };
            this.state.results = [];
        }

        /**
         * 顺序处理任务（简化版本，不使用并发）
         * @param {Array} tasks - 任务列表
         * @param {Object} options - 选项
         * @returns {Promise<Array>} 处理结果
         */
        async processTasksSequentially(tasks, options) {
            const results = [];

            for (let i = 0; i < tasks.length; i++) {
                const task = tasks[i];
                
                try {
                    // 更新进度
                    this.updateProcessingState(task.index, 'processing');
                    
                    // 调用进度回调
                    if (options.onProgress) {
                        options.onProgress(this.getProgressInfo(), task);
                    }

                    // 处理单个任务
                    const result = await this.processTask(task, options);
                    results.push(result);
                    
                    // 更新状态
                    this.updateProcessingState(task.index, result.success ? 'completed' : 'failed');
                    
                    // 调用完成回调
                    if (options.onTaskComplete) {
                        options.onTaskComplete(task.index, result);
                    }

                    // 添加延迟以避免API过载
                    if (i < tasks.length - 1) {
                        await this.delay(this.config.requestDelay);
                    }

                } catch (error) {
                    this.logger.logError(`任务 ${task.index + 1} 处理失败`, error);
                    
                    const errorResult = {
                        success: false,
                        orderIndex: task.index,
                        order: task.order,
                        error: error.message,
                        retryCount: task.retryCount,
                        processingTime: Date.now() - this.state.startTime
                    };
                    
                    results.push(errorResult);
                    this.updateProcessingState(task.index, 'failed');
                    
                    if (options.onTaskComplete) {
                        options.onTaskComplete(task.index, errorResult);
                    }
                }
            }

            return results;
        }

        /**
         * 处理单个任务
         * @param {Object} task - 任务对象
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 处理结果
         */
        async processTask(task, options) {
            const { order, index } = task;
            const startTime = Date.now();
            
            try {
                // 处理订单
                const result = await this.processOrder(order, index, options);
                
                return {
                    success: true,
                    orderIndex: index,
                    order,
                    result,
                    processingTime: Date.now() - startTime
                };

            } catch (error) {
                // 重试逻辑
                if (task.retryCount < this.config.maxRetries) {
                    task.retryCount++;
                    this.logger.log(`🔄 重试订单 ${index + 1} (第 ${task.retryCount} 次)`, 'warning');
                    
                    // 重试延迟
                    await this.delay(this.config.requestDelay * task.retryCount);
                    
                    return await this.processTask(task, options);
                }

                throw error;
            }
        }

        /**
         * 处理单个订单
         * @param {Object} order - 订单数据
         * @param {number} index - 订单索引
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(order, index, options) {
            // 获取API服务
            const apiService = this.getApiService();
            if (!apiService) {
                throw new Error('API服务不可用');
            }

            // 准备订单数据
            const orderData = this.prepareOrderData(order, index);
            
            // 创建订单
            const response = await this.createOrderWithTimeout(apiService, orderData);
            
            if (this.config.enableDetailedLogging) {
                this.logger.log(`✅ 订单 ${index + 1} 创建成功`, 'success', {
                    订单ID: response.id || response.orderId,
                    客户: orderData.customer_name
                });
            }

            // 保存到历史记录
            await this.saveToHistory(orderData, response);

            return {
                apiResponse: response,
                orderData,
                createdAt: new Date().toISOString(),
                orderId: response.id || response.orderId
            };
        }

        /**
         * 准备订单数据
         * @param {Object} order - 原始订单数据
         * @param {number} index - 订单索引
         * @returns {Object} 处理后的订单数据
         */
        prepareOrderData(order, index) {
            const cleanedOrder = {
                ...order,
                // 添加批量处理标识
                _batchProcessing: {
                    batchId: this.generateBatchId(),
                    orderIndex: index,
                    processedAt: new Date().toISOString()
                }
            };

            // 移除内部字段
            delete cleanedOrder._processed;
            delete cleanedOrder._metadata;
            delete cleanedOrder._validation;
            delete cleanedOrder._original;

            return cleanedOrder;
        }

        /**
         * 带超时的订单创建
         * @param {Object} apiService - API服务
         * @param {Object} orderData - 订单数据
         * @returns {Promise<Object>} API响应
         */
        async createOrderWithTimeout(apiService, orderData) {
            return new Promise((resolve, reject) => {
                const timeoutId = setTimeout(() => {
                    reject(new Error(`订单创建超时 (${this.config.timeout}ms)`));
                }, this.config.timeout);

                apiService.createOrder(orderData)
                    .then(response => {
                        clearTimeout(timeoutId);
                        resolve(response);
                    })
                    .catch(error => {
                        clearTimeout(timeoutId);
                        reject(error);
                    });
            });
        }

        /**
         * 保存到历史记录
         * @param {Object} orderData - 订单数据
         * @param {Object} response - API响应
         */
        async saveToHistory(orderData, response) {
            try {
                const historyManager = this.getHistoryManager();
                if (!historyManager) {
                    this.logger.log('⚠️ 未找到历史管理器，跳过历史记录保存', 'warning');
                    return;
                }

                // 方法1：新的历史管理器 (saveOrder)
                if (typeof historyManager.saveOrder === 'function') {
                    const apiResult = {
                        success: true,
                        response: response,
                        orderId: response.id || response.orderId,
                        timestamp: Date.now()
                    };

                    await historyManager.saveOrder(orderData, apiResult);
                    this.logger.log('✅ 已通过新历史管理器保存', 'success');
                    return;
                }

                // 方法2：旧的历史管理器 (addToHistory)
                if (typeof historyManager.addToHistory === 'function') {
                    const historyRecord = {
                        id: this.generateId(),
                        customer_name: orderData.customer_name || '未知客户',
                        pickup_location: orderData.pickup_location || '',
                        dropoff_location: orderData.dropoff_location || '',
                        customer_phone: orderData.customer_phone || '',
                        ota: orderData.ota || 'multi-order-v2',
                        timestamp: Date.now(),
                        success: true,
                        apiResult: response,
                        orderId: response.id || response.orderId,
                        source: 'batch_processing'
                    };

                    historyManager.addToHistory(historyRecord);
                    this.logger.log('✅ 已通过旧历史管理器保存', 'success');
                    return;
                }

                // 方法3：其他接口 (addOrder)
                if (typeof historyManager.addOrder === 'function') {
                    await historyManager.addOrder({
                        ...orderData,
                        id: response.id || response.orderId,
                        status: 'completed',
                        createdAt: new Date().toISOString(),
                        source: 'batch_processing'
                    });
                    this.logger.log('✅ 已通过通用历史管理器保存', 'success');
                    return;
                }

                this.logger.log('⚠️ 历史管理器接口不兼容', 'warning');

            } catch (error) {
                this.logger.logError('保存历史记录失败', error);
                // 不抛出错误，避免影响主流程
            }
        }

        /**
         * 更新处理状态
         * @param {number} index - 订单索引
         * @param {string} status - 状态
         */
        updateProcessingState(index, status) {
            if (status === 'processing') {
                this.state.progress.processing++;
            } else if (status === 'completed') {
                this.state.progress.processing--;
                this.state.progress.completed++;
            } else if (status === 'failed') {
                this.state.progress.processing--;
                this.state.progress.failed++;
            }
        }

        /**
         * 获取进度信息
         * @returns {Object} 进度信息
         */
        getProgressInfo() {
            const { total, completed, failed, processing } = this.state.progress;
            return {
                total,
                completed,
                failed,
                processing,
                remaining: total - completed - failed,
                completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
                failureRate: total > 0 ? Math.round((failed / total) * 100) : 0,
                isComplete: completed + failed >= total
            };
        }

        /**
         * 生成批量处理汇总
         * @param {Array} results - 处理结果
         * @returns {Object} 汇总信息
         */
        generateBatchSummary(results) {
            const successful = results.filter(r => r && r.success);
            const failed = results.filter(r => r && !r.success);
            
            return {
                total: results.length,
                successful: successful.length,
                failed: failed.length,
                successRate: results.length > 0 ? Math.round((successful.length / results.length) * 100) : 0,
                failureRate: results.length > 0 ? Math.round((failed.length / results.length) * 100) : 0,
                errors: failed.map(f => ({
                    orderIndex: f.orderIndex,
                    error: f.error,
                    retryCount: f.retryCount || 0
                })),
                successfulOrders: successful.map(s => ({
                    orderIndex: s.orderIndex,
                    orderId: s.result?.orderId || s.result?.apiResponse?.id,
                    customerName: s.order?.customer_name
                }))
            };
        }

        /**
         * 清理批量处理状态
         */
        cleanup() {
            this.state.isProcessing = false;
            this.state.currentBatch = null;
            this.state.startTime = null;
        }

        /**
         * 生成批次ID
         * @returns {string} 批次ID
         */
        generateBatchId() {
            return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 延迟函数
         * @param {number} ms - 毫秒数
         * @returns {Promise<void>}
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取API服务
         * @returns {Object|null} API服务实例
         */
        getApiService() {
            return window.getApiService?.() || 
                   window.OTA?.apiService || 
                   window.getService?.('apiService') ||
                   null;
        }

        /**
         * 获取历史记录管理器
         * @returns {Object|null} 历史记录管理器实例
         */
        getHistoryManager() {
            // 优先级1：新的历史管理器
            if (window.OTA?.historyManager) {
                return window.OTA.historyManager;
            }

            // 优先级2：旧的订单历史管理器
            if (window.getOrderHistoryManager) {
                return window.getOrderHistoryManager();
            }

            // 优先级3：其他可能的历史管理器
            return window.OTA?.orderHistoryManager ||
                   window.getService?.('orderHistoryManager') ||
                   null;
        }

        /**
         * 检查是否正在处理
         * @returns {boolean} 是否正在处理
         */
        isProcessing() {
            return this.state.isProcessing;
        }

        /**
         * 获取当前进度
         * @returns {Object} 当前进度信息
         */
        getCurrentProgress() {
            return this.getProgressInfo();
        }

        /**
         * 更新配置
         * @param {Object} newConfig - 新配置
         */
        updateConfig(newConfig) {
            this.config = {
                ...this.config,
                ...newConfig
            };
            this.logger.log('🔧 批量管理服务配置已更新', 'info');
        }

        /**
         * 销毁服务
         */
        destroy() {
            this.cleanup();
            this.config = null;
            this.state = null;
            this.logger.log('🗑️ 批量管理服务已销毁', 'info');
        }
    }

    // 创建全局批量管理服务实例
    const batchManager = new BatchManager();

    // 暴露到OTA命名空间
    window.OTA.Services.BatchManager = BatchManager;
    window.OTA.Services.batchManager = batchManager;

    // 向后兼容
    window.batchManager = batchManager;

    console.log('✅ 批量管理服务已加载');

})();
