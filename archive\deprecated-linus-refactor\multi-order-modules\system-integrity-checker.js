/**
 * 依赖标签（Dependency Tags）
 * 文件: js/multi-order/system-integrity-checker.js
 * 角色: 多订单系统一致性/健康检查（依赖可用性、映射完整性、UI绑定）
 * 上游依赖(直接使用):
 *  - Logger(getLogger)
 *  - ServiceLocator/DI（ApiService, LanguageManager, UIManager）
 *  - 内置字段映射（架构简化后）
 * 下游被依赖(常见调用方):
 *  - MultiOrderCoordinator / UI调试页面
 * 事件: 输出诊断报告；可触发警告日志
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_INTEGRITY_CHECKER 多订单系统完整性检查器
 * 🏷️ 标签: @OTA_SYSTEM_INTEGRITY_CHECKER
 * 📝 说明: 验证所有多订单模块是否正确加载和连接
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.SystemIntegrityChecker) {
    console.log('系统完整性检查器已存在，跳过重复加载');
} else {

/**
 * 系统完整性检查器类
 */
class SystemIntegrityChecker {
    constructor() {
        this.checkResults = [];
        this.logger = this.getLogger();
    }

    /**
     * 运行完整性检查
     * @returns {Promise<object>} 检查结果
     */
    async runIntegrityCheck() {
        this.logger.log('🔍 开始系统完整性检查', 'info');
        this.checkResults = [];

        // 1. 检查依赖关系
        await this.checkDependencies();

        // 2. 检查注册机制
        await this.checkRegistrations();

        // 3. 检查事件系统
        await this.checkEventSystem();

        // 4. 检查初始化逻辑
        await this.checkInitialization();

        // 5. 检查向后兼容性
        await this.checkBackwardCompatibility();

        const summary = this.generateSummary();
        this.logger.log('🏁 系统完整性检查完成', 'info', summary);
        
        return summary;
    }

    /**
     * 检查依赖关系
     */
    async checkDependencies() {
        this.logger.log('🔗 检查依赖关系...', 'info');
        
        const requiredModules = [
            'MultiOrderDetector',
            'MultiOrderProcessor',
            'MultiOrderRenderer',
            'BatchProcessor',
            'MultiOrderStateManager',
            'MultiOrderCoordinator',
            // 减法修复：MultiOrderManagerV2 已被 MultiOrderManagerAdapter 替代
            'MultiOrderManagerAdapter'
        ];

        for (const moduleName of requiredModules) {
            const exists = window.OTA && window.OTA[moduleName] !== undefined;
            this.checkResults.push({
                category: 'Dependencies',
                test: `模块存在: ${moduleName}`,
                status: exists ? 'PASS' : 'FAIL',
                details: exists ? '模块已加载' : '模块缺失'
            });
        }
    }

    /**
     * 检查注册机制
     * 减法修复：优化服务检查逻辑，避免触发降级警告
     */
    async checkRegistrations() {
        this.logger.log('📋 检查服务注册...', 'info');

        const requiredServices = [
            'multiOrderDetector',
            'multiOrderProcessor',
            'multiOrderRenderer',
            'batchProcessor',
            'multiOrderStateManager',
            'multiOrderCoordinator',
            // 减法修复：multiOrderManagerV2 已被 MultiOrderManagerAdapter 替代
            'multiOrderManagerAdapter'
        ];

        for (const serviceName of requiredServices) {
            // 浏览器兼容性修复：使用更健壮的服务检查逻辑
            let registered = false;
            let checkMethod = 'unknown';

            try {
                // 方法1：检查依赖容器中是否有该服务
                if (window.OTA?.container?.has?.(serviceName)) {
                    registered = true;
                    checkMethod = 'container';
                }
                // 方法2：检查fallbackMap中是否有该服务，并尝试获取
                else if (window.OTA?.serviceLocator?.fallbackMap?.has?.(serviceName)) {
                    try {
                        const service = window.OTA.serviceLocator.fallbackMap.get(serviceName)();
                        registered = !!service;
                        checkMethod = 'fallback';
                    } catch (fallbackError) {
                        // fallback 获取失败，继续其他检查方法
                        registered = false;
                    }
                }
                // 方法3：检查全局对象中是否有该服务
                else if (window.OTA?.[serviceName] || window[serviceName]) {
                    registered = true;
                    checkMethod = 'global';
                }
                // 方法4：特殊处理 multiOrderManagerAdapter（兼容性检查）
                else if (serviceName === 'multiOrderManagerAdapter') {
                    if (window.OTA?.multiOrderManagerAdapter ||
                        window.OTA?.multiOrderManager ||
                        window.multiOrderManager) {
                        registered = true;
                        checkMethod = 'adapter-compat';
                    }
                }
            } catch (error) {
                // 静默处理检查错误，避免干扰系统运行
                registered = false;
                checkMethod = 'error';
            }

            this.checkResults.push({
                category: 'Registration',
                test: `服务注册: ${serviceName}`,
                status: registered ? 'PASS' : 'FAIL',
                details: registered ? `服务已注册 (${checkMethod})` : '服务未注册'
            });
        }
    }

    /**
     * 检查事件系统
     */
    async checkEventSystem() {
        this.logger.log('🎭 检查事件系统...', 'info');
        
        const coordinator = window.OTA?.multiOrderCoordinator;
        if (!coordinator) {
            this.checkResults.push({
                category: 'Events',
                test: '协调器事件系统',
                status: 'FAIL',
                details: '协调器不存在'
            });
            return;
        }

        // 检查事件方法存在
        const eventMethods = ['addEventListener', 'dispatchEvent'];
        for (const method of eventMethods) {
            const exists = typeof coordinator[method] === 'function';
            this.checkResults.push({
                category: 'Events',
                test: `事件方法: ${method}`,
                status: exists ? 'PASS' : 'FAIL',
                details: exists ? '方法存在' : '方法缺失'
            });
        }
    }

    /**
     * 检查初始化逻辑
     */
    async checkInitialization() {
        this.logger.log('🚀 检查初始化逻辑...', 'info');
        
        const manager = window.OTA && window.OTA.multiOrderManager;
        if (!manager) {
            this.checkResults.push({
                category: 'Initialization',
                test: '主管理器初始化',
                status: 'FAIL',
                details: '主管理器不存在'
            });
            return;
        }

        // 检查关键方法
        const requiredMethods = [
            'analyzeInputForMultiOrder',
            'showMultiOrderPanel',
            'toggleMultiOrderMode',
            'createSelectedOrders',
            'getState'
        ];

        for (const method of requiredMethods) {
            const exists = typeof manager[method] === 'function';
            this.checkResults.push({
                category: 'Initialization',
                test: `方法存在: ${method}`,
                status: exists ? 'PASS' : 'FAIL',
                details: exists ? '方法可用' : '方法缺失'
            });
        }
    }

    /**
     * 检查向后兼容性
     */
    async checkBackwardCompatibility() {
        this.logger.log('🔄 检查向后兼容性...', 'info');
        
        // 检查全局对象
        const globalObjects = [
            'window.OTA.multiOrderManager',
            'window.MultiOrderDetector', // 向后兼容
            'window.MultiOrderProcessor' // 向后兼容
        ];

        for (const objPath of globalObjects) {
            const exists = this.getNestedObject(window, objPath.replace('window.', '')) !== undefined;
            this.checkResults.push({
                category: 'Compatibility',
                test: `全局对象: ${objPath}`,
                status: exists ? 'PASS' : 'FAIL',
                details: exists ? '对象可访问' : '对象不存在'
            });
        }

        // 检查旧版管理器备份
        const legacyExists = window.OTA?.multiOrderManagerLegacy !== undefined;
        this.checkResults.push({
            category: 'Compatibility',
            test: '旧版管理器备份',
            status: legacyExists ? 'PASS' : 'INFO',
            details: legacyExists ? '已备份旧版管理器' : '无旧版管理器需要备份'
        });
    }

    /**
     * 生成检查汇总
     * @returns {object} 汇总结果
     */
    generateSummary() {
        const totalTests = this.checkResults.length;
        const passed = this.checkResults.filter(r => r.status === 'PASS').length;
        const failed = this.checkResults.filter(r => r.status === 'FAIL').length;
        const warnings = this.checkResults.filter(r => r.status === 'INFO').length;

        const categories = {};
        this.checkResults.forEach(result => {
            if (!categories[result.category]) {
                categories[result.category] = { pass: 0, fail: 0, info: 0 };
            }
            categories[result.category][result.status.toLowerCase()]++;
        });

        return {
            overall: failed === 0 ? 'HEALTHY' : 'ISSUES_FOUND',
            summary: {
                total: totalTests,
                passed: passed,
                failed: failed,
                warnings: warnings,
                successRate: Math.round((passed / totalTests) * 100)
            },
            categories: categories,
            failedTests: this.checkResults.filter(r => r.status === 'FAIL'),
            results: this.checkResults
        };
    }

    /**
     * 获取嵌套对象
     * @param {object} obj - 根对象
     * @param {string} path - 路径
     * @returns {any} 值
     */
    getNestedObject(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 获取Logger实例
     * @returns {object} Logger实例
     */
    getLogger() {
        if (typeof getLogger === 'function') {
            return getLogger();
        }
        return {
            log: (message, level, data) => {
                console.log(`[INTEGRITY][${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
            }
        };
    }

    /**
     * 浏览器兼容性检测
     * @returns {object} 浏览器信息
     */
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        // 使用更兼容的浏览器检测方法
        const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent);
        const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
        const isEdge = /Edg/.test(userAgent);
        const isFirefox = /Firefox/.test(userAgent);

        return {
            isChrome,
            isSafari,
            isEdge,
            isFirefox,
            userAgent,
            // Safari 和 Edge 对 Promise 错误更严格
            isStrictErrorHandling: isSafari || isEdge
        };
    }
}

// 创建全局实例
const systemIntegrityChecker = new SystemIntegrityChecker();

// 暴露到OTA命名空间
window.OTA = window.OTA || {};
window.OTA.SystemIntegrityChecker = SystemIntegrityChecker;
window.OTA.systemIntegrityChecker = systemIntegrityChecker;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('systemIntegrityChecker', systemIntegrityChecker, '@OTA_SYSTEM_INTEGRITY_CHECKER');
}

// 浏览器兼容性修复：安全的自动检查执行
// 确保检查失败不会阻止应用运行，特别是在 Safari 和 Edge 中
async function safeRunIntegrityCheck() {
    try {
        await systemIntegrityChecker.runIntegrityCheck();
    } catch (error) {
        // 降级处理：将检查错误记录为警告，不阻止应用运行
        console.warn('🔍 系统完整性检查遇到问题，但不影响应用运行:', error.message);

        // 尝试记录到日志系统（如果可用）
        try {
            const logger = systemIntegrityChecker.getLogger();
            logger.log('系统完整性检查失败，但应用继续运行', 'warning', { error: error.message });
        } catch (logError) {
            // 静默处理日志错误
        }
    }
}

// 延长等待时间，确保所有服务都已注册（特别是在较慢的浏览器中）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(safeRunIntegrityCheck, 3000); // 增加到3秒，提高兼容性
    });
} else {
    setTimeout(safeRunIntegrityCheck, 3000);
}

console.log('✅ 系统完整性检查器已加载');

}