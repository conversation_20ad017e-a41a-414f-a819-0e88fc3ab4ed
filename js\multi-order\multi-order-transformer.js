// build-stamp: version 2.0.3 (multi-order-transformer)
/**
 * 依赖标签（Dependency Tags）
 * 文件: js/multi-order/multi-order-transformer.js
 * 角色: 字段映射与标准化（将AI解析结果→表单/接口字段；处理语言/地区/车型等映射）
 * 上游依赖(直接使用):
 *  - <PERSON><PERSON>(getLogger)
 *  - 内置字段映射配置（架构简化后，无外部依赖）
 *  - LanguageManager（语言/地区转换）
 * 下游被依赖(常见调用方):
 *  - Detector（初步拆单后标准化）
 *  - Processor（创建前最终转换）
 * 事件: 无直接事件，作为纯函数/工具使用
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_TRANSFORMER 多订单数据转换器
 * 🏷️ 标签: @OTA_MULTI_ORDER_TRANSFORMER
 * 📝 说明: 负责字段映射和格式化，确保数据转换的完整性和一致性
 * ⚠️ 警告: 已注册，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderTransformer) {
    console.log('多订单数据转换器已存在，跳过重复加载');
} else {

/**
 * 多订单数据转换器类
 * 提供字段映射、数据验证和格式化功能
 */
class MultiOrderTransformer {
    constructor(config = {}) {
        this.config = {
            strictValidation: config.strictValidation || false,
            preserveOriginalFields: config.preserveOriginalFields || true,
            ...config
        };
        
        this.logger = this.getLogger();
        this.fieldMappingConfig = this.getFieldMappingConfig();
        this.validator = this.getValidator();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 获取字段映射配置
     * @returns {Object} 字段映射配置
     */
    getFieldMappingConfig() {
        const legacyConfig = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
        
        if (legacyConfig) {
            // 如果仍有遗留配置，使用它（向后兼容）
            return legacyConfig;
        }
        
        // 🧹 架构简化：使用简化的内置配置（与FormManager保持一致）
        return {
            AI_TO_FRONTEND: {
                'customer_name': 'customerName',
                'customer_contact': 'customerContact', 
                'customer_email': 'customerEmail',
                'pickup': 'pickup',
                'destination': 'dropoff',
                'date': 'pickupDate',
                'arrival_time': 'pickupTime',
                'departure_time': 'pickupTime',
                'pickup_time': 'pickupTime',
                'time': 'pickupTime',
                'passenger_number': 'passengerCount',
                'luggage_number': 'luggageCount',
                'ota_price': 'otaPrice',
                'ota_reference_number': 'otaReferenceNumber',
                'extra_requirement': 'extraRequirement',
                'sub_category_id': 'subCategoryId',
                'car_type_id': 'carTypeId',
                'driving_region_id': 'drivingRegionId'
            },
            ALTERNATIVE_FIELDS: {
                'pickup': ['pickupLocation', 'pickup_location'],
                'destination': ['dropoff', 'dropoffLocation', 'dropoff_location'],
                // 🚀 修复：时间字段优先级映射，确保arrival_time正确映射到时间
                'pickupTime': ['arrival_time', 'departure_time', 'pickup_time', 'time']
            }
        };
    }

    /**
     * 获取字段映射验证器
     * @returns {Object} 验证器实例
     */
    getValidator() {
        return window.OTA?.FieldMappingValidator || window.FieldMappingValidator;
    }

    /**
     * 转换订单数据（主要入口方法）
     * @param {Object} orderData - 原始订单数据
     * @returns {Object} 转换后的订单数据
     */
    transformOrderData(orderData) {
        if (!orderData || typeof orderData !== 'object') {
            this.logger?.logError('无效的订单数据', { orderData });
            return orderData;
        }

        try {
            this.logger?.log('🔄 开始数据转换', 'info', { 
                originalFields: Object.keys(orderData).length 
            });

            // 记录输入数据中的关键字段
            const inputKeyFields = {
                car_type_id: orderData.car_type_id,
                carTypeId: orderData.carTypeId,
                driving_region_id: orderData.driving_region_id,
                drivingRegionId: orderData.drivingRegionId
            };
            this.logger?.log('🔍 转换前输入数据分析', 'info', {
                keyFields: inputKeyFields,
                hasSnakeCase: !!(orderData.car_type_id || orderData.driving_region_id),
                hasCamelCase: !!(orderData.carTypeId || orderData.drivingRegionId)
            });

            // 1. 应用备用字段映射
            let transformedData = this.applyAlternativeFieldMapping({ ...orderData });

            // 2. 应用AI到前端字段映射
            transformedData = this.applyAIToFrontendMapping(transformedData);

            // 3. 验证和格式化字段
            transformedData = this.validateAndFormatOrderFields(transformedData);

            // 4. 处理Paging服务
            transformedData = this.processPagingServiceForOrder(transformedData);

            // 5. 处理中文语言检测
            transformedData = this.processChineseLanguageDetection(transformedData);

            // 6. 应用一致性数据处理
            transformedData = this.applyConsistentDataProcessing(transformedData);

            // 记录最终转换结果中的关键字段
            const outputKeyFields = {
                car_type_id: transformedData.car_type_id,
                carTypeId: transformedData.carTypeId,
                driving_region_id: transformedData.driving_region_id,
                drivingRegionId: transformedData.drivingRegionId
            };
            
            // 数据完整性验证
            const validationReport = {
                carTypeId: {
                    hasOriginal: !!(transformedData.carTypeId || transformedData.car_type_id),
                    value: transformedData.carTypeId || transformedData.car_type_id,
                    source: transformedData.carTypeId ? 'camelCase' : 'snake_case'
                },
                drivingRegionId: {
                    hasOriginal: !!(transformedData.drivingRegionId || transformedData.driving_region_id),
                    value: transformedData.drivingRegionId || transformedData.driving_region_id,
                    source: transformedData.drivingRegionId ? 'camelCase' : 'snake_case'
                }
            };
            
            this.logger?.log('✅ 数据转换完成', 'success', { 
                transformedFields: Object.keys(transformedData).length,
                finalKeyFields: outputKeyFields,
                validationReport: validationReport,
                dataIntegrity: {
                    carTypeIdPreserved: validationReport.carTypeId.hasOriginal,
                    drivingRegionIdPreserved: validationReport.drivingRegionId.hasOriginal
                }
            });
            
            // 警告检测：如果原始值丢失
            if (!validationReport.carTypeId.hasOriginal) {
                this.logger?.log('⚠️ 警告：车型ID数据可能丢失', 'warning', {
                    availableFields: Object.keys(transformedData),
                    potentialIssue: 'car_type_id_not_found'
                });
            }
            if (!validationReport.drivingRegionId.hasOriginal) {
                this.logger?.log('⚠️ 警告：区域ID数据可能丢失', 'warning', {
                    availableFields: Object.keys(transformedData),
                    potentialIssue: 'driving_region_id_not_found'
                });
            }

            return transformedData;

        } catch (error) {
            this.logger?.logError('数据转换失败', error);
            return orderData; // 返回原始数据作为降级方案
        }
    }

    /**
     * 应用备用字段映射
     * @param {Object} order - 订单对象
     * @returns {Object} 映射后的订单对象
     */
    applyAlternativeFieldMapping(order) {
        if (!this.fieldMappingConfig.ALTERNATIVE_FIELDS) {
            return order;
        }

        const mappedOrder = { ...order };

        // 🚀 修复：正确处理null值的备用字段映射规则
        Object.entries(this.fieldMappingConfig.ALTERNATIVE_FIELDS).forEach(([primaryField, alternatives]) => {
            // 修复null值判断：字段不存在或值为null/undefined/空字符串时才进行映射
            if (!mappedOrder.hasOwnProperty(primaryField) ||
                mappedOrder[primaryField] === null ||
                mappedOrder[primaryField] === undefined ||
                mappedOrder[primaryField] === '') {

                for (const altField of alternatives) {
                    // 确保备用字段存在且有有效值
                    if (mappedOrder.hasOwnProperty(altField) &&
                        mappedOrder[altField] !== null &&
                        mappedOrder[altField] !== undefined &&
                        mappedOrder[altField] !== '') {

                        mappedOrder[primaryField] = mappedOrder[altField];
                        this.logger?.log(`🔄 字段映射: ${altField} → ${primaryField} (值: ${mappedOrder[altField]})`, 'info');

                        // 根据配置决定是否保留原始字段
                        if (!this.config.preserveOriginalFields) {
                            delete mappedOrder[altField];
                        }
                        break;
                    }
                }
            }
        });

        return mappedOrder;
    }

    /**
     * 应用AI到前端字段映射
     * @param {Object} order - 订单对象
     * @returns {Object} 映射后的订单对象
     */
    applyAIToFrontendMapping(order) {
        if (!this.fieldMappingConfig.AI_TO_FRONTEND) {
            return order;
        }

        const mappedOrder = { ...order };
        
        // 特别追踪关键字段的映射过程
        const keyFields = ['car_type_id', 'driving_region_id'];
        const mappingResults = {};

        // 应用AI到前端的字段映射
        Object.entries(this.fieldMappingConfig.AI_TO_FRONTEND).forEach(([aiField, frontendField]) => {
            if (order.hasOwnProperty(aiField) && !mappedOrder.hasOwnProperty(frontendField)) {
                const originalValue = order[aiField];
                mappedOrder[frontendField] = originalValue;
                
                // 详细记录关键字段的映射
                if (keyFields.includes(aiField)) {
                    mappingResults[aiField] = {
                        aiField: aiField,
                        frontendField: frontendField,
                        value: originalValue,
                        status: 'mapped_successfully'
                    };
                    this.logger?.log(`🔑 关键字段映射: ${aiField} → ${frontendField}`, 'info', {
                        originalValue: originalValue,
                        fieldType: aiField === 'car_type_id' ? 'vehicle_type' : 'region',
                        mappingDirection: 'ai_to_frontend'
                    });
                } else {
                    this.logger?.log(`🤖 AI字段映射: ${aiField} → ${frontendField}`, 'info');
                }
                
                // 根据配置决定是否保留原始字段
                if (!this.config.preserveOriginalFields) {
                    delete mappedOrder[aiField];
                }
            }
        });
        
        // 记录映射摘要
        if (Object.keys(mappingResults).length > 0) {
            this.logger?.log('📊 关键字段映射摘要', 'info', {
                mappedFields: mappingResults,
                totalMappings: Object.keys(mappingResults).length
            });
        }

        return mappedOrder;
    }

    /**
     * 验证和格式化订单字段
     * @param {Object} order - 订单对象
     * @returns {Object} 验证和格式化后的订单对象
     */
    validateAndFormatOrderFields(order) {
        const processedOrder = { ...order };

        try {
            // 处理特殊字段规则
            if (this.fieldMappingConfig.SPECIAL_FIELD_RULES) {
                Object.entries(this.fieldMappingConfig.SPECIAL_FIELD_RULES).forEach(([fieldName, rules]) => {
                    if (processedOrder.hasOwnProperty(fieldName) && rules.toAPI) {
                        const originalValue = processedOrder[fieldName];
                        const processedValue = rules.toAPI(originalValue);
                        
                        if (processedValue !== originalValue) {
                            processedOrder[fieldName] = processedValue;
                            this.logger?.log(`🔧 字段格式化: ${fieldName}`, 'info', {
                                original: originalValue,
                                processed: processedValue
                            });
                        }
                    }
                });
            }

            // 特殊处理languages_id_array字段
            if (processedOrder.languagesIdArray && this.validator) {
                const languageResult = this.validator.validateLanguagesIdArrayFormat(processedOrder.languagesIdArray);
                if (languageResult.converted) {
                    processedOrder.languagesIdArray = languageResult.converted;
                    this.logger?.log('🔧 languages_id_array格式转换', 'info', languageResult);
                }
            }

            // 数据类型转换
            this.convertDataTypes(processedOrder);

            // 规范化：将行驶区域的名称/别名转换为数值ID
            this.normalizeDrivingRegionId(processedOrder);

            return processedOrder;

        } catch (error) {
            this.logger?.logError('字段验证和格式化失败', error);
            return order;
        }
    }

    /**
     * 将行驶区域值标准化为数值ID
     * 支持输入为名称、别名或缩写（例如 KL/PNG/JB/MLK/SBH/SG/PG/CHRT）
     * @param {Object} order - 订单对象（就地修改）
     */
    normalizeDrivingRegionId(order) {
        try {
            const value = order.drivingRegionId ?? order.driving_region_id;
            if (value === undefined || value === null || value === '') return;

            // 已是有效数字则直接返回
            const numeric = parseInt(value);
            if (!isNaN(numeric) && numeric > 0) {
                order.drivingRegionId = numeric;
                return;
            }

            // 从服务获取区域列表
            const api = window.OTA?.getService ? window.OTA.getService('apiService') : null;
            const regions = api?.staticData?.drivingRegions || [];
            if (!regions.length) return;

            const norm = (s) => (s || '').toString().toLowerCase().replace(/[^a-z0-9]+/g, '');
            const byId = new Map(regions.map(r => [String(r.id), r.id]));
            const byName = new Map();

            regions.forEach(r => {
                const nameKey = norm(r.name);
                if (nameKey) byName.set(nameKey, r.id);
                // 提取括号中的缩写，例如 "Kl/selangor (KL)" → KL
                const abbrMatch = r.name.match(/\(([^)]+)\)/);
                if (abbrMatch && abbrMatch[1]) {
                    abbrMatch[1].split('/').forEach(token => {
                        const k = norm(token);
                        if (k) byName.set(k, r.id);
                    });
                }
                // 同时把斜杠前部分也作为键，例如 Kl/selangor → klselangor
                const slashParts = r.name.split('(')[0].split('/');
                slashParts.forEach(p => {
                    const k = norm(p);
                    if (k) byName.set(k, r.id);
                });
            });

            const key = norm(value);
            // 先按ID字符串匹配，再按名称/缩写匹配
            let resolved = byId.get(value) || byName.get(key);
            if (resolved) {
                order.drivingRegionId = resolved;
            }
        } catch (_) { /* 忽略标准化错误，保持原值 */ }
    }

    /**
     * 数据类型转换
     * @param {Object} order - 订单对象
     */
    convertDataTypes(order) {
        // 数值字段转换
        const numericFields = ['passengerCount', 'luggageCount', 'otaPrice', 'subCategoryId', 'carTypeId', 'drivingRegionId'];
        numericFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                const numValue = parseFloat(order[field]);
                if (!isNaN(numValue)) {
                    order[field] = numValue;
                }
            }
        });

        // 整数字段转换
        const integerFields = ['passengerCount', 'luggageCount', 'subCategoryId', 'carTypeId', 'drivingRegionId'];
        integerFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                const intValue = parseInt(order[field]);
                if (!isNaN(intValue)) {
                    order[field] = intValue;
                }
            }
        });

        // 布尔字段转换
        const booleanFields = ['meetAndGreet', 'babyChair', 'tourGuide'];
        booleanFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                if (typeof order[field] === 'string') {
                    order[field] = order[field].toLowerCase() === 'true' || order[field] === '1';
                } else {
                    order[field] = Boolean(order[field]);
                }
            }
        });
    }

    /**
     * 处理Paging服务订单
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    processPagingServiceForOrder(order) {
        try {
            // 检测是否为Paging服务订单
            const isPagingOrder = this.detectPagingService(order);
            
            if (isPagingOrder) {
                const originalCarTypeId = order.carTypeId;
                order.is_paging_order = true;
                
                // 仅在没有提供有效车型ID时才设置为Paging专用车型ID
                if (!originalCarTypeId || originalCarTypeId <= 0) {
                    order.carTypeId = 34; // Paging服务的车型ID
                    this.logger?.log('🏷️ Paging服务：设置默认车型ID', 'info', {
                        action: 'set_default_paging_car_type',
                        newCarTypeId: 34,
                        reason: 'no_original_car_type'
                    });
                } else {
                    this.logger?.log('🏷️ Paging服务：保留原始车型ID', 'info', {
                        action: 'preserve_original_car_type',
                        originalCarTypeId: originalCarTypeId,
                        reason: 'has_valid_gemini_car_type'
                    });
                }
                
                // 服务分类仍然设置为接机（如果未提供）
                if (!order.subCategoryId) {
                    order.subCategoryId = 2; // 默认为接机服务
                }
                
                this.logger?.log('🏷️ 检测到Paging服务订单', 'info', {
                    customerName: order.customerName,
                    finalCarTypeId: order.carTypeId,
                    preservedOriginal: !!(originalCarTypeId && originalCarTypeId > 0)
                });
            }

            return order;

        } catch (error) {
            this.logger?.logError('Paging服务处理失败', error);
            return order;
        }
    }

    /**
     * 检测是否为Paging服务
     * @param {Object} order - 订单对象
     * @returns {boolean} 是否为Paging服务
     */
    detectPagingService(order) {
        // 检测关键词
        const pagingKeywords = [
            '举牌', '接机牌', '接机服务', 'paging', 'meet and greet',
            '机场接待', '接机员', '举牌接机', '接机牌服务'
        ];

        const orderText = JSON.stringify(order).toLowerCase();
        
        return pagingKeywords.some(keyword => 
            orderText.includes(keyword.toLowerCase())
        );
    }

    /**
     * 处理中文语言检测
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    processChineseLanguageDetection(order) {
        try {
            // 检测客户姓名是否包含中文
            const hasChineseName = this.containsChinese(order.customerName || '');
            
            // 检测其他字段是否包含中文
            const fieldsToCheck = ['extraRequirement', 'pickup', 'dropoff'];
            const hasChineseContent = fieldsToCheck.some(field => 
                this.containsChinese(order[field] || '')
            );

            // 如果检测到中文，确保语言数组包含中文
            if (hasChineseName || hasChineseContent) {
                if (!order.languagesIdArray) {
                    order.languagesIdArray = [4, 2]; // 中文 + 英文
                } else if (Array.isArray(order.languagesIdArray) && !order.languagesIdArray.includes(4)) {
                    order.languagesIdArray.unshift(4); // 将中文添加到首位
                }
                
                this.logger?.log('🇨🇳 检测到中文内容，已添加中文语言', 'info', {
                    hasChineseName,
                    hasChineseContent,
                    languagesIdArray: order.languagesIdArray
                });
            }

            return order;

        } catch (error) {
            this.logger?.logError('中文语言检测失败', error);
            return order;
        }
    }

    /**
     * 检测文本是否包含中文字符
     * @param {string} text - 待检测文本
     * @returns {boolean} 是否包含中文
     */
    containsChinese(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        // 中文字符的Unicode范围
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
        return chineseRegex.test(text);
    }

    /**
     * 应用一致性数据处理
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    applyConsistentDataProcessing(order) {
        try {
            // 🎯 使用车型配置管理器获取默认车型ID
            let defaultCarTypeId = 5; // 降级默认值
            try {
                const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                if (vehicleConfigManager) {
                    defaultCarTypeId = vehicleConfigManager.getDefaultCarTypeId();
                    this.logger?.log('从车型配置管理器获取默认车型ID', 'debug', { defaultCarTypeId });
                }
            } catch (error) {
                this.logger?.log('车型配置管理器不可用，使用降级默认值', 'warning', { 
                    error: error.message, 
                    fallbackCarTypeId: defaultCarTypeId 
                });
            }

            // 确保必要字段存在默认值（但不覆盖Gemini提供的有效值）
            const defaults = {
                passengerCount: 1,
                luggageCount: 0,
                subCategoryId: 2, // 默认接机
                carTypeId: defaultCarTypeId, // 从配置管理器获取的默认车型
                drivingRegionId: 1, // 默认区域
                languagesIdArray: [2] // 默认英文
            };

            Object.entries(defaults).forEach(([field, defaultValue]) => {
                if (order[field] === undefined || order[field] === null || order[field] === '') {
                    order[field] = defaultValue;
                    this.logger?.log(`🔧 设置默认值: ${field} = ${defaultValue}`, 'info', {
                        field: field,
                        defaultValue: defaultValue,
                        reason: 'missing_or_empty_field'
                    });
                } else {
                    this.logger?.log(`✅ 保留原始值: ${field} = ${order[field]}`, 'info', {
                        field: field,
                        originalValue: order[field],
                        reason: 'field_has_valid_value'
                    });
                }
            });

            // 🚗 智能车型推荐：仅在未提供车型ID时根据乘客数量推荐
            // 不覆盖Gemini提供的有效车型ID
            if (order.passengerCount && order.passengerCount > 3) {
                try {
                    const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                    if (vehicleConfigManager) {
                        const recommendation = vehicleConfigManager.recommendCarType(order.passengerCount);
                        
                        // 检查是否有Gemini提供的原始车型ID
                        const hasOriginalCarType = order.carTypeId !== undefined && order.carTypeId !== null && order.carTypeId > 0;
                        
                        if (!hasOriginalCarType) {
                            // 仅在没有原始值时使用推荐值
                            order.carTypeId = recommendation.carTypeId;
                            this.logger?.log('🔄 使用智能车型推荐（无原始值）', 'info', {
                                passengerCount: order.passengerCount,
                                recommendedCarTypeId: recommendation.carTypeId,
                                recommendedName: recommendation.carTypeName,
                                reason: 'no_original_car_type_provided'
                            });
                        } else if (recommendation.carTypeId !== order.carTypeId) {
                            // 记录推荐差异但不覆盖原始值
                            this.logger?.log('⚠️ 车型推荐与原始值不同（保留原始）', 'warning', {
                                passengerCount: order.passengerCount,
                                originalCarTypeId: order.carTypeId,
                                recommendedCarTypeId: recommendation.carTypeId,
                                recommendedName: recommendation.carTypeName,
                                reason: 'preserving_original_gemini_value'
                            });
                        }
                    }
                } catch (error) {
                    this.logger?.log('智能车型推荐失败', 'warning', { error: error.message });
                }
            }

            // 确保价格字段为数值
            if (order.otaPrice) {
                order.otaPrice = parseFloat(order.otaPrice) || 0;
            }

            // 确保日期时间格式正确
            if (order.pickupDate && !/^\d{4}-\d{2}-\d{2}$/.test(order.pickupDate)) {
                // 尝试转换日期格式
                const dateObj = new Date(order.pickupDate);
                if (!isNaN(dateObj.getTime())) {
                    order.pickupDate = dateObj.toISOString().split('T')[0];
                    this.logger?.log('🔧 日期格式转换', 'info', { 
                        original: order.pickupDate, 
                        converted: order.pickupDate 
                    });
                }
            }

            // 确保时间格式正确
            if (order.pickupTime && !/^\d{2}:\d{2}$/.test(order.pickupTime)) {
                // 尝试提取时间部分
                const timeMatch = order.pickupTime.match(/(\d{1,2}):(\d{2})/);
                if (timeMatch) {
                    const hours = timeMatch[1].padStart(2, '0');
                    const minutes = timeMatch[2];
                    order.pickupTime = `${hours}:${minutes}`;
                    this.logger?.log('🔧 时间格式转换', 'info', { 
                        original: order.pickupTime, 
                        converted: order.pickupTime 
                    });
                }
            }

            return order;

        } catch (error) {
            this.logger?.logError('一致性数据处理失败', error);
            return order;
        }
    }

    /**
     * 转换为API格式
     * @param {Object} frontendData - 前端格式数据
     * @returns {Object} API格式数据
     */
    transformToAPIFormat(frontendData) {
        if (!this.fieldMappingConfig.FRONTEND_TO_API) {
            return frontendData;
        }

        const apiData = { ...frontendData };

        // 应用前端到API的字段映射
        Object.entries(this.fieldMappingConfig.FRONTEND_TO_API).forEach(([frontendField, apiField]) => {
            if (frontendData.hasOwnProperty(frontendField)) {
                apiData[apiField] = frontendData[frontendField];
                
                // 删除前端字段名（避免重复）
                if (frontendField !== apiField) {
                    delete apiData[frontendField];
                }
            }
        });

        // 特殊处理languages_id_array格式
        if (apiData.languages_id_array && this.validator) {
            const languageResult = this.validator.validateLanguagesIdArrayFormat(apiData.languages_id_array);
            apiData.languages_id_array = languageResult.converted;
        }

        return apiData;
    }

    /**
     * 验证字段映射完整性
     * @param {Object} originalData - 原始数据
     * @param {Object} transformedData - 转换后数据
     * @returns {Object} 验证结果
     */
    validateFieldMappingIntegrity(originalData, transformedData) {
        if (!this.validator) {
            return { success: true, message: '验证器不可用' };
        }

        try {
            const comparison = this.validator.compareFieldMapping(originalData, transformedData);
            
            const report = {
                success: comparison.identical || comparison.differences.length === 0,
                identical: comparison.identical,
                differences: comparison.differences,
                missingFields: comparison.missingInTransformed,
                addedFields: comparison.addedInTransformed,
                summary: comparison.summary
            };

            if (!report.success) {
                this.logger?.log('⚠️ 字段映射完整性检查发现问题', 'warn', report);
            }

            return report;

        } catch (error) {
            this.logger?.logError('字段映射完整性验证失败', error);
            return { success: false, error: error.message };
        }
    }
}

// 导出数据转换器
window.OTA = window.OTA || {};
window.OTA.MultiOrderTransformer = MultiOrderTransformer;

// 向后兼容
window.MultiOrderTransformer = MultiOrderTransformer;

console.log('✅ 多订单数据转换器已加载');

// 结束防重复加载检查
}
