/**
 * ============================================================================
 * 🚀 核心业务流程 - 订单解析器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 订单解析器 - 子层实现
 * @description 负责订单数据的结构化解析和验证，从gemini-service.js拆分而来
 * 
 * @businessFlow 订单数据解析
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API调用
 *     ↓
 * Gemini返回原始结果
 *     ↓
 * 【当前文件职责】订单数据结构化解析 - 本地处理
 *     ↓
 * 结果处理 → 订单管理
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：订单数据解析的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供订单解析服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * - flow/knowledge-base.js (知识库查询)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 单订单数据解析和验证
 * - 🟢 多订单检测和分割
 * - 🟢 订单字段标准化和格式化
 * - 🟢 数据完整性检查和修复
 * - 🟢 车型ID映射和价格计算
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地处理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有订单数据格式
 * - 兼容现有的解析逻辑
 * - 保持向后兼容的API接口
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用Gemini API（严格本地处理）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持解析准确率
 * - ✅ 保持现有的数据格式
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 订单解析器 - 子层实现
     */
    class OrderParser {
        constructor() {
            this.logger = this.getLogger();
            
            // 解析配置
            this.parseConfig = {
                requiredFields: ['customer_name', 'pickup_location'],
                optionalFields: ['dropoff_location', 'pickup_time', 'customer_contact'],
                defaultValues: {
                    passenger_count: 1,
                    luggage_count: 0,
                    languages_id_array: [2, 4], // 英文和中文
                    sub_category_id: null
                },
                validation: {
                    minNameLength: 2,
                    maxNameLength: 50,
                    minLocationLength: 3,
                    maxLocationLength: 200
                }
            };
            
            this.logger.log('订单解析器已初始化', 'info');
        }

        /**
         * 解析单个订单
         * @param {object} rawData - Gemini返回的原始数据
         * @param {object} channelInfo - 渠道信息
         * @param {object} options - 解析选项
         * @returns {Promise<object>} 解析后的订单数据
         */
        async parseSingleOrder(rawData, channelInfo = {}, options = {}) {
            try {
                this.logger.log('开始解析单个订单', 'info', { 
                    hasRawData: !!rawData,
                    channel: channelInfo.channel 
                });

                if (!rawData || typeof rawData !== 'object') {
                    throw new Error('无效的原始数据');
                }

                // 标准化订单数据
                const standardizedOrder = this.standardizeOrderData(rawData, channelInfo);
                
                // 验证订单数据
                const validation = this.validateOrderData(standardizedOrder);
                if (!validation.valid) {
                    this.logger.log('订单数据验证失败', 'warning', validation);
                }

                // 应用渠道特定的处理规则
                const processedOrder = await this.applyChannelRules(standardizedOrder, channelInfo);

                this.logger.log('单个订单解析完成', 'success', { 
                    customerName: processedOrder.customer_name,
                    channel: channelInfo.channel 
                });

                return {
                    ...processedOrder,
                    validation,
                    parsedAt: new Date().toISOString()
                };

            } catch (error) {
                this.logger.log('单个订单解析失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 解析多个订单
         * @param {array} orderSegments - 订单片段数组
         * @param {object} channelInfo - 渠道信息
         * @param {object} options - 解析选项
         * @returns {Promise<array>} 解析后的订单数组
         */
        async parseMultipleOrders(orderSegments, channelInfo = {}, options = {}) {
            try {
                this.logger.log('开始解析多个订单', 'info', { 
                    segmentCount: orderSegments.length,
                    channel: channelInfo.channel 
                });

                if (!Array.isArray(orderSegments) || orderSegments.length === 0) {
                    return [];
                }

                const parsedOrders = [];
                
                for (let i = 0; i < orderSegments.length; i++) {
                    try {
                        const segment = orderSegments[i];
                        const parsedOrder = await this.parseSingleOrder(segment, channelInfo, {
                            ...options,
                            orderIndex: i
                        });
                        
                        if (parsedOrder && this.isValidOrder(parsedOrder)) {
                            parsedOrders.push(parsedOrder);
                        } else {
                            this.logger.log(`订单片段 ${i + 1} 解析失败或无效`, 'warning');
                        }
                    } catch (error) {
                        this.logger.log(`订单片段 ${i + 1} 解析出错`, 'error', { error: error.message });
                    }
                }

                this.logger.log('多个订单解析完成', 'success', { 
                    totalSegments: orderSegments.length,
                    validOrders: parsedOrders.length 
                });

                return parsedOrders;

            } catch (error) {
                this.logger.log('多个订单解析失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 标准化订单数据
         * @param {object} rawData - 原始数据
         * @param {object} channelInfo - 渠道信息
         * @returns {object} 标准化后的订单数据
         */
        standardizeOrderData(rawData, channelInfo = {}) {
            const standardized = {
                // 客户信息
                customer_name: this.cleanString(rawData.customer_name || rawData.customerName || rawData.name),
                customer_contact: this.cleanString(rawData.customer_contact || rawData.contact || rawData.phone),
                
                // 位置信息
                pickup_location: this.cleanString(rawData.pickup_location || rawData.pickupLocation || rawData.from),
                dropoff_location: this.cleanString(rawData.dropoff_location || rawData.dropoffLocation || rawData.to),
                
                // 时间信息
                pickup_time: this.cleanString(rawData.pickup_time || rawData.pickupTime || rawData.time),
                
                // 订单信息
                ota: channelInfo.channel || rawData.ota || null,
                ota_reference_number: this.cleanString(rawData.ota_reference_number || rawData.referenceNumber || rawData.orderNumber),
                ota_price: this.parsePrice(rawData.ota_price || rawData.price || rawData.amount),
                
                // 车辆信息
                car_type_id: this.parseCarTypeId(rawData.car_type_id || rawData.carType || rawData.vehicleType),
                passenger_count: this.parseNumber(rawData.passenger_count || rawData.passengers, 1),
                luggage_count: this.parseNumber(rawData.luggage_count || rawData.luggage, 0),
                
                // 语言和服务
                languages_id_array: rawData.languages_id_array || this.parseConfig.defaultValues.languages_id_array,
                sub_category_id: rawData.sub_category_id || this.parseConfig.defaultValues.sub_category_id,
                
                // 元数据
                confidence: rawData.confidence || 0.8,
                source: 'order-parser',
                rawData: rawData // 保留原始数据用于调试
            };

            return standardized;
        }

        /**
         * 验证订单数据
         * @param {object} order - 订单数据
         * @returns {object} 验证结果
         */
        validateOrderData(order) {
            const validation = {
                valid: true,
                errors: [],
                warnings: []
            };

            try {
                // 检查必需字段
                for (const field of this.parseConfig.requiredFields) {
                    if (!order[field] || order[field] === null) {
                        validation.errors.push(`缺少必需字段: ${field}`);
                        validation.valid = false;
                    }
                }

                // 检查字段长度
                if (order.customer_name) {
                    const nameLength = order.customer_name.length;
                    if (nameLength < this.parseConfig.validation.minNameLength || 
                        nameLength > this.parseConfig.validation.maxNameLength) {
                        validation.warnings.push(`客户姓名长度异常: ${nameLength}`);
                    }
                }

                // 检查数据类型
                if (order.passenger_count && (typeof order.passenger_count !== 'number' || order.passenger_count < 1)) {
                    validation.warnings.push('乘客数量格式不正确');
                }

                if (order.ota_price && typeof order.ota_price !== 'number') {
                    validation.warnings.push('价格格式不正确');
                }

                return validation;

            } catch (error) {
                validation.valid = false;
                validation.errors.push(`验证过程出错: ${error.message}`);
                return validation;
            }
        }

        /**
         * 应用渠道特定的处理规则
         * @param {object} order - 标准化订单数据
         * @param {object} channelInfo - 渠道信息
         * @returns {Promise<object>} 处理后的订单数据
         */
        async applyChannelRules(order, channelInfo = {}) {
            try {
                if (!channelInfo.channel) {
                    return order; // 无渠道信息，返回原数据
                }

                const processedOrder = { ...order };

                // 应用渠道特定的价格计算和验证
                if (channelInfo.channel === 'fliggy' && processedOrder.ota_price) {
                    // 获取飞猪策略进行价格验证
                    const FliggyStrategy = window.OTA?.strategies?.FliggyOTAStrategy;
                    if (FliggyStrategy) {
                        // 确定地区（基于地点信息）
                        const region = this.determineRegionFromOrder(processedOrder);

                        // 验证Gemini计算的价格是否正确
                        const isValidPrice = FliggyStrategy.validatePriceCalculation(
                            this.extractOriginalPrice(processedOrder),
                            processedOrder.ota_price,
                            region
                        );

                        if (!isValidPrice) {
                            // 如果Gemini计算错误，使用后端重新计算
                            const originalPrice = this.extractOriginalPrice(processedOrder);
                            const correctedPrice = FliggyStrategy.calculatePrice(originalPrice, region);

                            this.logger.log('价格计算错误，已自动纠正', 'warning', {
                                original: processedOrder.ota_price,
                                corrected: correctedPrice,
                                region: region,
                                basePrice: originalPrice
                            });

                            processedOrder.ota_price = correctedPrice;
                            processedOrder._price_corrected = true;
                            processedOrder._original_gemini_price = processedOrder.ota_price;
                        }
                    }
                } else if (channelInfo.channel === 'jingge' && processedOrder.ota_price) {
                    processedOrder.calculated_price = processedOrder.ota_price * 0.615;
                }

                // 应用渠道特定的车型映射
                if (processedOrder.car_type_id) {
                    processedOrder.car_type_id = this.mapCarTypeForChannel(
                        processedOrder.car_type_id, 
                        channelInfo.channel
                    );
                }

                return processedOrder;

            } catch (error) {
                this.logger.log('应用渠道规则失败', 'error', { error: error.message });
                return order; // 返回原数据作为降级方案
            }
        }

        /**
         * 检查是否为有效订单
         * @param {object} order - 订单数据
         * @returns {boolean} 是否有效
         */
        isValidOrder(order) {
            if (!order || typeof order !== 'object') {
                return false;
            }

            // 至少要有客户姓名
            if (!order.customer_name || order.customer_name.trim().length < 2) {
                return false;
            }

            // 至少要有接送地点之一
            if (!order.pickup_location && !order.dropoff_location) {
                return false;
            }

            return true;
        }

        // ========================================
        // 工具方法
        // ========================================

        /**
         * 清理字符串
         * @param {any} value - 输入值
         * @returns {string|null} 清理后的字符串
         */
        cleanString(value) {
            if (!value || typeof value !== 'string') {
                return null;
            }
            return value.trim() || null;
        }

        /**
         * 解析价格
         * @param {any} value - 价格值
         * @returns {number|null} 解析后的价格
         */
        parsePrice(value) {
            if (typeof value === 'number') {
                return value;
            }
            if (typeof value === 'string') {
                const cleaned = value.replace(/[^\d.]/g, '');
                const parsed = parseFloat(cleaned);
                return isNaN(parsed) ? null : parsed;
            }
            return null;
        }

        /**
         * 解析数字
         * @param {any} value - 输入值
         * @param {number} defaultValue - 默认值
         * @returns {number} 解析后的数字
         */
        parseNumber(value, defaultValue = 0) {
            if (typeof value === 'number') {
                return value;
            }
            if (typeof value === 'string') {
                const parsed = parseInt(value, 10);
                return isNaN(parsed) ? defaultValue : parsed;
            }
            return defaultValue;
        }

        /**
         * 解析车型ID
         * @param {any} value - 车型值
         * @returns {number|null} 车型ID
         */
        parseCarTypeId(value) {
            // 简化的车型映射逻辑
            if (typeof value === 'number') {
                return value;
            }
            if (typeof value === 'string') {
                const lowerValue = value.toLowerCase();
                if (lowerValue.includes('经济') || lowerValue.includes('economy')) {
                    return 1;
                }
                if (lowerValue.includes('舒适') || lowerValue.includes('comfort')) {
                    return 2;
                }
                if (lowerValue.includes('豪华') || lowerValue.includes('luxury')) {
                    return 3;
                }
            }
            return null;
        }

        /**
         * 为渠道映射车型
         * @param {number} carTypeId - 车型ID
         * @param {string} channel - 渠道名称
         * @returns {number} 映射后的车型ID
         */
        mapCarTypeForChannel(carTypeId, channel) {
            // 保持现有的车型映射逻辑
            // JingGe和Fliggy使用相同的映射
            return carTypeId;
        }

        /**
         * 从订单信息确定地区
         * @param {object} order - 订单数据
         * @returns {string} 地区 ('singapore' | 'malaysia')
         */
        determineRegionFromOrder(order) {
            const pickup = (order.pickup || order.pickup_location || '').toLowerCase();
            const destination = (order.destination || order.dropoff_location || '').toLowerCase();
            const combinedText = `${pickup} ${destination}`.toLowerCase();

            // 新加坡地区标识
            const singaporeKeywords = [
                'singapore', 'changi airport', '樟宜机场', '新加坡',
                'universal studios singapore', '圣淘沙'
            ];

            // 检查是否为新加坡
            for (const keyword of singaporeKeywords) {
                if (combinedText.includes(keyword.toLowerCase())) {
                    return 'singapore';
                }
            }

            // 默认为马来西亚
            return 'malaysia';
        }

        /**
         * 从订单数据中提取原始价格
         * @param {object} order - 订单数据
         * @returns {number} 原始价格
         */
        extractOriginalPrice(order) {
            // 尝试从raw_text或其他字段中提取原始价格
            // 这里需要根据实际的订单文本格式来实现
            // 暂时返回当前的ota_price作为基础

            // 如果有原始文本，尝试提取
            if (order.raw_text) {
                const priceMatch = order.raw_text.match(/(?:总价格|商家实收|用户实付|实付)[：:]\s*(\d+(?:\.\d+)?)/);
                if (priceMatch) {
                    return parseFloat(priceMatch[1]);
                }
            }

            // 如果当前价格看起来像是已经计算过的（小于原始价格），尝试反推
            if (order.ota_price && order.ota_price < 100) {
                // 可能是已经计算过的价格，尝试反推原始价格
                // 165 * 0.84 * 0.615 = 85.24，所以 85.24 / (0.84 * 0.615) = 165
                const region = this.determineRegionFromOrder(order);
                const factor = region === 'singapore' ? 0.84 * 0.2 : 0.84 * 0.615;
                return Math.round((order.ota_price / factor) * 100) / 100;
            }

            // 默认返回当前价格
            return order.ota_price || 0;
        }

        /**
         * 获取解析统计信息
         * @returns {object} 统计信息
         */
        getParsingStats() {
            return {
                config: this.parseConfig,
                version: '2.0.0',
                supportedChannels: ['fliggy', 'jingge', 'generic']
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const orderParser = new OrderParser();

    // 导出到全局作用域
    window.OrderParser = OrderParser;
    window.OTA.OrderParser = OrderParser;
    window.OTA.orderParser = orderParser;

    console.log('✅ OrderParser (子层实现) 已加载');

})();
