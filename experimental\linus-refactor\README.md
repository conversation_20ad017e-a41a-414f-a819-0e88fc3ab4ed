## Linus 重构试验区 (Isolated Sandbox)


本目录存放此前引入但未接入主生产加载链的“Lin<PERSON> Torvalds 式重构”实验文件。它们已从仓库根目录/生产路径中移出，防止：

1. 意外被 `index.html` 或未来的自动脚本清单加载
2. 干扰现有 DI / 启动顺序 (window.OTA.*)
3. Service Worker / PWA 误注册导致缓存策略与 CSP 偏差

### 包含文件

| 文件 | 说明 | 当前状态 |
|------|------|----------|
| `ui-simple.js` | 简化 UI 管理器 (window.ota.ui.enhanced) | 未在生产加载 |
| `load-test.js` | 负载/压力测试工具 | 手动使用 |
| `monitoring-dashboard.html` | 独立监控面板入口 | 独立打开 |
| `production-config.js` | 实验性生产配置+SW注册 | 未引用；路径已调整 |
| `sw.js` | 实验 Service Worker | 未注册 |
| `manifest.json` | 实验 PWA manifest | 未引用 |
| `test-*.html` | 重构相关测试页 | 手动调试 |

### 与主应用的隔离保证

- `index.html` 仅加载 `js/core/script-manifest.js` / `script-loader.js`，不扫描本目录
- 未在根级残留同名文件（避免缓存旧路径）
- `production-config.js` 若需要单独验证，请在独立 HTML 中显式 `<script src="experimental/linus-refactor/production-config.js"></script>`
- 该区任何脚本不应写入 `window.OTA` 核心命名空间（除非通过桥接层评审）

### 如何临时启用某个实验

1. 在单独测试页（不要用生产 `index.html`）中引用需要的文件
2. 明确测试完后移除引用，防止提交混入
3. 若要迁回主线：
   - 通过架构评审：命名空间、依赖顺序、CSP、缓存策略
   - 编写回归测试（渠道检测 / 价格策略 / 历史记录 / 多订单）

### 生产接入审批清单 (Checklist)

- [ ] 不破坏 `window.OTA.*` 现有 API 合约
- [ ] 无全局污染（除经过批准的单一入口注册）
- [ ] 不改变渠道检测 & 价格计算逻辑结果
- [ ] 通过最少：启动、渠道、解析、历史、批量 5 项冒烟
- [ ] Service Worker 与现有缓存策略冲突评审完成

### 注意

`production-config.js` 内部的相对 `import('./performance-monitor.js')` 等路径若后续再移动层级，请同步检查。

---
维护者：重构试验组  
更新日期：自动隔离执行
