/**
 * 简化脚本加载器 - <PERSON><PERSON>ds式重构版本
 * 
 * "Never break userspace" - 保持功能完整性
 * "Good taste" - 消除不必要的复杂性
 * 
 * 从162行复杂代码简化为50行直接代码
 */

'use strict';

class SimpleScriptLoader {
    constructor() {
        this.startTime = performance.now();
        this.loadedPhases = 0;
        this.totalPhases = 0;
    }
    
    async loadAll() {
        console.log('🚀 开始加载OTA系统 - 简化版本');
        
        if (!window.OTA?.scriptManifest?.phases) {
            throw new Error('脚本清单未找到');
        }
        
        const phases = window.OTA.scriptManifest.phases;
        this.totalPhases = phases.length;
        
        // 顺序加载各个阶段
        for (const phase of phases) {
            await this.loadPhase(phase);
        }
        
        const totalTime = performance.now() - this.startTime;
        console.log(`✅ 所有脚本加载完成，耗时: ${totalTime.toFixed(1)}ms`);
        
        return { success: true, totalTime };
    }
    
    async loadPhase(phase) {
        const startTime = performance.now();
        console.log(`🔧 加载阶段: ${phase.name} (${phase.scripts.length}个脚本)`);
        
        // 并行加载阶段内的所有脚本
        const promises = phase.scripts.map(script => this.loadScript(script));
        await Promise.all(promises);
        
        this.loadedPhases++;
        const phaseTime = performance.now() - startTime;
        
        console.log(`✅ 阶段完成: ${phase.name}，耗时: ${phaseTime.toFixed(1)}ms`);
    }
    
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // 检查是否已加载
            if (document.querySelector(`script[src="${src}"]`)) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = src;
            script.async = false; // 保持加载顺序
            
            script.onload = () => {
                console.log(`  ✓ ${src}`);
                resolve();
            };
            
            script.onerror = () => {
                console.error(`  ✗ ${src} 加载失败`);
                reject(new Error(`脚本加载失败: ${src}`));
            };
            
            document.head.appendChild(script);
        });
    }
}

// 自动启动加载器
window.OTA = window.OTA || {};
window.OTA.SimpleScriptLoader = SimpleScriptLoader;

// 兼容性：如果在index.html中被调用
if (typeof window !== 'undefined' && document.readyState !== 'loading') {
    // 如果DOM已准备好，立即开始加载
    const loader = new SimpleScriptLoader();
    loader.loadAll().catch(error => {
        console.error('❌ 脚本加载失败:', error);
    });
}

console.log('✅ 简化脚本加载器已准备就绪');