<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单组件诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #007cba;
            background-color: #f8f9fa;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        .status.ok { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .details {
            margin-top: 10px;
            font-family: monospace;
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        .check-item {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        h1 { color: #333; margin-bottom: 30px; }
        h2 { color: #555; margin-bottom: 15px; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a8b; }
        #results { min-height: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 多订单组件诊断工具</h1>
        <p>此工具用于检查多订单模式所需的所有JavaScript组件是否正确加载和初始化。</p>
        
        <div class="section">
            <h2>🚀 诊断操作</h2>
            <button onclick="runDiagnostics()">开始全面诊断</button>
            <button onclick="checkNamespaces()">检查命名空间</button>
            <button onclick="checkServices()">检查服务</button>
            <button onclick="checkComponents()">检查组件</button>
            <button onclick="checkPages()">检查页面</button>
            <button onclick="simulateMultiOrder()">模拟多订单初始化</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- 加载现有的脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>

    <script>
        // 诊断结果显示
        function displayResults(title, results) {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = `
                <h2>${title}</h2>
                ${results.map(result => `
                    <div class="check-item">
                        <span class="status ${result.status}">${result.status.toUpperCase()}</span>
                        <span>${result.name}</span>
                        ${result.details ? `<div class="details">${result.details}</div>` : ''}
                    </div>
                `).join('')}
            `;
            resultsDiv.appendChild(section);
        }

        // 检查命名空间
        function checkNamespaces() {
            const results = [];
            
            // 检查基础命名空间
            const namespaces = [
                'window.OTA',
                'window.OTA.Services', 
                'window.OTA.Components',
                'window.OTA.Pages'
            ];

            namespaces.forEach(ns => {
                try {
                    const obj = eval(ns);
                    results.push({
                        name: ns,
                        status: obj ? 'ok' : 'error',
                        details: obj ? `类型: ${typeof obj}, 属性数: ${Object.keys(obj).length}` : '未定义'
                    });
                } catch (e) {
                    results.push({
                        name: ns,
                        status: 'error',
                        details: `错误: ${e.message}`
                    });
                }
            });

            displayResults('🏗️ 命名空间检查', results);
        }

        // 检查服务
        function checkServices() {
            const results = [];
            const services = [
                'StateManager',
                'OrderDetector', 
                'OrderProcessor',
                'BatchManager',
                'ApiClient',
                'stateManager',
                'orderDetector',
                'orderProcessor', 
                'batchManager',
                'apiClient'
            ];

            services.forEach(service => {
                try {
                    const obj = window.OTA?.Services?.[service];
                    results.push({
                        name: `OTA.Services.${service}`,
                        status: obj ? 'ok' : 'error',
                        details: obj ? `类型: ${typeof obj}${typeof obj === 'function' ? ' (类)' : ' (实例)'}` : '未找到'
                    });
                } catch (e) {
                    results.push({
                        name: `OTA.Services.${service}`,
                        status: 'error',
                        details: `错误: ${e.message}`
                    });
                }
            });

            displayResults('⚙️ 服务层检查', results);
        }

        // 检查组件
        function checkComponents() {
            const results = [];
            const components = [
                'BaseComponent',
                'ComponentRegistry',
                'OrderCard',
                'BatchControls',
                'ProgressIndicator', 
                'StatusPanel',
                'registry'
            ];

            components.forEach(component => {
                try {
                    const obj = window.OTA?.Components?.[component];
                    results.push({
                        name: `OTA.Components.${component}`,
                        status: obj ? 'ok' : 'error',
                        details: obj ? `类型: ${typeof obj}${typeof obj === 'function' ? ' (类)' : ' (实例)'}` : '未找到'
                    });
                } catch (e) {
                    results.push({
                        name: `OTA.Components.${component}`,
                        status: 'error',
                        details: `错误: ${e.message}`
                    });
                }
            });

            displayResults('🧩 组件层检查', results);
        }

        // 检查页面
        function checkPages() {
            const results = [];
            const pages = [
                'MultiOrderPageV2',
                'multiOrderPageV2',
                'multiOrderPage' // 向后兼容
            ];

            pages.forEach(page => {
                try {
                    const obj = window.OTA?.Pages?.[page];
                    results.push({
                        name: `OTA.Pages.${page}`,
                        status: obj ? 'ok' : 'error',
                        details: obj ? `类型: ${typeof obj}, 已初始化: ${obj.isInitialized || false}` : '未找到'
                    });
                } catch (e) {
                    results.push({
                        name: `OTA.Pages.${page}`,
                        status: 'error',
                        details: `错误: ${e.message}`
                    });
                }
            });

            displayResults('📄 页面层检查', results);
        }

        // 模拟多订单初始化
        function simulateMultiOrder() {
            const results = [];
            
            try {
                // 尝试创建多订单页面实例
                if (window.OTA?.Pages?.MultiOrderPageV2) {
                    const pageClass = window.OTA.Pages.MultiOrderPageV2;
                    const page = new pageClass();
                    
                    results.push({
                        name: '创建多订单页面实例',
                        status: 'ok',
                        details: `实例已创建，页面ID: ${page.pageId || '未知'}`
                    });

                    // 测试初始化
                    page.initialize().then(() => {
                        results.push({
                            name: '多订单页面初始化',
                            status: 'ok',
                            details: `初始化成功，状态: ${page.isInitialized}`
                        });
                        displayResults('🎮 模拟初始化', results);
                    }).catch(error => {
                        results.push({
                            name: '多订单页面初始化',
                            status: 'error',
                            details: `初始化失败: ${error.message}`
                        });
                        displayResults('🎮 模拟初始化', results);
                    });
                } else {
                    results.push({
                        name: '多订单页面类检查',
                        status: 'error',
                        details: 'MultiOrderPageV2 类未找到'
                    });
                    displayResults('🎮 模拟初始化', results);
                }
            } catch (error) {
                results.push({
                    name: '模拟初始化过程',
                    status: 'error',
                    details: `异常: ${error.message}`
                });
                displayResults('🎮 模拟初始化', results);
            }
        }

        // 运行全面诊断
        function runDiagnostics() {
            // 清空之前的结果
            document.getElementById('results').innerHTML = '';
            
            // 依次运行所有检查
            setTimeout(() => checkNamespaces(), 100);
            setTimeout(() => checkServices(), 200);
            setTimeout(() => checkComponents(), 300);
            setTimeout(() => checkPages(), 400);
            setTimeout(() => simulateMultiOrder(), 500);
        }

        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔍 多订单组件诊断工具已准备就绪');
                checkNamespaces();
            }, 1000);
        });
    </script>
</body>
</html>