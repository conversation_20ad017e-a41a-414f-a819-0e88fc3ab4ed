# OTA订单处理系统 - 文档中心

## 文档结构概览

### 📋 核心文档
- [项目架构](../project-architecture.md) - 系统整体架构说明
- [数据流程](../data-flow.md) - 数据处理流程图
- [部署指南](../deployment/deployment-guide.md) - 系统部署说明

### 🔧 开发文档
- [开发日志](../memory-bank/development-log.md) - 开发过程记录
- [项目概览](../memory-bank/project-overview.md) - 项目总体概况
- [重构计划](implementation-reports/) - 各阶段重构报告

### 📚 API文档
- [API文档](../memory-bank/legacy-docs/api-documentation.md) - API接口说明
- [字段要求](../memory-bank/legacy-docs/gomyhire-api-field-requirements.md) - API字段规范
- [API参考](../memory-bank/legacy-docs/api-reference/) - 详细API参考

### 📊 分析报告
- [架构分析](analysis-reports/) - 系统架构分析报告
- [性能优化](../memory-bank/legacy-docs/performance-optimization-guide.md) - 性能优化指南
- [代码分析](../reports/) - 代码质量分析

### 🛠 技术修复报告
- [CORS解决方案](technical-fixes/CORS-SOLUTION-COMPLETE.md)
- [字段标准化](technical-fixes/FIELD-STANDARDIZATION-FIX-REPORT.md)
- [动画系统](technical-fixes/ANIMATION-SYSTEM-IMPLEMENTATION-REPORT.md)
- [更多修复报告...](technical-fixes/)

### 📖 用户指南
- [用户指南](../memory-bank/legacy-docs/user-guide.md) - 系统使用说明
- [多订单模式](../memory-bank/legacy-docs/多订单模式使用指南.md) - 多订单功能说明

### 🗂 归档文档
- [过期报告](../archive/outdated-reports/) - 历史修复报告
- [旧版文档](../memory-bank/legacy-docs/) - 历史版本文档

## 文档分类说明

### 📁 目录结构
```
docs/
├── README.md                    # 本文件 - 文档导航
├── implementation-reports/      # 实施报告
├── technical-fixes/            # 技术修复报告  
├── analysis-reports/           # 分析报告
├── user-guides/               # 用户指南
└── api-reference/             # API参考文档

memory-bank/                   # 历史文档库
├── legacy-docs/              # 遗留文档
├── development-log.md        # 开发日志
└── project-overview.md       # 项目概览

reports/                      # 分析报告
├── architecture_solution_summary.md
├── changelog.md
└── 其他分析报告...

archive/                      # 归档区域
├── outdated-reports/         # 过期报告
└── temp/                     # 临时文件
```

## 文档维护指南

### 新文档创建规范
1. **技术修复报告** → `docs/technical-fixes/`
2. **分析报告** → `docs/analysis-reports/`
3. **用户指南** → `docs/user-guides/`
4. **实施报告** → `docs/implementation-reports/`
5. **API文档** → `docs/api-reference/`

### 文档命名规范
- 使用大写字母和连字符: `FEATURE-NAME-REPORT.md`
- 包含日期（如需要）: `FEATURE-FIX-2025-08-15.md`
- 语言标识: 中文内容保持中文名称，英文内容使用英文名称

### 文档状态管理
- ✅ **当前有效** - 在对应分类目录中
- 📁 **归档** - 移至 `archive/` 目录
- 🔄 **更新中** - 标注更新状态
- ❌ **已废弃** - 移至 `archive/outdated-reports/`

最后更新: 2025-08-15