/**
 * 多订单页面组件样式
 * 文件: js/pages/multi-order/styles/components.css
 * 角色: 多订单页面UI组件的样式定义
 * 
 * @MULTI_ORDER_COMPONENTS_STYLES 多订单组件样式
 * 🏷️ 标签: @OTA_MULTI_ORDER_COMPONENTS_STYLES
 * 📝 说明: 订单卡片、批量控制等组件的样式
 * <AUTHOR>
 * @version 2.0.0
 */

/* ================================
   订单卡片组件样式
   ================================ */

.order-card {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.order-card:hover {
    border-color: var(--color-primary-light);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.order-card.selected {
    border-color: var(--color-primary);
    background: var(--color-primary-light);
    box-shadow: var(--shadow-lg);
}

.order-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--color-primary);
}

.order-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.order-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--color-primary);
    cursor: pointer;
}

.order-title {
    display: flex;
    flex-direction: column;
}

.order-number {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-parsed {
    background: var(--color-success-light);
    color: var(--color-success);
}

.status-processing {
    background: var(--color-warning-light);
    color: var(--color-warning);
    animation: pulse 1.5s infinite;
}

.status-completed {
    background: var(--color-info-light);
    color: var(--color-info);
}

.order-card-body {
    margin-top: var(--spacing-sm);
}

.order-summary {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.summary-label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 60px;
    font-size: 0.9rem;
}

.summary-value {
    color: var(--text-primary);
    font-weight: 400;
    text-align: right;
    flex: 1;
    font-size: 0.9rem;
}

/* ================================
   批量控制组件样式
   ================================ */

.batch-controls {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
    justify-content: space-between;
}

.batch-controls-left {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    flex-wrap: wrap;
}

.batch-controls-right {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.selected-count {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.9rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--color-primary-light);
    border-radius: 6px;
}

.batch-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.batch-btn:hover:not(:disabled) {
    background: var(--bg-tertiary);
    border-color: var(--color-primary-light);
}

.batch-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.batch-btn.primary {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

.batch-btn.primary:hover:not(:disabled) {
    background: var(--color-primary-dark);
}

/* ================================
   结果摘要组件样式
   ================================ */

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.summary-stat {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-2px);
}

.summary-stat.success {
    border-left: 4px solid var(--color-success);
}

.summary-stat.failed {
    border-left: 4px solid var(--color-error);
}

.summary-stat.rate {
    border-left: 4px solid var(--color-info);
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-message {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 500;
}

/* ================================
   空状态样式
   ================================ */

.empty-message {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-style: italic;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px dashed var(--border-color);
}

/* ================================
   响应式设计
   ================================ */

@media (max-width: 768px) {
    .order-card {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .order-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .summary-label {
        min-width: auto;
    }
    
    .summary-value {
        text-align: left;
    }
    
    .batch-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .batch-controls-left,
    .batch-controls-right {
        justify-content: center;
        width: 100%;
    }
    
    .batch-btn {
        flex: 1;
        text-align: center;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .batch-controls-left,
    .batch-controls-right {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}
