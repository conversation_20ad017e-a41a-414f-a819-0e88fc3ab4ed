/**
 * OTA策略统一配置 - 合并版本
 * 
 * 设计目标：
 * - 将所有OTA策略合并到单一文件中
 * - 保持原有的静态方法调用接口
 * - 简化策略文件管理
 * 
 * <AUTHOR>
 * @version 1.0.0 (Unified Strategies)
 */

(function() {
    'use strict';

    /**
     * Fliggy渠道策略
     * 保持原有接口完全不变
     */
    class FliggyOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'fliggy';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
                // 价格与车型ID映射片段
                ota_price: '【🚨 飞猪价格计算 - 强制执行规则 🚨】\n\n**第一步：价格提取（按优先级）**\n- 优先级1：商家实收\n- 优先级2：总价格  \n- 优先级3：用户实付\n- 优先级4：其他价格字段\n\n**第二步：地区识别（关键步骤）**\n马来西亚地区标识（必须包含以下任一关键词）：\n- 机场：KLIA, KLIA2, Kuala Lumpur International Airport\n- 城市：Kuala Lumpur, KL, 吉隆坡, 马来西亚\n- 地点：Subang, Penang, Johor, Sabah, Malacca\n\n新加坡地区标识：\n- Universal Studios Singapore, Changi Airport, Singapore, 樟宜机场, 新加坡, 圣淘沙\n\n**第三步：强制计算公式（必须完整执行）**\n⚠️ 重要：必须使用完整的两个系数相乘！\n\n马来西亚地区：最终价格 = 原始价格 × 0.84 × 0.615\n- 第一个系数：0.84（飞猪渠道费率）\n- 第二个系数：0.615（马来西亚地区费率）\n- 完整公式：原始价格 × 0.84 × 0.615\n\n新加坡地区：最终价格 = 原始价格 × 0.84 × 0.2\n- 第一个系数：0.84（飞猪渠道费率）\n- 第二个系数：0.2（新加坡地区费率）\n- 完整公式：原始价格 × 0.84 × 0.2\n\n**第四步：计算验证（必须自检）**\n马来西亚示例验证：\n- 165元 → 165 × 0.84 = 138.6 → 138.6 × 0.615 = 85.239 → 85.24 MYR ✓\n- 230元 → 230 × 0.84 = 193.2 → 193.2 × 0.615 = 118.818 → 118.82 MYR ✓\n\n新加坡示例验证：\n- 230元 → 230 × 0.84 = 193.2 → 193.2 × 0.2 = 38.64 MYR ✓\n\n**第五步：计算过程展示（必须包含）**\n在JSON输出中必须包含以下字段来展示计算过程：\n```json\n{\n  "ota_price": 85.24,\n  "price_calculation": {\n    "original_price": 165,\n    "region": "malaysia", \n    "channel_rate": 0.84,\n    "region_rate": 0.615,\n    "step1": "165 × 0.84 = 138.6",\n    "step2": "138.6 × 0.615 = 85.239", \n    "final_price": 85.24,\n    "currency": "MYR"\n  }\n}\n```\n\n**第六步：输出要求**\n- 货币单位：MYR（马来西亚令吉）\n- 小数位数：保留2位小数\n- 必须输出计算后的最终价格，不是原始价格\n- 必须包含 price_calculation 对象展示完整计算过程\n\n**🔴 错误检测：如果你的计算结果是165×0.615=101.64，这是错误的！**\n**✅ 正确计算：165×0.84×0.615=85.24**\n\n**🚨 强制要求：每次价格计算都必须在JSON中包含price_calculation字段！**\n\n**🔴 重要：ota_price字段必须与price_calculation.final_price保持一致！**\n- ota_price = price_calculation.final_price\n- 两个字段必须是相同的数值\n- 如果计算结果是63.00，那么ota_price也必须是63.00，不能是其他值',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                // 新增：日期和地区字段强化
                pickup_date: 'Fliggy日期提取：**必须从订单文本中提取具体日期**，如"2025-08-11"等完整格式，转换为YYYY-MM-DD格式输出。',
                driving_region_id: '【🎯 地区识别强化规则 - 价格计算关键】：\n\n**马来西亚地区识别（优先级高）**：\n- 吉隆坡：KLIA, KLIA2, Kuala Lumpur International Airport, KL, 吉隆坡国际机场, 吉隆坡\n- 雪兰莪：Subang, Shah Alam, Petaling Jaya\n- 槟城：Penang, 槟城\n- 柔佛：Johor, JB, 新山\n- 沙巴：Sabah, 沙巴, 斗湖机场\n- 马六甲：Malacca, Melaka, 马六甲\n- 通用标识：Malaysia, 马来西亚, Malaysian\n\n**新加坡地区识别**：\n- 机场：Changi Airport, 樟宜机场\n- 景点：Universal Studios Singapore, 圣淘沙\n- 通用：Singapore, 新加坡\n\n**地区ID映射**：\n- 新加坡 → driving_region_id=5\n- 吉隆坡/雪兰莪 → driving_region_id=1  \n- 槟城 → driving_region_id=2\n- 柔佛 → driving_region_id=3\n- 沙巴 → driving_region_id=4\n- 马六甲 → driving_region_id=12\n\n⚠️ 重要：地区识别直接影响价格计算，必须准确识别！',
                // 字段完整性确保
                pickup_location: '上车地点：必须从订单中提取完整地点名称，保持原始描述准确性。',
                dropoff_location: '下车地点：必须从订单中提取完整地点名称，保持原始描述准确性。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 飞猪价格计算规则 - 与提示词保持一致
         * @param {number} basePrice - 原始价格
         * @param {string} region - 地区 ('singapore' | 'malaysia')
         * @returns {number} 计算后的价格
         */
        static calculatePrice(basePrice, region = 'malaysia') {
            // 飞猪渠道费率：0.84
            const channelRate = 0.84;

            // 地区费率
            const regionRate = region === 'singapore' ? 0.2 : 0.615;

            // 完整计算：原始价格 × 渠道费率 × 地区费率
            const finalPrice = basePrice * channelRate * regionRate;

            // 保留两位小数
            return Math.round(finalPrice * 100) / 100;
        }

        /**
         * 验证价格计算结果
         * @param {number} originalPrice - 原始价格
         * @param {number} calculatedPrice - 计算后价格
         * @param {string} region - 地区
         * @returns {boolean} 是否计算正确
         */
        static validatePriceCalculation(originalPrice, calculatedPrice, region = 'malaysia') {
            const expectedPrice = this.calculatePrice(originalPrice, region);
            const tolerance = 0.01; // 允许0.01的误差
            return Math.abs(calculatedPrice - expectedPrice) <= tolerance;
        }
    }

    /**
     * JingGe渠道策略
     * 保持原有接口完全不变
     */
    class JingGeOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'jingge';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是JingGe商铺渠道的订单，输出JSON时ota字段请设置为"Jing Ge"。',
                // 价格、车型ID、联系方式、订单号
                ota_price: '价格识别与换算：若为JingGe商铺订单，最终价格=基础价×0.615；保留两位小数，明确输出最终价；无法确定时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                customer_contact: '若订单未提供手机号，可临时使用订单号(ota_reference_number)作为联系标识填充customer_contact字段；若存在手机号，请保持原值，不要覆盖。',
                ota_reference_number: '订单号识别：请从文本中抽取明确的订单编号；一般为纯数字组合，若无可靠线索，请返回null，不要凭空生成。',
                pickup_date: 'JingGe日期解析规则：输入格式如10/8、01/07、8.13等，默认为2025年；以当前日期为判断基准，选择月份前后最近的日期。例如：当前8月15日，输入10/8解析为2025-10-08，输入01/07解析为2025-01-07（下一年较近），输入8.13解析为2025-08-13。输出YYYY-MM-DD格式。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 价格计算规则
         */
        static calculatePrice(basePrice) {
            return Math.round(basePrice * 0.615 * 100) / 100;
        }

        /**
         * JingGe日期解析规则
         * 输入格式: 10/8, 01/07, 8.13 等
         * 默认年份: 2025
         * 逻辑: 以当前日期为基准，选择前后最近的日期
         */
        static parseJingGeDate(dateStr, currentDate = new Date()) {
            if (!dateStr) return null;
            
            try {
                // 清理输入
                const cleaned = dateStr.trim();
                let month, day;
                
                // 匹配各种分隔符格式: 10/8, 01/07, 8.13, 12-25
                const patterns = [
                    /^(\d{1,2})[\/\.\-](\d{1,2})$/,  // MM/DD, MM.DD, MM-DD
                    /^(\d{1,2})月(\d{1,2})日?$/,      // 中文格式
                ];
                
                let matched = false;
                for (const pattern of patterns) {
                    const match = cleaned.match(pattern);
                    if (match) {
                        month = parseInt(match[1]);
                        day = parseInt(match[2]);
                        matched = true;
                        break;
                    }
                }
                
                if (!matched || month < 1 || month > 12 || day < 1 || day > 31) {
                    return null;
                }
                
                // 当前日期信息
                const currentYear = currentDate.getFullYear();
                const currentMonth = currentDate.getMonth() + 1;
                const currentDay = currentDate.getDate();
                
                // 默认使用2025年，但根据当前日期调整
                let targetYear = 2025;
                
                // 如果当前已经是2025年之后，使用当前年份
                if (currentYear >= 2025) {
                    targetYear = currentYear;
                    
                    // 距离判断逻辑：选择前后最近的月份
                    const currentDate_thisYear = new Date(currentYear, month - 1, day);
                    const currentDate_nextYear = new Date(currentYear + 1, month - 1, day);
                    
                    const diffThisYear = Math.abs(currentDate_thisYear.getTime() - currentDate.getTime());
                    const diffNextYear = Math.abs(currentDate_nextYear.getTime() - currentDate.getTime());
                    
                    // 如果下一年更近，使用下一年
                    if (diffNextYear < diffThisYear) {
                        targetYear = currentYear + 1;
                    }
                } else {
                    // 当前年份小于2025，使用2025年作为基准进行距离判断
                    const date2025 = new Date(2025, month - 1, day);
                    const date2026 = new Date(2026, month - 1, day);
                    
                    const diff2025 = Math.abs(date2025.getTime() - currentDate.getTime());
                    const diff2026 = Math.abs(date2026.getTime() - currentDate.getTime());
                    
                    targetYear = diff2026 < diff2025 ? 2026 : 2025;
                }
                
                // 构造最终日期并验证
                const resultDate = new Date(targetYear, month - 1, day);
                if (isNaN(resultDate.getTime())) {
                    return null;
                }
                
                // 返回 YYYY-MM-DD 格式
                return `${targetYear}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                
            } catch (error) {
                console.error('JingGe日期解析失败:', error);
                return null;
            }
        }
    }

    /**
     * 统一策略配置对象
     * 提供统一的策略访问接口
     */
    const UnifiedOTAStrategies = {
        fliggy: FliggyOTAStrategy,
        jingge: JingGeOTAStrategy,
        
        /**
         * 获取指定策略
         */
        getStrategy(channel) {
            return this[channel] || null;
        },

        /**
         * 获取所有可用策略
         */
        getAllStrategies() {
            return {
                fliggy: FliggyOTAStrategy,
                jingge: JingGeOTAStrategy
            };
        },

        /**
         * 检查策略是否存在
         */
        hasStrategy(channel) {
            return channel in this && typeof this[channel] === 'function';
        }
    };

    // 暴露到全局作用域（保持向后兼容）
    window.FliggyOTAStrategy = FliggyOTAStrategy;
    window.JingGeOTAStrategy = JingGeOTAStrategy;
    
    // 新的统一接口
    window.OTA = window.OTA || {};
    window.OTA.strategies = UnifiedOTAStrategies;

    // 添加价格计算验证工具
    window.OTA.priceValidator = {
        /**
         * 验证飞猪价格计算
         * @param {number} originalPrice - 原始价格
         * @param {number} calculatedPrice - 计算后价格
         * @param {string} region - 地区
         * @returns {object} 验证结果
         */
        validateFliggyPrice(originalPrice, calculatedPrice, region = 'malaysia') {
            const expectedPrice = FliggyOTAStrategy.calculatePrice(originalPrice, region);
            const isValid = FliggyOTAStrategy.validatePriceCalculation(originalPrice, calculatedPrice, region);

            return {
                isValid,
                originalPrice,
                calculatedPrice,
                expectedPrice,
                region,
                difference: Math.abs(calculatedPrice - expectedPrice),
                correctionNeeded: !isValid
            };
        }
    };

    console.log('✅ 统一OTA策略配置已加载');

})();