/**
 * 无感知更新配置文件
 * @CONFIG_FILE @SILENT_UPDATE
 * 
 * 提供无感知更新的全局配置选项
 * 用户可以通过修改此文件来自定义更新行为
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 无感知更新配置
     * @SHARED_CONSTANT
     */
    const SILENT_UPDATE_CONFIG = {
        // 基础设置
        enabled: true,                    // 启用无感知更新
        mode: 'smart',                   // 更新模式: 'immediate', 'smart', 'manual'
        
        // 时机控制
        updateOnHidden: true,            // 页面隐藏时更新
        updateOnIdle: true,              // 用户空闲时更新
        idleTimeout: 300000,             // 空闲阈值（5分钟）
        maxWaitTime: 1800000,            // 最大等待时间（30分钟）
        
        // 数据保护
        preserveFormData: true,          // 保护表单数据
        autoSaveBeforeUpdate: true,      // 更新前自动保存
        formDataStorageKey: 'ota_auto_save_before_update',
        
        // 用户体验
        showMinimalNotification: true,   // 显示最小化通知
        notificationDuration: 2000,      // 通知显示时间（毫秒）
        notificationPosition: 'top-right', // 通知位置: 'top-right', 'top-left', 'bottom-right', 'bottom-left'
        
        // 高级选项
        preloadNewAssets: true,          // 预加载新资源
        gracefulFallback: true,          // 优雅降级
        updateRetryCount: 3,             // 更新重试次数
        retryDelay: 5000,                // 重试延迟（毫秒）
        
        // 调试选项
        debugMode: false,                // 调试模式
        verboseLogging: false,           // 详细日志
        
        // 环境特定配置
        environments: {
            development: {
                enabled: true,
                debugMode: true,
                verboseLogging: true,
                idleTimeout: 60000,      // 开发环境1分钟空闲
                maxWaitTime: 300000      // 开发环境5分钟最大等待
            },
            staging: {
                enabled: true,
                debugMode: true,
                idleTimeout: 180000,     // 测试环境3分钟空闲
                maxWaitTime: 900000      // 测试环境15分钟最大等待
            },
            production: {
                enabled: true,
                debugMode: false,
                verboseLogging: false,
                idleTimeout: 300000,     // 生产环境5分钟空闲
                maxWaitTime: 1800000     // 生产环境30分钟最大等待
            }
        }
    };

    /**
     * 配置管理器
     * @MANAGER
     */
    class SilentUpdateConfigManager {
        constructor() {
            this.config = { ...SILENT_UPDATE_CONFIG };
            this.environment = this.detectEnvironment();
            this.applyEnvironmentConfig();
            this.loadUserConfig();
            
            this.log('无感知更新配置管理器已初始化', 'info');
        }

        /**
         * 检测当前环境
         * @returns {string} 环境名称
         */
        detectEnvironment() {
            const hostname = window.location.hostname;
            
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('local')) {
                return 'development';
            } else if (hostname.includes('staging') || hostname.includes('test')) {
                return 'staging';
            } else {
                return 'production';
            }
        }

        /**
         * 应用环境特定配置
         */
        applyEnvironmentConfig() {
            const envConfig = this.config.environments[this.environment];
            if (envConfig) {
                Object.assign(this.config, envConfig);
                this.log(`已应用 ${this.environment} 环境配置`, 'info');
            }
        }

        /**
         * 加载用户自定义配置
         */
        loadUserConfig() {
            try {
                const userConfig = localStorage.getItem('silent_update_config');
                if (userConfig) {
                    const parsed = JSON.parse(userConfig);
                    Object.assign(this.config, parsed);
                    this.log('已加载用户自定义配置', 'info');
                }
            } catch (error) {
                this.log('用户配置加载失败: ' + error.message, 'warning');
            }
        }

        /**
         * 保存用户配置
         * @param {Object} userConfig - 用户配置
         */
        saveUserConfig(userConfig) {
            try {
                localStorage.setItem('silent_update_config', JSON.stringify(userConfig));
                Object.assign(this.config, userConfig);
                this.log('用户配置已保存', 'success');
                return true;
            } catch (error) {
                this.log('用户配置保存失败: ' + error.message, 'error');
                return false;
            }
        }

        /**
         * 获取配置值
         * @param {string} key - 配置键
         * @returns {*} 配置值
         */
        get(key) {
            return key.split('.').reduce((obj, k) => obj && obj[k], this.config);
        }

        /**
         * 设置配置值
         * @param {string} key - 配置键
         * @param {*} value - 配置值
         */
        set(key, value) {
            const keys = key.split('.');
            const lastKey = keys.pop();
            const target = keys.reduce((obj, k) => obj[k] = obj[k] || {}, this.config);
            target[lastKey] = value;
            
            this.log(`配置已更新: ${key} = ${value}`, 'info');
        }

        /**
         * 获取完整配置
         * @returns {Object} 完整配置对象
         */
        getAll() {
            return { ...this.config };
        }

        /**
         * 重置为默认配置
         */
        reset() {
            this.config = { ...SILENT_UPDATE_CONFIG };
            this.applyEnvironmentConfig();
            localStorage.removeItem('silent_update_config');
            this.log('配置已重置为默认值', 'info');
        }

        /**
         * 验证配置
         * @returns {Object} 验证结果
         */
        validate() {
            const errors = [];
            const warnings = [];

            // 验证时间配置
            if (this.config.idleTimeout < 60000) {
                warnings.push('空闲阈值过短，建议至少1分钟');
            }
            if (this.config.maxWaitTime < this.config.idleTimeout) {
                errors.push('最大等待时间不能小于空闲阈值');
            }
            if (this.config.notificationDuration < 1000) {
                warnings.push('通知显示时间过短，建议至少1秒');
            }

            // 验证重试配置
            if (this.config.updateRetryCount < 1) {
                errors.push('重试次数至少为1');
            }
            if (this.config.retryDelay < 1000) {
                warnings.push('重试延迟过短，建议至少1秒');
            }

            return {
                valid: errors.length === 0,
                errors,
                warnings
            };
        }

        /**
         * 日志记录
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别
         */
        log(message, level = 'info') {
            if (!this.config.verboseLogging && level === 'info') return;
            
            const prefix = '[SilentUpdateConfig]';
            switch (level) {
                case 'error':
                    console.error(prefix, message);
                    break;
                case 'warning':
                    console.warn(prefix, message);
                    break;
                case 'success':
                    console.log(prefix, '✅', message);
                    break;
                default:
                    console.log(prefix, message);
            }
        }

        /**
         * 获取配置摘要
         * @returns {Object} 配置摘要
         */
        getSummary() {
            return {
                environment: this.environment,
                enabled: this.config.enabled,
                mode: this.config.mode,
                idleTimeout: this.config.idleTimeout / 1000 + 's',
                maxWaitTime: this.config.maxWaitTime / 1000 + 's',
                preserveFormData: this.config.preserveFormData,
                showNotification: this.config.showMinimalNotification,
                debugMode: this.config.debugMode
            };
        }
    }

    // 创建全局配置管理器实例
    window.OTA.silentUpdateConfig = new SilentUpdateConfigManager();

    // 向后兼容：提供全局配置对象
    window.SILENT_UPDATE_CONFIG = window.OTA.silentUpdateConfig.getAll();

    // 导出配置管理器（如果支持模块系统）
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = SilentUpdateConfigManager;
    }

    console.log('🔧 无感知更新配置已加载');

})();
