/**
 * 进度指示器组件
 * 文件: js/pages/multi-order/components/progress-indicator.js
 * 角色: 进度指示器UI组件，显示详细的处理进度和状态
 * 
 * @PROGRESS_INDICATOR 进度指示器组件
 * 🏷️ 标签: @OTA_PROGRESS_INDICATOR_COMPONENT
 * 📝 说明: 模块化的进度指示器，支持实时进度展示和订单状态跟踪
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 进度指示器组件类
     * 继承自BaseComponent
     */
    class ProgressIndicator extends window.OTA.Components.BaseComponent {
        constructor(options = {}) {
            super({
                container: options.container,
                autoRender: false,
                enableEvents: true,
                ...options
            });

            // 组件特定配置
            this.config = {
                showOverallProgress: options.showOverallProgress !== false,
                showStats: options.showStats !== false,
                showOrderList: options.showOrderList !== false,
                enableAnimations: options.enableAnimations !== false,
                updateInterval: options.updateInterval || 100,
                ...options.config
            };

            // 进度状态
            this.progressState = {
                total: 0,
                completed: 0,
                failed: 0,
                processing: 0,
                currentIndex: -1,
                orders: [],
                startTime: null,
                isActive: false
            };

            // 动画相关
            this.animationFrame = null;
            this.lastUpdateTime = 0;
        }

        /**
         * 初始化状态
         */
        initializeState() {
            this.state.data = {
                ...this.progressState
            };
        }

        /**
         * 渲染组件
         */
        render() {
            if (!this.elements.container) {
                throw new Error('进度指示器组件需要容器元素');
            }

            // 生成进度指示器HTML
            const progressHTML = this.generateProgressHTML();
            
            // 创建进度指示器元素
            const progressElement = document.createElement('div');
            progressElement.innerHTML = progressHTML;
            this.elements.root = progressElement.firstElementChild;

            // 添加到容器
            this.elements.container.appendChild(this.elements.root);

            // 初始化进度显示
            this.resetProgress();

            // 标记为已渲染
            this.state.isRendered = true;

            this.logger.log('📊 进度指示器已渲染', 'info');
        }

        /**
         * 生成进度指示器HTML
         * @returns {string} HTML字符串
         */
        generateProgressHTML() {
            return `
                <div class="progress-indicator" data-component-id="${this.componentId}">
                    <div class="progress-header">
                        <h4>📊 处理进度</h4>
                        <div class="progress-summary" id="progressSummary-${this.componentId}">
                            准备处理...
                        </div>
                    </div>
                    
                    ${this.config.showOverallProgress ? `
                        <div class="overall-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar" id="overallProgressBar-${this.componentId}">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                                <span class="progress-text" id="overallProgressText-${this.componentId}">0/0</span>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${this.config.showStats ? `
                        <div class="progress-stats">
                            <div class="stat-item stat-success">
                                <span class="stat-label">成功</span>
                                <span class="stat-value" id="successCount-${this.componentId}">0</span>
                            </div>
                            <div class="stat-item stat-failed">
                                <span class="stat-label">失败</span>
                                <span class="stat-value" id="failedCount-${this.componentId}">0</span>
                            </div>
                            <div class="stat-item stat-remaining">
                                <span class="stat-label">剩余</span>
                                <span class="stat-value" id="remainingCount-${this.componentId}">0</span>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${this.config.showOrderList ? `
                        <div class="order-status-list" id="orderStatusList-${this.componentId}">
                            <!-- 订单状态项将在这里动态生成 -->
                        </div>
                    ` : ''}
                </div>
            `;
        }

        /**
         * 开始进度跟踪
         * @param {Array} orders - 订单列表
         */
        startProgress(orders = []) {
            this.state.data = {
                total: orders.length,
                completed: 0,
                failed: 0,
                processing: 0,
                currentIndex: -1,
                orders: orders.map((order, index) => ({
                    index,
                    order,
                    status: 'waiting',
                    message: '等待处理...',
                    startTime: null,
                    endTime: null
                })),
                startTime: Date.now(),
                isActive: true
            };

            // 初始化订单状态列表
            if (this.config.showOrderList) {
                this.initializeOrderStatusList();
            }

            // 更新进度显示
            this.updateProgressDisplay();

            // 开始动画循环
            if (this.config.enableAnimations) {
                this.startAnimationLoop();
            }

            this.logger.log(`📊 开始进度跟踪: ${orders.length} 个订单`, 'info');
        }

        /**
         * 更新订单状态
         * @param {number} index - 订单索引
         * @param {string} status - 状态 (waiting, processing, success, failed)
         * @param {string} message - 状态消息
         */
        updateOrderStatus(index, status, message = '') {
            if (!this.state.data.isActive || index < 0 || index >= this.state.data.orders.length) {
                return;
            }

            const orderState = this.state.data.orders[index];
            const oldStatus = orderState.status;
            
            // 更新订单状态
            orderState.status = status;
            orderState.message = message;

            // 更新时间戳
            if (status === 'processing' && oldStatus !== 'processing') {
                orderState.startTime = Date.now();
                this.state.data.currentIndex = index;
                this.state.data.processing++;
            } else if (status === 'success' && oldStatus === 'processing') {
                orderState.endTime = Date.now();
                this.state.data.processing--;
                this.state.data.completed++;
            } else if (status === 'failed' && oldStatus === 'processing') {
                orderState.endTime = Date.now();
                this.state.data.processing--;
                this.state.data.failed++;
            }

            // 更新UI
            this.updateOrderStatusItem(index, status, message);
            this.updateProgressDisplay();

            this.logger.log(`📋 订单 ${index + 1} 状态更新: ${status}`, 'info');
        }

        /**
         * 完成进度跟踪
         */
        completeProgress() {
            this.state.data.isActive = false;
            this.state.data.currentIndex = -1;

            // 停止动画循环
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }

            // 最终更新显示
            this.updateProgressDisplay();

            const duration = Date.now() - this.state.data.startTime;
            this.logger.log(`✅ 进度跟踪完成，耗时: ${Math.round(duration / 1000)}秒`, 'success');
        }

        /**
         * 重置进度
         */
        resetProgress() {
            this.state.data = {
                total: 0,
                completed: 0,
                failed: 0,
                processing: 0,
                currentIndex: -1,
                orders: [],
                startTime: null,
                isActive: false
            };

            // 停止动画循环
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }

            // 重置UI
            this.updateProgressDisplay();
            this.clearOrderStatusList();
        }

        /**
         * 初始化订单状态列表
         */
        initializeOrderStatusList() {
            const statusList = this.elements.root?.querySelector(`#orderStatusList-${this.componentId}`);
            if (!statusList) return;

            statusList.innerHTML = '';

            this.state.data.orders.forEach((orderState, index) => {
                this.addOrderStatusItem(index, orderState.status, orderState.message);
            });
        }

        /**
         * 添加订单状态项
         * @param {number} index - 订单索引
         * @param {string} status - 状态
         * @param {string} message - 消息
         */
        addOrderStatusItem(index, status, message) {
            const statusList = this.elements.root?.querySelector(`#orderStatusList-${this.componentId}`);
            if (!statusList) return;

            const orderState = this.state.data.orders[index];
            const order = orderState?.order || {};
            const customerName = order.customer_name || `订单 ${index + 1}`;

            const statusIcons = {
                waiting: '⏳',
                processing: '⚙️',
                success: '✅',
                failed: '❌'
            };

            const statusItem = document.createElement('div');
            statusItem.className = `order-status-item status-${status}`;
            statusItem.setAttribute('data-order-index', index);
            statusItem.innerHTML = `
                <div class="order-status-icon">${statusIcons[status] || '❓'}</div>
                <div class="order-status-content">
                    <div class="order-status-title">${customerName}</div>
                    <div class="order-status-message">${message}</div>
                </div>
            `;

            statusList.appendChild(statusItem);

            // 滚动到最新项
            statusItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        /**
         * 更新订单状态项
         * @param {number} index - 订单索引
         * @param {string} status - 状态
         * @param {string} message - 消息
         */
        updateOrderStatusItem(index, status, message) {
            const statusItem = this.elements.root?.querySelector(`[data-order-index="${index}"]`);
            if (!statusItem) return;

            const statusIcons = {
                waiting: '⏳',
                processing: '⚙️',
                success: '✅',
                failed: '❌'
            };

            // 更新状态类
            statusItem.className = `order-status-item status-${status}`;

            // 更新图标
            const iconElement = statusItem.querySelector('.order-status-icon');
            if (iconElement) {
                iconElement.textContent = statusIcons[status] || '❓';
            }

            // 更新消息
            const messageElement = statusItem.querySelector('.order-status-message');
            if (messageElement) {
                messageElement.textContent = message;
            }

            // 滚动到当前项
            if (status === 'processing') {
                statusItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }

        /**
         * 清空订单状态列表
         */
        clearOrderStatusList() {
            const statusList = this.elements.root?.querySelector(`#orderStatusList-${this.componentId}`);
            if (statusList) {
                statusList.innerHTML = '';
            }
        }

        /**
         * 更新进度显示
         */
        updateProgressDisplay() {
            if (!this.elements.root) return;

            const { total, completed, failed, processing, currentIndex, isActive } = this.state.data;
            const remaining = total - completed - failed;
            const percentage = total > 0 ? Math.round(((completed + failed) / total) * 100) : 0;

            // 更新总体进度条
            if (this.config.showOverallProgress) {
                const progressBar = this.elements.root.querySelector(`#overallProgressBar-${this.componentId} .progress-fill`);
                if (progressBar) {
                    progressBar.style.width = `${percentage}%`;
                }

                const progressText = this.elements.root.querySelector(`#overallProgressText-${this.componentId}`);
                if (progressText) {
                    progressText.textContent = `${completed + failed}/${total}`;
                }
            }

            // 更新统计数据
            if (this.config.showStats) {
                const successElement = this.elements.root.querySelector(`#successCount-${this.componentId}`);
                const failedElement = this.elements.root.querySelector(`#failedCount-${this.componentId}`);
                const remainingElement = this.elements.root.querySelector(`#remainingCount-${this.componentId}`);

                if (successElement) successElement.textContent = completed;
                if (failedElement) failedElement.textContent = failed;
                if (remainingElement) remainingElement.textContent = remaining;
            }

            // 更新进度摘要
            const progressSummary = this.elements.root.querySelector(`#progressSummary-${this.componentId}`);
            if (progressSummary) {
                if (!isActive && total === 0) {
                    progressSummary.textContent = '准备处理...';
                } else if (!isActive && completed + failed === total && total > 0) {
                    progressSummary.textContent = '✅ 处理完成';
                } else if (currentIndex >= 0) {
                    progressSummary.textContent = `⚙️ 正在处理第 ${currentIndex + 1} 个订单...`;
                } else if (isActive) {
                    progressSummary.textContent = '⏳ 准备开始处理...';
                } else {
                    progressSummary.textContent = '等待中...';
                }
            }
        }

        /**
         * 开始动画循环
         */
        startAnimationLoop() {
            const animate = (currentTime) => {
                if (currentTime - this.lastUpdateTime >= this.config.updateInterval) {
                    this.updateProgressDisplay();
                    this.lastUpdateTime = currentTime;
                }

                if (this.state.data.isActive) {
                    this.animationFrame = requestAnimationFrame(animate);
                }
            };

            this.animationFrame = requestAnimationFrame(animate);
        }

        /**
         * 获取进度信息
         * @returns {Object} 进度信息
         */
        getProgressInfo() {
            const { total, completed, failed, processing } = this.state.data;
            return {
                total,
                completed,
                failed,
                processing,
                remaining: total - completed - failed,
                percentage: total > 0 ? Math.round(((completed + failed) / total) * 100) : 0,
                isComplete: completed + failed >= total && total > 0,
                isActive: this.state.data.isActive
            };
        }

        /**
         * 销毁回调
         */
        onDestroy() {
            // 停止动画循环
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }

            // 清理状态
            this.progressState = null;
        }
    }

    // 注册组件到组件注册中心
    if (window.OTA?.Components?.registry) {
        window.OTA.Components.registry.register('ProgressIndicator', ProgressIndicator, {
            singleton: false,
            autoCreate: false
        });
    }

    // 暴露到OTA命名空间
    window.OTA.Components.ProgressIndicator = ProgressIndicator;

    console.log('✅ 进度指示器组件已加载');

})();
