/**
 * 状态管理服务
 * 文件: js/pages/multi-order/services/state-manager.js
 * 角色: 新的独立状态管理服务，页面级别的状态管理
 * 
 * @STATE_MANAGER 状态管理服务
 * 🏷️ 标签: @OTA_STATE_MANAGER_V2
 * 📝 说明: 负责多订单页面的状态管理，不依赖全局AppState
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Services = window.OTA.Services || {};

(function() {
    'use strict';

    /**
     * 状态管理服务类
     * 管理多订单页面的所有状态
     */
    class StateManager {
        constructor(config = {}) {
            this.config = {
                enablePersistence: config.enablePersistence || false,
                persistenceKey: config.persistenceKey || 'multiOrderPageState',
                enableHistory: config.enableHistory !== false,
                maxHistorySize: config.maxHistorySize || 10,
                enableValidation: config.enableValidation !== false,
                ...config
            };

            // 页面状态
            this.state = {
                // 页面基本状态
                page: {
                    isInitialized: false,
                    currentStep: 'preview', // preview, processing, completed
                    isProcessing: false,
                    lastUpdated: null
                },

                // 订单数据
                orders: {
                    list: [],
                    selectedIndices: new Set(),
                    originalText: '',
                    detectionMethod: 'ai',
                    confidence: 0
                },

                // 处理进度
                processing: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    processing: 0,
                    currentIndex: -1,
                    startTime: null,
                    endTime: null
                },

                // 处理结果
                results: {
                    summary: null,
                    successful: [],
                    failed: [],
                    details: []
                },

                // UI状态
                ui: {
                    showProgress: false,
                    showResults: false,
                    enableBatchControls: true,
                    selectedCount: 0
                }
            };

            // 状态历史
            this.stateHistory = [];
            
            // 状态监听器
            this.listeners = new Map();
            
            this.logger = this.getLogger();
            this.logger.log('📊 状态管理服务已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 初始化状态
         * @param {Object} initialData - 初始数据
         */
        initialize(initialData = {}) {
            try {
                // 恢复持久化状态
                if (this.config.enablePersistence) {
                    this.restoreFromPersistence();
                }

                // 设置初始数据
                if (initialData.orders) {
                    this.setOrders(initialData.orders, initialData);
                }

                // 标记为已初始化
                this.updateState('page', {
                    isInitialized: true,
                    lastUpdated: Date.now()
                });

                this.logger.log('✅ 状态管理器初始化完成', 'success');

            } catch (error) {
                this.logger.logError('状态管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 设置订单数据
         * @param {Array} orders - 订单列表
         * @param {Object} metadata - 元数据
         */
        setOrders(orders, metadata = {}) {
            if (!Array.isArray(orders)) {
                throw new Error('订单数据必须是数组');
            }

            const selectedIndices = new Set();
            // 默认全选
            for (let i = 0; i < orders.length; i++) {
                selectedIndices.add(i);
            }

            this.updateState('orders', {
                list: orders,
                selectedIndices,
                originalText: metadata.originalText || '',
                detectionMethod: metadata.detectionMethod || 'ai',
                confidence: metadata.confidence || 0
            });

            this.updateState('ui', {
                selectedCount: selectedIndices.size
            });

            this.logger.log(`📋 已设置 ${orders.length} 个订单`, 'info');
        }

        /**
         * 更新订单选择
         * @param {number} index - 订单索引
         * @param {boolean} selected - 是否选中
         */
        updateOrderSelection(index, selected) {
            const selectedIndices = new Set(this.state.orders.selectedIndices);
            
            if (selected) {
                selectedIndices.add(index);
            } else {
                selectedIndices.delete(index);
            }

            this.updateState('orders', { selectedIndices });
            this.updateState('ui', { selectedCount: selectedIndices.size });

            this.notifyListeners('orderSelectionChanged', {
                index,
                selected,
                selectedCount: selectedIndices.size,
                totalCount: this.state.orders.list.length
            });
        }

        /**
         * 全选/取消全选订单
         * @param {boolean} selectAll - 是否全选
         */
        selectAllOrders(selectAll) {
            const selectedIndices = new Set();
            
            if (selectAll) {
                for (let i = 0; i < this.state.orders.list.length; i++) {
                    selectedIndices.add(i);
                }
            }

            this.updateState('orders', { selectedIndices });
            this.updateState('ui', { selectedCount: selectedIndices.size });

            this.notifyListeners('orderSelectionChanged', {
                selectAll,
                selectedCount: selectedIndices.size,
                totalCount: this.state.orders.list.length
            });
        }

        /**
         * 开始处理
         */
        startProcessing() {
            const selectedOrders = this.getSelectedOrders();
            
            this.updateState('page', {
                currentStep: 'processing',
                isProcessing: true
            });

            this.updateState('processing', {
                total: selectedOrders.length,
                completed: 0,
                failed: 0,
                processing: 0,
                currentIndex: -1,
                startTime: Date.now(),
                endTime: null
            });

            this.updateState('ui', {
                showProgress: true,
                showResults: false,
                enableBatchControls: false
            });

            this.notifyListeners('processingStarted', {
                totalOrders: selectedOrders.length
            });

            this.logger.log(`🚀 开始处理 ${selectedOrders.length} 个订单`, 'info');
        }

        /**
         * 更新处理进度
         * @param {number} currentIndex - 当前处理的订单索引
         * @param {string} status - 状态 (processing, completed, failed)
         */
        updateProcessingProgress(currentIndex, status) {
            const processing = { ...this.state.processing };
            
            if (status === 'processing') {
                processing.currentIndex = currentIndex;
                processing.processing++;
            } else if (status === 'completed') {
                processing.processing--;
                processing.completed++;
            } else if (status === 'failed') {
                processing.processing--;
                processing.failed++;
            }

            this.updateState('processing', processing);

            this.notifyListeners('progressUpdated', {
                ...processing,
                remaining: processing.total - processing.completed - processing.failed,
                completionRate: processing.total > 0 ? Math.round((processing.completed / processing.total) * 100) : 0
            });
        }

        /**
         * 完成处理
         * @param {Array} results - 处理结果
         */
        completeProcessing(results) {
            const successful = results.filter(r => r && r.success);
            const failed = results.filter(r => r && !r.success);

            const summary = {
                total: results.length,
                successful: successful.length,
                failed: failed.length,
                successRate: results.length > 0 ? Math.round((successful.length / results.length) * 100) : 0,
                processingTime: Date.now() - this.state.processing.startTime
            };

            this.updateState('page', {
                currentStep: 'completed',
                isProcessing: false
            });

            this.updateState('processing', {
                endTime: Date.now()
            });

            this.updateState('results', {
                summary,
                successful,
                failed,
                details: results
            });

            this.updateState('ui', {
                showProgress: false,
                showResults: true,
                enableBatchControls: true
            });

            this.notifyListeners('processingCompleted', {
                summary,
                results
            });

            this.logger.log('✅ 处理完成', 'success', summary);
        }

        /**
         * 重置状态
         */
        reset() {
            this.state = {
                page: {
                    isInitialized: false,
                    currentStep: 'preview',
                    isProcessing: false,
                    lastUpdated: null
                },
                orders: {
                    list: [],
                    selectedIndices: new Set(),
                    originalText: '',
                    detectionMethod: 'ai',
                    confidence: 0
                },
                processing: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    processing: 0,
                    currentIndex: -1,
                    startTime: null,
                    endTime: null
                },
                results: {
                    summary: null,
                    successful: [],
                    failed: [],
                    details: []
                },
                ui: {
                    showProgress: false,
                    showResults: false,
                    enableBatchControls: true,
                    selectedCount: 0
                }
            };

            this.notifyListeners('stateReset', {});
            this.logger.log('🔄 状态已重置', 'info');
        }

        /**
         * 更新状态
         * @param {string} section - 状态分区
         * @param {Object} updates - 更新内容
         */
        updateState(section, updates) {
            if (!this.state[section]) {
                throw new Error(`未知的状态分区: ${section}`);
            }

            // 保存历史
            if (this.config.enableHistory) {
                this.saveToHistory();
            }

            // 更新状态
            this.state[section] = {
                ...this.state[section],
                ...updates
            };

            // 更新最后修改时间
            this.state.page.lastUpdated = Date.now();

            // 持久化
            if (this.config.enablePersistence) {
                this.saveToPersistence();
            }

            // 通知监听器
            this.notifyListeners('stateUpdated', {
                section,
                updates,
                newState: this.state[section]
            });
        }

        /**
         * 获取状态
         * @param {string} section - 状态分区（可选）
         * @returns {Object} 状态对象
         */
        getState(section = null) {
            if (section) {
                return this.state[section] ? { ...this.state[section] } : null;
            }
            return JSON.parse(JSON.stringify(this.state));
        }

        /**
         * 获取选中的订单
         * @returns {Array} 选中的订单列表
         */
        getSelectedOrders() {
            return this.state.orders.list.filter((_, index) => 
                this.state.orders.selectedIndices.has(index)
            );
        }

        /**
         * 获取选中的订单索引
         * @returns {Array} 选中的订单索引数组
         */
        getSelectedIndices() {
            return Array.from(this.state.orders.selectedIndices);
        }

        /**
         * 添加状态监听器
         * @param {string} event - 事件名称
         * @param {Function} callback - 回调函数
         * @returns {Function} 取消监听的函数
         */
        addListener(event, callback) {
            if (!this.listeners.has(event)) {
                this.listeners.set(event, new Set());
            }
            
            this.listeners.get(event).add(callback);
            
            // 返回取消监听的函数
            return () => {
                const eventListeners = this.listeners.get(event);
                if (eventListeners) {
                    eventListeners.delete(callback);
                }
            };
        }

        /**
         * 通知监听器
         * @param {string} event - 事件名称
         * @param {Object} data - 事件数据
         */
        notifyListeners(event, data) {
            const eventListeners = this.listeners.get(event);
            if (eventListeners) {
                eventListeners.forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        this.logger.logError(`状态监听器执行失败: ${event}`, error);
                    }
                });
            }
        }

        /**
         * 保存到历史
         */
        saveToHistory() {
            const snapshot = JSON.parse(JSON.stringify(this.state));
            snapshot._timestamp = Date.now();
            
            this.stateHistory.push(snapshot);
            
            // 限制历史大小
            if (this.stateHistory.length > this.config.maxHistorySize) {
                this.stateHistory = this.stateHistory.slice(-this.config.maxHistorySize);
            }
        }

        /**
         * 保存到持久化存储
         */
        saveToPersistence() {
            try {
                const persistData = {
                    state: this.state,
                    timestamp: Date.now()
                };
                sessionStorage.setItem(this.config.persistenceKey, JSON.stringify(persistData));
            } catch (error) {
                this.logger.logError('保存状态到持久化存储失败', error);
            }
        }

        /**
         * 从持久化存储恢复
         */
        restoreFromPersistence() {
            try {
                const persistData = sessionStorage.getItem(this.config.persistenceKey);
                if (persistData) {
                    const { state, timestamp } = JSON.parse(persistData);
                    
                    // 检查数据是否过期（1小时）
                    if (Date.now() - timestamp < 3600000) {
                        this.state = state;
                        this.logger.log('📥 已从持久化存储恢复状态', 'info');
                    }
                }
            } catch (error) {
                this.logger.logError('从持久化存储恢复状态失败', error);
            }
        }

        /**
         * 清理持久化存储
         */
        clearPersistence() {
            try {
                sessionStorage.removeItem(this.config.persistenceKey);
                this.logger.log('🧹 已清理持久化存储', 'info');
            } catch (error) {
                this.logger.logError('清理持久化存储失败', error);
            }
        }

        /**
         * 获取状态统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                isInitialized: this.state.page.isInitialized,
                currentStep: this.state.page.currentStep,
                isProcessing: this.state.page.isProcessing,
                orderCount: this.state.orders.list.length,
                selectedCount: this.state.orders.selectedIndices.size,
                historySize: this.stateHistory.length,
                listenerCount: Array.from(this.listeners.values()).reduce((sum, set) => sum + set.size, 0)
            };
        }

        /**
         * 销毁状态管理器
         */
        destroy() {
            // 清理监听器
            this.listeners.clear();
            
            // 清理历史
            this.stateHistory = [];
            
            // 清理持久化存储
            if (this.config.enablePersistence) {
                this.clearPersistence();
            }
            
            // 重置状态
            this.state = null;
            this.config = null;
            
            this.logger.log('🗑️ 状态管理服务已销毁', 'info');
        }
    }

    // 创建全局状态管理服务实例
    const stateManager = new StateManager();

    // 暴露到OTA命名空间
    window.OTA.Services.StateManager = StateManager;
    window.OTA.Services.stateManager = stateManager;

    // 向后兼容
    window.stateManager = stateManager;

    console.log('✅ 状态管理服务已加载');

})();
