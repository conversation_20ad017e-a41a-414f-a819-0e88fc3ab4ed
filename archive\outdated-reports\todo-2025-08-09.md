# TODO & Cleanup Plan (2025-08-09)

Scope: Track actionable work to stabilize startup, clean up scripts, and streamline the architecture after the manifest+loader refactor and logger hardening.

## P0 (High priority)

- [ ] Boot sequence verification
  - [ ] Ensure `core/script-manifest.js` includes all required modules in correct phases
  - [ ] Expose loader stats to `window.OTA.loaderStats` (timings, errors)
  - [ ] Smoke test: no console errors; `ApplicationBootstrap.start()` => success

- [ ] FeatureToggle gating (production defaults)
  - [ ] Set conservative defaults in `core/feature-toggle.js`: disable dev/diagnostic in prod
  - [ ] Make validators/guardians/duplicate-checker conditional on flags
  - [ ] Exclude dev-only scripts from manifest in prod

- [ ] Prune/archive demo/unused
  - [ ] Move to `archive/` or `tests/` and remove from manifest
    - [ ] `js/managers/simple-ota-manager.js`
    - [ ] `js/simple-channel-detection.js`
    - [ ] `demo-simplified-ota.html`
    - [ ] Debug-only HTML under `tests/` should be labeled and not linked from prod

- [ ] Fix small index.html lint issues
  - [ ] Move inline styles to CSS
  - [ ] Ensure form controls have accessible labels

- [ ] Fliggy channel E2E
  - [ ] Run `diagnose-channel-strategy.js` in browser and verify all checks pass

## P1 (Medium priority)

- [ ] Static data to JSON
  - [ ] Extract `hotel-data-inline.js`, `hotel-name-database.js` into `/data/*.json`
  - [ ] Add async data loader util with fallback

- [ ] i18n unification
  - [ ] Clarify ownership between `language-manager.js` and `i18n.js`
  - [ ] Single source of truth for locale & translations

- [ ] Logger audit & docs
  - [ ] Normalize `logger.log(message, level, data)` usage across codebase
  - [ ] Add JSDoc typedefs and usage notes

- [ ] Tests & harness
  - [ ] Browser tests: channel detection, strategy snippets, multi-order happy path
  - [ ] Optional Node harness for regex/helpers

- [ ] Manifest refinement
  - [ ] Validate dependencies per phase
  - [ ] Consider parallelizing safe data/config groups

## P2 (Lower priority)

- [ ] Performance & lazy-load
  - [ ] Defer heavy modules (e.g., Gemini) until needed
  - [ ] Optionally split modules and dynamic import via loader

- [ ] Docs
  - [ ] Update root README and `reports/architecture_solution_summary.md` to cover manifest+loader
  - [ ] Document phase boundaries and how to add a script

- [ ] Deployment hygiene
  - [ ] Review `netlify.toml` and `deployment/` scripts; ensure debug pages excluded from prod
  - [ ] Add basic health check endpoint/page

- [ ] Code style
  - [ ] Add ESLint/Prettier; `npm run lint`, `npm run format`

## Risks / Edge cases

- Fallback logger must never assume level is a string
- Manifest omissions cause runtime ReferenceErrors—add guard logs and fail-fast
- JSON fetch can fail—provide inline fallback or cache last-known data

## Acceptance criteria

- P0: Clean boot with manifest; dev-only gated; demo archived; Fliggy diagnosis passes
- P1: Data moved to JSON; i18n unified; logger usage normalized; basic tests pass; safe parallelization validated
- P2: Heavy features lazy-loaded; docs/deployment cleaned; linters available
