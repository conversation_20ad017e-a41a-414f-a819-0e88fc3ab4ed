/**
 * 组件注册中心
 * 文件: js/pages/multi-order/components/component-registry.js
 * 角色: 管理所有UI组件的注册、创建和生命周期
 * 
 * @COMPONENT_REGISTRY 组件注册中心
 * 🏷️ 标签: @OTA_COMPONENT_REGISTRY
 * 📝 说明: 统一管理组件的注册和实例化
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 组件注册中心类
     * 管理组件的注册、创建和销毁
     */
    class ComponentRegistry {
        constructor() {
            // 注册的组件类
            this.componentClasses = new Map();
            
            // 组件实例
            this.componentInstances = new Map();
            
            // 组件依赖关系
            this.dependencies = new Map();
            
            // 日志服务
            this.logger = this.getLogger();
            
            this.logger.log('🏭 组件注册中心已初始化', 'info');
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 注册组件类
         * @param {string} name - 组件名称
         * @param {Function} componentClass - 组件类
         * @param {Object} options - 注册选项
         */
        register(name, componentClass, options = {}) {
            try {
                if (!name || typeof name !== 'string') {
                    throw new Error('组件名称必须是非空字符串');
                }

                if (!componentClass || typeof componentClass !== 'function') {
                    throw new Error('组件类必须是构造函数');
                }

                // 检查是否已注册
                if (this.componentClasses.has(name)) {
                    if (!options.allowOverride) {
                        throw new Error(`组件 ${name} 已经注册，使用 allowOverride: true 来覆盖`);
                    }
                    this.logger.log(`⚠️ 覆盖已注册的组件: ${name}`, 'warning');
                }

                // 注册组件
                this.componentClasses.set(name, {
                    componentClass,
                    options: {
                        singleton: false,
                        autoCreate: false,
                        dependencies: [],
                        ...options
                    },
                    registeredAt: Date.now()
                });

                // 记录依赖关系
                if (options.dependencies && Array.isArray(options.dependencies)) {
                    this.dependencies.set(name, options.dependencies);
                }

                this.logger.log(`📝 组件已注册: ${name}`, 'info');

                // Linus: 如果设置了自动创建，异步创建实例（不阻塞注册过程）
                if (options.autoCreate) {
                    // 使用 Promise 异步创建，避免阻塞注册流程
                    this.create(name, options.defaultOptions).catch(error => {
                        this.logger.logError(`自动创建组件 ${name} 失败`, error);
                    });
                }

            } catch (error) {
                this.logger.logError(`组件注册失败: ${name}`, error);
                throw error;
            }
        }

        /**
         * 创建组件实例
         * @param {string} name - 组件名称
         * @param {Object} options - 创建选项
         * @returns {Promise<Object>} 组件实例
         */
        async create(name, options = {}) {
            try {
                if (!this.componentClasses.has(name)) {
                    throw new Error(`组件 ${name} 未注册`);
                }

                const componentInfo = this.componentClasses.get(name);
                const { componentClass, options: registrationOptions } = componentInfo;

                // 检查单例模式
                if (registrationOptions.singleton && this.componentInstances.has(name)) {
                    this.logger.log(`♻️ 返回单例组件实例: ${name}`, 'info');
                    return this.componentInstances.get(name);
                }

                // 检查依赖 - Linus: 现在语法正确了
                await this.resolveDependencies(name);

                // 创建实例
                const instance = new componentClass({
                    ...registrationOptions.defaultOptions,
                    ...options
                });

                // 添加组件元数据
                instance._componentName = name;
                instance._createdAt = Date.now();
                instance._registry = this;

                // 存储实例
                const instanceKey = registrationOptions.singleton ? name : `${name}_${instance.componentId}`;
                this.componentInstances.set(instanceKey, instance);

                this.logger.log(`🎨 组件实例已创建: ${name}`, 'success');

                return instance;

            } catch (error) {
                this.logger.logError(`组件创建失败: ${name}`, error);
                throw error;
            }
        }

        /**
         * 解析组件依赖
         * @param {string} name - 组件名称
         */
        async resolveDependencies(name) {
            const dependencies = this.dependencies.get(name);
            if (!dependencies || dependencies.length === 0) {
                return;
            }

            for (const dependency of dependencies) {
                if (!this.componentClasses.has(dependency)) {
                    throw new Error(`依赖组件 ${dependency} 未注册`);
                }

                // 如果依赖组件是单例且未创建，先创建它
                const depInfo = this.componentClasses.get(dependency);
                if (depInfo.options.singleton && !this.componentInstances.has(dependency)) {
                    await this.create(dependency);
                }
            }
        }

        /**
         * 获取组件实例
         * @param {string} name - 组件名称
         * @param {string} instanceId - 实例ID（可选）
         * @returns {Object|null} 组件实例
         */
        getInstance(name, instanceId = null) {
            const key = instanceId ? `${name}_${instanceId}` : name;
            
            if (this.componentInstances.has(key)) {
                return this.componentInstances.get(key);
            }

            // 如果是单例组件，尝试查找
            if (!instanceId) {
                for (const [key, instance] of this.componentInstances) {
                    if (instance._componentName === name) {
                        return instance;
                    }
                }
            }

            return null;
        }

        /**
         * 获取所有组件实例
         * @param {string} name - 组件名称（可选）
         * @returns {Array} 组件实例数组
         */
        getAllInstances(name = null) {
            if (name) {
                return Array.from(this.componentInstances.values())
                    .filter(instance => instance._componentName === name);
            }
            return Array.from(this.componentInstances.values());
        }

        /**
         * 销毁组件实例
         * @param {string} name - 组件名称
         * @param {string} instanceId - 实例ID（可选）
         */
        destroy(name, instanceId = null) {
            try {
                const key = instanceId ? `${name}_${instanceId}` : name;
                const instance = this.componentInstances.get(key);

                if (instance) {
                    // 调用组件的销毁方法
                    if (typeof instance.destroy === 'function') {
                        instance.destroy();
                    }

                    // 从注册表中移除
                    this.componentInstances.delete(key);

                    this.logger.log(`🗑️ 组件实例已销毁: ${name}`, 'info');
                } else {
                    this.logger.log(`⚠️ 组件实例未找到: ${name}`, 'warning');
                }

            } catch (error) {
                this.logger.logError(`组件销毁失败: ${name}`, error);
            }
        }

        /**
         * 销毁所有组件实例
         * @param {string} name - 组件名称（可选，如果提供则只销毁该组件的所有实例）
         */
        destroyAll(name = null) {
            try {
                const instancesToDestroy = name 
                    ? this.getAllInstances(name)
                    : Array.from(this.componentInstances.values());

                instancesToDestroy.forEach(instance => {
                    if (typeof instance.destroy === 'function') {
                        instance.destroy();
                    }
                });

                if (name) {
                    // 移除特定组件的所有实例
                    for (const [key, instance] of this.componentInstances) {
                        if (instance._componentName === name) {
                            this.componentInstances.delete(key);
                        }
                    }
                } else {
                    // 清空所有实例
                    this.componentInstances.clear();
                }

                this.logger.log(`🗑️ 组件实例批量销毁完成${name ? `: ${name}` : ''}`, 'info');

            } catch (error) {
                this.logger.logError('组件批量销毁失败', error);
            }
        }

        /**
         * 注销组件类
         * @param {string} name - 组件名称
         */
        unregister(name) {
            try {
                // 先销毁所有实例
                this.destroyAll(name);

                // 移除注册信息
                this.componentClasses.delete(name);
                this.dependencies.delete(name);

                this.logger.log(`📝 组件已注销: ${name}`, 'info');

            } catch (error) {
                this.logger.logError(`组件注销失败: ${name}`, error);
            }
        }

        /**
         * 检查组件是否已注册
         * @param {string} name - 组件名称
         * @returns {boolean} 是否已注册
         */
        isRegistered(name) {
            return this.componentClasses.has(name);
        }

        /**
         * 获取已注册的组件列表
         * @returns {Array} 组件名称数组
         */
        getRegisteredComponents() {
            return Array.from(this.componentClasses.keys());
        }

        /**
         * 获取组件信息
         * @param {string} name - 组件名称
         * @returns {Object|null} 组件信息
         */
        getComponentInfo(name) {
            return this.componentClasses.get(name) || null;
        }

        /**
         * 获取注册中心统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                registeredComponents: this.componentClasses.size,
                activeInstances: this.componentInstances.size,
                componentList: this.getRegisteredComponents(),
                instanceList: Array.from(this.componentInstances.keys())
            };
        }

        /**
         * 清理注册中心
         */
        clear() {
            this.destroyAll();
            this.componentClasses.clear();
            this.dependencies.clear();
            this.logger.log('🧹 组件注册中心已清理', 'info');
        }
    }

    // 创建全局组件注册中心实例
    const componentRegistry = new ComponentRegistry();

    // 暴露到OTA命名空间
    window.OTA.Components.ComponentRegistry = ComponentRegistry;
    window.OTA.Components.registry = componentRegistry;

    // 向后兼容
    window.componentRegistry = componentRegistry;

    console.log('✅ 组件注册中心已加载');

})();
