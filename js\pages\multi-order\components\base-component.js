/**
 * 基础组件类
 * 文件: js/pages/multi-order/components/base-component.js
 * 角色: 所有UI组件的基础类，提供通用的组件功能和生命周期管理
 * 
 * @BASE_COMPONENT 基础组件类
 * 🏷️ 标签: @OTA_BASE_COMPONENT
 * 📝 说明: 组件体系的基础类，定义组件接口和通用功能
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Components = window.OTA.Components || {};

(function() {
    'use strict';

    /**
     * 基础组件类
     * 所有UI组件的基础类
     */
    class BaseComponent {
        constructor(options = {}) {
            this.options = {
                container: null,
                autoRender: true,
                enableEvents: true,
                enableLogging: true,
                ...options
            };

            // 组件状态
            this.state = {
                isInitialized: false,
                isRendered: false,
                isDestroyed: false,
                data: null
            };

            // 组件元素
            this.elements = {
                container: null,
                root: null
            };

            // 事件监听器
            this.eventListeners = new Map();

            // 日志服务
            this.logger = this.getLogger();

            // 组件ID
            this.componentId = this.generateComponentId();

            // 初始化组件
            this.init();
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            if (!this.options.enableLogging) {
                return {
                    log: () => {},
                    logError: () => {}
                };
            }

            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 生成组件ID
         * @returns {string} 组件ID
         */
        generateComponentId() {
            return `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 初始化组件
         */
        init() {
            try {
                this.logger.log(`🎨 初始化组件: ${this.constructor.name}`, 'info');

                // 设置容器
                this.setupContainer();

                // 初始化状态
                this.initializeState();

                // 绑定事件
                if (this.options.enableEvents) {
                    this.bindEvents();
                }

                // 自动渲染
                if (this.options.autoRender) {
                    this.render();
                }

                this.state.isInitialized = true;
                this.logger.log(`✅ 组件初始化完成: ${this.constructor.name}`, 'success');

            } catch (error) {
                this.logger.logError(`组件初始化失败: ${this.constructor.name}`, error);
                throw error;
            }
        }

        /**
         * 设置容器
         */
        setupContainer() {
            if (this.options.container) {
                if (typeof this.options.container === 'string') {
                    this.elements.container = document.querySelector(this.options.container);
                } else if (this.options.container instanceof HTMLElement) {
                    this.elements.container = this.options.container;
                }

                if (!this.elements.container) {
                    throw new Error(`容器未找到: ${this.options.container}`);
                }
            }
        }

        /**
         * 初始化状态 - 子类可重写
         */
        initializeState() {
            // 子类实现具体的状态初始化
        }

        /**
         * 绑定事件 - 子类可重写
         */
        bindEvents() {
            // 子类实现具体的事件绑定
        }

        /**
         * 渲染组件 - 子类必须实现
         */
        render() {
            throw new Error('render方法必须由子类实现');
        }

        /**
         * 更新组件数据
         * @param {Object} data - 新数据
         */
        updateData(data) {
            this.state.data = data;
            this.onDataUpdated(data);
        }

        /**
         * 数据更新回调 - 子类可重写
         * @param {Object} data - 更新的数据
         */
        onDataUpdated(data) {
            // 默认重新渲染
            if (this.state.isRendered) {
                this.render();
            }
        }

        /**
         * 添加事件监听器
         * @param {HTMLElement} element - 目标元素
         * @param {string} event - 事件类型
         * @param {Function} handler - 事件处理器
         * @param {Object} options - 事件选项
         */
        addEventListener(element, event, handler, options = {}) {
            if (!element || !event || !handler) {
                this.logger.logError('添加事件监听器参数无效', new Error('Invalid parameters'));
                return;
            }

            const wrappedHandler = (e) => {
                try {
                    handler.call(this, e);
                } catch (error) {
                    this.logger.logError(`事件处理器执行失败: ${event}`, error);
                }
            };

            element.addEventListener(event, wrappedHandler, options);

            // 记录事件监听器以便清理
            const key = `${element.tagName}_${event}_${Date.now()}`;
            this.eventListeners.set(key, {
                element,
                event,
                handler: wrappedHandler,
                options
            });
        }

        /**
         * 移除所有事件监听器
         */
        removeAllEventListeners() {
            this.eventListeners.forEach(({ element, event, handler, options }) => {
                try {
                    element.removeEventListener(event, handler, options);
                } catch (error) {
                    this.logger.logError('移除事件监听器失败', error);
                }
            });
            this.eventListeners.clear();
        }

        /**
         * 显示组件
         */
        show() {
            if (this.elements.root) {
                this.elements.root.classList.remove('hidden');
                this.elements.root.style.display = '';
                this.onShow();
            }
        }

        /**
         * 隐藏组件
         */
        hide() {
            if (this.elements.root) {
                this.elements.root.classList.add('hidden');
                this.elements.root.style.display = 'none';
                this.onHide();
            }
        }

        /**
         * 显示回调 - 子类可重写
         */
        onShow() {
            // 子类可以重写此方法
        }

        /**
         * 隐藏回调 - 子类可重写
         */
        onHide() {
            // 子类可以重写此方法
        }

        /**
         * 启用组件
         */
        enable() {
            if (this.elements.root) {
                this.elements.root.classList.remove('disabled');
                this.elements.root.removeAttribute('disabled');
                this.onEnable();
            }
        }

        /**
         * 禁用组件
         */
        disable() {
            if (this.elements.root) {
                this.elements.root.classList.add('disabled');
                this.elements.root.setAttribute('disabled', 'true');
                this.onDisable();
            }
        }

        /**
         * 启用回调 - 子类可重写
         */
        onEnable() {
            // 子类可以重写此方法
        }

        /**
         * 禁用回调 - 子类可重写
         */
        onDisable() {
            // 子类可以重写此方法
        }

        /**
         * 获取组件状态
         * @returns {Object} 组件状态
         */
        getState() {
            return { ...this.state };
        }

        /**
         * 检查组件是否已初始化
         * @returns {boolean} 是否已初始化
         */
        isInitialized() {
            return this.state.isInitialized;
        }

        /**
         * 检查组件是否已渲染
         * @returns {boolean} 是否已渲染
         */
        isRendered() {
            return this.state.isRendered;
        }

        /**
         * 检查组件是否已销毁
         * @returns {boolean} 是否已销毁
         */
        isDestroyed() {
            return this.state.isDestroyed;
        }

        /**
         * 销毁组件
         */
        destroy() {
            if (this.state.isDestroyed) {
                return;
            }

            try {
                this.logger.log(`🗑️ 销毁组件: ${this.constructor.name}`, 'info');

                // 移除事件监听器
                this.removeAllEventListeners();

                // 清理DOM元素
                if (this.elements.root && this.elements.root.parentNode) {
                    this.elements.root.parentNode.removeChild(this.elements.root);
                }

                // 清理状态
                this.state.isDestroyed = true;
                this.state.isInitialized = false;
                this.state.isRendered = false;

                // 清理元素引用
                this.elements.container = null;
                this.elements.root = null;

                // 调用子类清理方法
                this.onDestroy();

                this.logger.log(`✅ 组件销毁完成: ${this.constructor.name}`, 'success');

            } catch (error) {
                this.logger.logError(`组件销毁失败: ${this.constructor.name}`, error);
            }
        }

        /**
         * 销毁回调 - 子类可重写
         */
        onDestroy() {
            // 子类可以重写此方法进行额外的清理
        }
    }

    // 暴露到OTA命名空间
    window.OTA.Components.BaseComponent = BaseComponent;

    console.log('✅ 基础组件类已加载');

})();
