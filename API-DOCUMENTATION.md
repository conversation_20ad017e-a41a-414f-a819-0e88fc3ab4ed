# API文档 - OTA系统 Linus重构版

## 📋 概述

本文档描述了OTA订单处理系统的API接口，包括内部API和外部集成接口。系统采用RESTful API设计，支持JSON格式数据交换。

## 🔑 认证

### API密钥配置
```javascript
// 设置Gemini API密钥
window.productionConfig.set('gemini.apiKey', 'YOUR_API_KEY');

// 设置系统API密钥（如需要）
window.productionConfig.set('api.authToken', 'YOUR_AUTH_TOKEN');
```

### 认证头
```http
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
User-Agent: OTA-System-v1.0.0
```

## 🌐 API端点

### 基础URL
```
开发环境: http://localhost:8000/api
测试环境: https://staging-api.gomyhire.com/api
生产环境: https://gomyhire.com.my/api
```

## 📝 订单管理API

### 1. 创建订单
```http
POST /api/v1/order-jobs
Content-Type: application/json

{
  "customer_name": "张三",
  "customer_contact": "1380013800",
  "pickup": "香格里拉酒店",
  "destination": "KLIA机场",
  "date": "2025-08-16",
  "time": "14:00",
  "passenger_number": 2,
  "sub_category_id": 1,
  "car_type_id": 1,
  "driving_region_id": 1,
  "special_requirements": "需要儿童座椅"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "order_number": "OTA20250816001",
    "status": "confirmed",
    "created_at": "2025-08-16T06:00:00Z",
    "estimated_price": 180.00,
    "currency": "MYR"
  },
  "message": "订单创建成功"
}
```

### 2. 获取订单列表
```http
GET /api/v1/order-jobs?limit=20&offset=0&status=all&date_from=2025-08-01&date_to=2025-08-31
```

**响应**:
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": 12345,
        "order_number": "OTA20250816001",
        "customer_name": "张三",
        "pickup": "香格里拉酒店",
        "destination": "KLIA机场",
        "date": "2025-08-16",
        "time": "14:00",
        "status": "confirmed",
        "created_at": "2025-08-16T06:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}
```

### 3. 获取订单详情
```http
GET /api/v1/order-jobs/12345
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "order_number": "OTA20250816001",
    "customer_name": "张三",
    "customer_contact": "1380013800",
    "pickup": "香格里拉酒店",
    "destination": "KLIA机场",
    "date": "2025-08-16",
    "time": "14:00",
    "passenger_number": 2,
    "status": "confirmed",
    "price": 180.00,
    "currency": "MYR",
    "driver_info": {
      "name": "Ahmad",
      "phone": "1234567890",
      "vehicle": "Proton X70 - ABC1234"
    },
    "created_at": "2025-08-16T06:00:00Z",
    "updated_at": "2025-08-16T06:05:00Z"
  }
}
```

### 4. 更新订单
```http
PUT /api/v1/order-jobs/12345
Content-Type: application/json

{
  "time": "15:00",
  "passenger_number": 3,
  "special_requirements": "需要儿童座椅和婴儿座椅"
}
```

### 5. 删除订单
```http
DELETE /api/v1/order-jobs/12345
```

### 6. 批量创建订单
```http
POST /api/v1/order-jobs/batch
Content-Type: application/json

{
  "orders": [
    {
      "customer_name": "张三",
      "customer_contact": "1380013800",
      "pickup": "香格里拉酒店",
      "destination": "KLIA机场",
      "date": "2025-08-16",
      "time": "14:00",
      "passenger_number": 2
    },
    {
      "customer_name": "李四",
      "customer_contact": "1390013900",
      "pickup": "希尔顿酒店",
      "destination": "LCCT机场",
      "date": "2025-08-16",
      "time": "15:30",
      "passenger_number": 3
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "created": 2,
    "failed": 0,
    "orders": [
      {
        "id": 12345,
        "order_number": "OTA20250816001",
        "customer_name": "张三"
      },
      {
        "id": 12346,
        "order_number": "OTA20250816002",
        "customer_name": "李四"
      }
    ]
  }
}
```

## 📊 数据字典API

### 1. 获取子分类
```http
GET /api/v1/sub-categories
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "机场接送",
      "name_en": "Airport Transfer",
      "description": "往返机场的接送服务"
    },
    {
      "id": 2,
      "name": "市内包车",
      "name_en": "City Charter",
      "description": "市内点对点包车服务"
    }
  ]
}
```

### 2. 获取车型
```http
GET /api/v1/car-types
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "标准轿车",
      "name_en": "Standard Sedan",
      "capacity": 4,
      "description": "适合1-4人乘坐"
    },
    {
      "id": 2,
      "name": "商务车",
      "name_en": "MPV",
      "capacity": 7,
      "description": "适合5-7人乘坐"
    }
  ]
}
```

### 3. 获取服务区域
```http
GET /api/v1/driving-regions
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "吉隆坡",
      "name_en": "Kuala Lumpur",
      "code": "KL",
      "active": true
    },
    {
      "id": 2,
      "name": "槟城",
      "name_en": "Penang",
      "code": "PG",
      "active": true
    }
  ]
}
```

## 🖼️ 图片处理API

### 1. 上传图片
```http
POST /api/v1/upload
Content-Type: multipart/form-data

FormData:
- image: [File]
- type: "order_analysis"
```

**响应**:
```json
{
  "success": true,
  "data": {
    "file_id": "img_123456",
    "url": "https://cdn.example.com/uploads/img_123456.jpg",
    "analysis": {
      "detected_text": "客户：张三\n电话：1380013800\n从：香格里拉酒店\n到：KLIA机场",
      "extracted_orders": [
        {
          "customer_name": "张三",
          "customer_contact": "1380013800",
          "pickup": "香格里拉酒店",
          "destination": "KLIA机场",
          "confidence": 0.95
        }
      ]
    }
  }
}
```

### 2. 分析图片
```http
POST /api/v1/analyze-image
Content-Type: application/json

{
  "image_data": "base64_encoded_image_data",
  "type": "order_extraction"
}
```

## 🤖 AI集成API

### 1. Gemini API调用
```javascript
// 内部API，通过core.js调用
const response = await window.ota.callGeminiAPI({
  prompt: "分析这个订单信息...",
  image: imageData, // 可选
  temperature: 0.1
});
```

### 2. 智能订单分割
```javascript
// 分割多订单文本
const orders = await window.ota.detectAndSplitMultiOrdersWithVerification(
  "多个订单的文本内容..."
);
```

### 3. 地点智能识别
```javascript
// 识别和标准化地点名称
const standardizedLocation = await window.ota.normalizeLocation("香格里拉");
// 返回: "Shangri-La Hotel Kuala Lumpur"
```

## 📈 监控API

### 1. 获取性能指标
```http
GET /api/monitoring/metrics
```

**响应**:
```json
{
  "success": true,
  "data": {
    "response_time": {
      "average": 156,
      "p95": 280,
      "p99": 450
    },
    "throughput": 45.2,
    "error_rate": 0.012,
    "active_users": 23,
    "timestamp": "2025-08-16T06:00:00Z"
  }
}
```

### 2. 提交错误报告
```http
POST /api/monitoring/errors
Content-Type: application/json

{
  "type": "api_error",
  "message": "Request timeout",
  "stack": "Error stack trace...",
  "url": "/api/v1/order-jobs",
  "user_agent": "Mozilla/5.0...",
  "timestamp": "2025-08-16T06:00:00Z"
}
```

### 3. 获取系统状态
```http
GET /api/monitoring/health
```

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0-linus-refactor",
    "uptime": 86400,
    "memory_usage": 45.2,
    "cpu_usage": 12.8,
    "services": {
      "database": "connected",
      "gemini_api": "available",
      "cache": "active"
    }
  }
}
```

## 🔧 JavaScript客户端API

### 核心对象
```javascript
// 全局OTA对象
window.ota = {
  // 配置管理
  config: {},
  
  // 订单操作
  createOrder: async (orderData) => {},
  getOrderHistory: () => {},
  deleteOrder: async (orderId) => {},
  
  // 图片处理
  uploadAndAnalyzeImage: async (file) => {},
  analyzeImage: async (imageData) => {},
  
  // AI功能
  callGeminiAPI: async (params) => {},
  detectAndSplitMultiOrdersWithVerification: async (text) => {},
  
  // 工具函数
  validateOrderData: (data) => {},
  formatOrderData: (data) => {},
  normalizeLocation: (location) => {}
};
```

### 配置管理
```javascript
// 生产环境配置
window.productionConfig = {
  // 获取配置
  get: (path) => {},
  
  // 设置配置
  set: (path, value) => {},
  
  // 环境信息
  getEnvironmentInfo: () => {},
  
  // 深度合并
  deepMerge: (target, source) => {}
};
```

### 性能监控
```javascript
// 性能监控器
window.performanceMonitor = {
  // 获取指标
  getMetrics: () => {},
  
  // 生成报告
  generateReport: () => {},
  
  // 设置阈值
  setThreshold: (metric, value) => {},
  
  // 开始监控
  startMonitoring: () => {},
  
  // 停止监控
  stopMonitoring: () => {}
};
```

### 错误监控
```javascript
// 错误监控器
window.errorMonitor = {
  // 获取错误列表
  getErrors: () => {},
  
  // 获取错误摘要
  getErrorSummary: () => {},
  
  // 导出错误
  exportErrors: () => {},
  
  // 清除错误
  clearErrors: () => {},
  
  // 报告错误
  reportError: (error) => {}
};
```

## 📋 数据模型

### 订单模型
```typescript
interface Order {
  id?: number;
  order_number?: string;
  customer_name: string;
  customer_contact: string;
  pickup: string;
  destination: string;
  date: string; // YYYY-MM-DD
  time: string; // HH:MM
  passenger_number: number;
  sub_category_id?: number;
  car_type_id?: number;
  driving_region_id?: number;
  special_requirements?: string;
  status?: 'pending' | 'confirmed' | 'assigned' | 'completed' | 'cancelled';
  price?: number;
  currency?: string;
  created_at?: string;
  updated_at?: string;
}
```

### 响应模型
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    per_page?: number;
  };
}
```

### 错误模型
```typescript
interface ErrorReport {
  type: 'javascript_error' | 'api_error' | 'network_error' | 'validation_error';
  message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  user_agent?: string;
  timestamp: string;
  severity: 1 | 2 | 3 | 4 | 5; // 1=低, 5=严重
  context?: any;
}
```

## 🔌 Webhook

### 订单状态更新
```http
POST https://your-webhook-url.com/order-status
Content-Type: application/json

{
  "event": "order.status_changed",
  "data": {
    "order_id": 12345,
    "order_number": "OTA20250816001",
    "old_status": "confirmed",
    "new_status": "assigned",
    "timestamp": "2025-08-16T06:00:00Z"
  }
}
```

### 错误告警
```http
POST https://your-webhook-url.com/error-alert
Content-Type: application/json

{
  "event": "error.threshold_exceeded",
  "data": {
    "error_type": "api_timeout",
    "error_count": 10,
    "time_window": "5_minutes",
    "threshold": 5,
    "timestamp": "2025-08-16T06:00:00Z"
  }
}
```

## 🚦 限制和配额

### API限制
- **请求频率**: 1000次/小时/API密钥
- **并发连接**: 10个同时连接
- **文件上传**: 最大10MB/文件
- **批量操作**: 最多100个订单/次

### Gemini API限制
- **文本请求**: 100次/分钟
- **图片分析**: 50次/分钟
- **上下文长度**: 最大32K tokens
- **图片大小**: 最大20MB

## 📄 错误代码

| 代码 | HTTP状态 | 含义 | 解决方案 |
|------|----------|------|----------|
| E001 | 401 | API密钥无效 | 检查并重新配置API密钥 |
| E002 | 503 | 网络连接失败 | 检查网络连接 |
| E003 | 400 | 图片格式不支持 | 使用JPG/PNG/WebP格式 |
| E004 | 422 | 订单数据格式错误 | 检查必填字段 |
| E005 | 504 | 服务器响应超时 | 稍后重试 |
| E006 | 403 | 权限不足 | 检查API权限 |
| E007 | 429 | 请求频率过高 | 减少请求频率 |
| E008 | 413 | 文件过大 | 压缩文件大小 |

## 📚 示例代码

### 完整订单创建流程
```javascript
async function createOrderExample() {
  try {
    // 1. 验证数据
    const orderData = {
      customer_name: "张三",
      customer_contact: "1380013800",
      pickup: "香格里拉酒店",
      destination: "KLIA机场",
      date: "2025-08-16",
      time: "14:00",
      passenger_number: 2
    };
    
    const isValid = window.ota.validateOrderData(orderData);
    if (!isValid) {
      throw new Error('订单数据验证失败');
    }
    
    // 2. 格式化数据
    const formattedData = window.ota.formatOrderData(orderData);
    
    // 3. 创建订单
    const result = await window.ota.createOrder(formattedData);
    
    // 4. 处理结果
    if (result.success) {
      console.log('订单创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message || '订单创建失败');
    }
    
  } catch (error) {
    console.error('创建订单失败:', error);
    
    // 报告错误
    window.errorMonitor.reportError({
      type: 'api_error',
      message: error.message,
      context: { orderData }
    });
    
    throw error;
  }
}
```

### 图片分析示例
```javascript
async function analyzeImageExample(file) {
  try {
    // 1. 验证文件
    if (!file || file.size > 10 * 1024 * 1024) {
      throw new Error('文件无效或过大');
    }
    
    // 2. 上传并分析
    const result = await window.ota.uploadAndAnalyzeImage(file);
    
    // 3. 处理结果
    if (result.success && result.data.analysis) {
      const orders = result.data.analysis.extracted_orders;
      
      // 4. 批量创建订单
      for (const order of orders) {
        if (order.confidence > 0.8) {
          await window.ota.createOrder(order);
        }
      }
      
      return orders;
    }
    
  } catch (error) {
    console.error('图片分析失败:', error);
    throw error;
  }
}
```

## 🔗 相关链接

- [用户手册](USER-MANUAL.md)
- [迁移指南](MIGRATION-GUIDE.md)
- [监控面板](monitoring-dashboard.html)
- [负载测试工具](load-test.js)

---

**API文档版本**: 1.0.0  
**最后更新**: 2025-08-16  
**兼容系统版本**: OTA系统 Linus重构版 v1.0.0