#!/usr/bin/env node
/**
 * Deployment Comparator
 * 比对本地关键资源与线上(Netlify)部署版本差异 (内容哈希 / 大小 / 首行预览)
 * 使用方式:
 *   node deployment/compare-remote.js --url=https://your-site.netlify.app [--timeout=8000]
 * 输出: 控制台表格 + deployment/deployment-compare-report.json
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Node 18+ 自带 fetch
const args = process.argv.slice(2);
const argMap = Object.fromEntries(args.map(a => {
  const [k,v] = a.split('=');
  return [k.replace(/^--/, ''), v || true];
}));

const baseUrl = argMap.url || process.env.DEPLOY_BASE_URL;
if (!baseUrl) {
  console.error('❌ 缺少 --url 参数或 DEPLOY_BASE_URL 环境变量');
  process.exit(1);
}

const timeoutMs = parseInt(argMap.timeout || '8000', 10);

// 需要比对的核心文件（基础清单）
const coreFiles = [
  'index.html',
  'sw.js',
  'main.js',
  'js/core/script-manifest.js',
  'js/core/script-loader.js',
  'js/core/dependency-container.js',
  'js/core/service-locator.js',
  'js/core/application-bootstrap.js'
];

// 尝试从 manifest 中动态提取脚本路径
function extractScriptsFromManifest() {
  try {
    const manifestPath = path.join(process.cwd(), 'js', 'core', 'script-manifest.js');
    if (!fs.existsSync(manifestPath)) return [];
    const content = fs.readFileSync(manifestPath, 'utf8');
    const regex = /'([^'\n]+\.js)'/g;
    const set = new Set();
    let m;
    while ((m = regex.exec(content))) {
      const p = m[1];
      // 过滤掉注释掉的脚本（简单：匹配点之前50字符内是否出现 //）
      const prefix = content.slice(Math.max(0, m.index - 50), m.index);
      if (/\/\//.test(prefix)) continue;
      if (p.startsWith('http')) continue;
      if (!p.endsWith('.js')) continue;
      set.add(p);
    }
    return Array.from(set);
  } catch (e) {
    return [];
  }
}

const dynamicScripts = extractScriptsFromManifest();
dynamicScripts.forEach(f => { if (!coreFiles.includes(f)) coreFiles.push(f); });

function sha256(buf) { return crypto.createHash('sha256').update(buf).digest('hex'); }

async function fetchWithTimeout(url) {
  const ctrl = new AbortController();
  const id = setTimeout(() => ctrl.abort(), timeoutMs);
  try {
    const res = await fetch(url, { signal: ctrl.signal, cache: 'no-store' });
    const status = res.status;
    const text = await res.text();
    return { ok: res.ok, status, text, headers: Object.fromEntries(res.headers.entries()) };
  } catch (err) {
    return { ok: false, status: 0, error: err.message };
  } finally {
    clearTimeout(id);
  }
}

function readLocal(rel) {
  const filePath = path.join(process.cwd(), rel);
  if (!fs.existsSync(filePath)) return null;
  const content = fs.readFileSync(filePath);
  return { content, text: content.toString('utf8') };
}

async function run() {
  console.log(`🔍 对比本地与线上: ${baseUrl}`);
  const report = { baseUrl, generatedAt: new Date().toISOString(), files: [] };

  for (const rel of coreFiles) {
    const local = readLocal(rel);
    const remoteUrl = baseUrl.replace(/\/$/, '') + '/' + rel;
    const remote = await fetchWithTimeout(remoteUrl);

    const localHash = local ? sha256(local.content) : null;
    const remoteHash = remote.ok ? sha256(Buffer.from(remote.text)) : null;

    const same = localHash && remoteHash && localHash === remoteHash;
    const entry = {
      file: rel,
      localExists: !!local,
      remoteStatus: remote.status,
      same,
      localHash,
      remoteHash,
      localSize: local ? local.content.length : null,
      remoteSize: remote.ok ? remote.text.length : null,
      remoteError: remote.error || null,
      previewLocal: local ? local.text.split('\n')[0]?.slice(0,120) : null,
      previewRemote: remote.ok ? remote.text.split('\n')[0]?.slice(0,120) : null
    };
    report.files.push(entry);
    const statusIcon = same ? '✅' : '⚠️';
    console.log(`${statusIcon} ${rel} ${same ? '一致' : '不一致'} (local:${localHash?.slice(0,8)||'-'} remote:${remoteHash?.slice(0,8)||'-'})`);
  }

  report.summary = {
    total: report.files.length,
    mismatches: report.files.filter(f => f.same === false).length,
    missingRemote: report.files.filter(f => f.remoteStatus !== 200).length,
    missingLocal: report.files.filter(f => !f.localExists).length
  };

  const outPath = path.join(process.cwd(), 'deployment', 'deployment-compare-report.json');
  fs.writeFileSync(outPath, JSON.stringify(report, null, 2));
  console.log(`\n📝 报告已生成: deployment/deployment-compare-report.json`);
  console.log(`📊 汇总:`, report.summary);

  if (report.summary.mismatches > 0) {
    console.log('\n👉 建议:');
    console.log('- 执行: Clear cache & deploy site (Netlify)');
    console.log('- 客户端清理 SW 与 caches 后再试');
    console.log('- 确认 main 分支最新提交已触发部署');
  }
}

run().catch(e => { console.error('运行失败:', e); process.exit(1); });
