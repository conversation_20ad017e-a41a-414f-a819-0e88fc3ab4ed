# 无感知刷新功能使用指南

## 🎯 功能概述

无感知刷新功能允许应用在检测到新版本时，智能选择合适的时机自动更新，无需用户手动操作。这确保用户始终使用最新版本，同时最大化用户体验。

## ✨ 核心特性

### 🔄 智能更新时机
- **页面隐藏时**：用户切换到其他标签页或应用时立即更新
- **用户空闲时**：检测到用户5分钟无操作时自动更新
- **强制更新**：等待30分钟后强制更新（可配置）

### 🛡️ 数据保护
- **表单数据保护**：检测到未保存的表单数据时延迟更新
- **自动保存**：更新前自动保存用户数据到本地存储
- **数据恢复**：更新后自动恢复保存的数据

### 👁️ 用户体验
- **最小化通知**：仅显示2秒的简短更新提示
- **无感知操作**：大多数情况下用户不会察觉更新过程
- **优雅降级**：更新失败时自动回退到手动模式

## 🚀 快速开始

### 1. 启用无感知更新

无感知更新默认已启用。如需禁用，可以修改配置：

```javascript
// 禁用无感知更新
window.OTA.silentUpdateConfig.set('enabled', false);

// 或者通过 localStorage
localStorage.setItem('silent_update_config', JSON.stringify({
    enabled: false
}));
```

### 2. 自定义配置

```javascript
// 获取当前配置
const config = window.OTA.silentUpdateConfig.getAll();

// 修改配置
window.OTA.silentUpdateConfig.set('idleTimeout', 180000); // 3分钟空闲
window.OTA.silentUpdateConfig.set('maxWaitTime', 900000);  // 15分钟最大等待

// 保存用户配置
window.OTA.silentUpdateConfig.saveUserConfig({
    idleTimeout: 180000,
    maxWaitTime: 900000,
    showMinimalNotification: false
});
```

### 3. 监听更新事件

```javascript
// 监听版本更新
window.addEventListener('storage', (e) => {
    if (e.key === 'ota_last_build_hash') {
        console.log('检测到版本更新');
    }
});

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('页面隐藏，可能触发更新');
    }
});
```

## ⚙️ 配置选项

### 基础设置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `enabled` | `true` | 启用无感知更新 |
| `mode` | `'smart'` | 更新模式：`immediate`、`smart`、`manual` |

### 时机控制

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `updateOnHidden` | `true` | 页面隐藏时更新 |
| `updateOnIdle` | `true` | 用户空闲时更新 |
| `idleTimeout` | `300000` | 空闲阈值（毫秒，默认5分钟） |
| `maxWaitTime` | `1800000` | 最大等待时间（毫秒，默认30分钟） |

### 数据保护

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `preserveFormData` | `true` | 保护表单数据 |
| `autoSaveBeforeUpdate` | `true` | 更新前自动保存 |

### 用户体验

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `showMinimalNotification` | `true` | 显示最小化通知 |
| `notificationDuration` | `2000` | 通知显示时间（毫秒） |

## 🧪 测试功能

### 使用测试页面

访问 `tests/test-silent-update.html` 来测试无感知更新功能：

1. **配置测试**：调整各种配置选项
2. **表单数据测试**：填写表单测试数据保护
3. **模拟更新**：模拟版本更新、页面隐藏、用户空闲等场景
4. **实时日志**：查看详细的更新过程日志

### 手动测试步骤

1. **模拟版本更新**：
   ```javascript
   // 清除版本哈希触发更新检测
   localStorage.removeItem('ota_last_build_hash');
   ```

2. **测试页面隐藏**：
   - 切换到其他标签页
   - 最小化浏览器窗口
   - 切换到其他应用

3. **测试用户空闲**：
   - 停止鼠标和键盘操作5分钟
   - 观察是否触发自动更新

## 🔧 高级配置

### 环境特定配置

系统会自动检测运行环境并应用相应配置：

```javascript
// 开发环境
{
    idleTimeout: 60000,    // 1分钟空闲
    maxWaitTime: 300000,   // 5分钟最大等待
    debugMode: true
}

// 生产环境
{
    idleTimeout: 300000,   // 5分钟空闲
    maxWaitTime: 1800000,  // 30分钟最大等待
    debugMode: false
}
```

### 自定义通知样式

```javascript
// 修改通知位置
window.OTA.silentUpdateConfig.set('notificationPosition', 'bottom-left');

// 自定义通知样式（需要修改 CSS）
.silent-update-notification {
    background: rgba(76, 175, 80, 0.9) !important;
    color: white !important;
}
```

### 调试模式

```javascript
// 启用调试模式
window.OTA.silentUpdateConfig.set('debugMode', true);
window.OTA.silentUpdateConfig.set('verboseLogging', true);

// 查看配置摘要
console.log(window.OTA.silentUpdateConfig.getSummary());
```

## 🚨 故障排除

### 常见问题

#### 1. 更新不触发
**可能原因**：
- 无感知更新被禁用
- 检测到未保存的表单数据
- 用户一直处于活跃状态

**解决方案**：
```javascript
// 检查配置
console.log(window.OTA.silentUpdateConfig.get('enabled'));

// 检查表单数据
const form = document.getElementById('orderForm');
console.log(new FormData(form));

// 强制触发更新
localStorage.removeItem('ota_last_build_hash');
```

#### 2. 更新失败
**可能原因**：
- 网络连接问题
- Service Worker 注册失败
- 缓存清理失败

**解决方案**：
```javascript
// 检查 Service Worker 状态
navigator.serviceWorker.getRegistrations().then(console.log);

// 手动清理缓存
caches.keys().then(names => 
    Promise.all(names.map(name => caches.delete(name)))
);
```

#### 3. 数据丢失
**可能原因**：
- 自动保存功能被禁用
- localStorage 存储失败

**解决方案**：
```javascript
// 检查自动保存数据
const savedData = localStorage.getItem('ota_auto_save_before_update');
console.log(JSON.parse(savedData));

// 启用数据保护
window.OTA.silentUpdateConfig.set('preserveFormData', true);
```

## 📊 监控和分析

### 性能监控

```javascript
// 监控更新性能
const startTime = performance.now();
// ... 更新过程 ...
const updateTime = performance.now() - startTime;
console.log(`更新耗时: ${updateTime}ms`);
```

### 用户行为分析

```javascript
// 记录更新触发原因
localStorage.setItem('last_update_reason', 'page_hidden');
localStorage.setItem('last_update_time', Date.now());
```

## 🔮 未来计划

- **智能学习**：根据用户习惯优化更新时机
- **渐进式更新**：支持部分资源的增量更新
- **A/B 测试**：支持不同更新策略的效果对比
- **更新统计**：提供详细的更新成功率和用户体验数据

## 📞 技术支持

如果遇到问题或需要帮助，请：

1. 查看浏览器控制台的错误信息
2. 使用测试页面进行诊断
3. 检查配置是否正确
4. 联系技术支持团队

---

**注意**：无感知更新功能需要现代浏览器支持，建议使用 Chrome 80+、Firefox 75+、Safari 13+ 或 Edge 80+。
