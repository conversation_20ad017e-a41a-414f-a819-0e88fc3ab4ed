/**
 * 错误监控和报警系统 - Linus重构版
 * 
 * 专注于错误捕获、分析和报告
 * "失败快，修复快" - <PERSON><PERSON>原则
 */

'use strict';

class ErrorMonitor {
    constructor(config = {}) {
        this.config = {
            enabled: true,
            maxErrors: 500,
            reportEndpoint: '/api/errors',
            alertThresholds: {
                errorRate: 0.1, // 10%错误率
                criticalErrors: 5, // 5个严重错误
                timeWindow: 300000 // 5分钟时间窗口
            },
            ignoredErrors: [
                'Script error',
                'Non-Error promise rejection captured',
                'ResizeObserver loop limit exceeded'
            ],
            severityLevels: {
                LOW: 1,
                MEDIUM: 2,
                HIGH: 3,
                CRITICAL: 4
            },
            ...config
        };

        this.errors = [];
        this.errorCounts = new Map();
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();

        this.init();
    }

    init() {
        if (!this.config.enabled) return;

        console.log('🛡️ 启动错误监控系统...');

        // 捕获JavaScript错误
        this.setupJavaScriptErrorHandling();
        
        // 捕获Promise拒绝
        this.setupPromiseRejectionHandling();
        
        // 捕获资源加载错误
        this.setupResourceErrorHandling();
        
        // 捕获网络错误
        this.setupNetworkErrorHandling();
        
        // 捕获OTA系统特定错误
        this.setupOTAErrorHandling();

        console.log('✅ 错误监控系统已启动');
    }

    // JavaScript错误处理
    setupJavaScriptErrorHandling() {
        window.addEventListener('error', (event) => {
            const errorInfo = {
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: Date.now(),
                severity: this.calculateSeverity(event.error)
            };

            this.handleError(errorInfo);
        });
    }

    // Promise拒绝处理
    setupPromiseRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            const errorInfo = {
                type: 'promise_rejection',
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack,
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: Date.now(),
                severity: this.calculateSeverity(event.reason)
            };

            this.handleError(errorInfo);
            
            // 阻止在控制台显示
            event.preventDefault();
        });
    }

    // 资源加载错误处理
    setupResourceErrorHandling() {
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                const errorInfo = {
                    type: 'resource',
                    message: `Failed to load resource: ${event.target.src || event.target.href}`,
                    resource: event.target.src || event.target.href,
                    tagName: event.target.tagName,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: Date.now(),
                    severity: this.config.severityLevels.MEDIUM
                };

                this.handleError(errorInfo);
            }
        }, true);
    }

    // 网络错误处理
    setupNetworkErrorHandling() {
        // 拦截fetch错误
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                // 检查HTTP错误状态
                if (!response.ok) {
                    this.handleError({
                        type: 'network',
                        message: `HTTP ${response.status}: ${response.statusText}`,
                        url: args[0],
                        status: response.status,
                        statusText: response.statusText,
                        timestamp: Date.now(),
                        severity: response.status >= 500 ? 
                            this.config.severityLevels.HIGH : 
                            this.config.severityLevels.MEDIUM
                    });
                }
                
                return response;
            } catch (error) {
                this.handleError({
                    type: 'network',
                    message: `Network error: ${error.message}`,
                    url: args[0],
                    error: error.message,
                    timestamp: Date.now(),
                    severity: this.config.severityLevels.HIGH
                });
                throw error;
            }
        };
    }

    // OTA系统特定错误处理
    setupOTAErrorHandling() {
        // 监控Gemini API错误
        if (window.ota && window.ota.gemini) {
            const originalParseOrder = window.ota.gemini.parseOrder;
            window.ota.gemini.parseOrder = async (...args) => {
                try {
                    return await originalParseOrder.apply(window.ota.gemini, args);
                } catch (error) {
                    this.handleError({
                        type: 'ota_gemini',
                        message: `Gemini API error: ${error.message}`,
                        context: 'parseOrder',
                        args: args[0]?.substring(0, 100), // 前100字符
                        timestamp: Date.now(),
                        severity: this.config.severityLevels.HIGH
                    });
                    throw error;
                }
            };
        }

        // 监控API错误
        if (window.ota && window.ota.api) {
            const originalCreateOrder = window.ota.api.createOrder;
            window.ota.api.createOrder = async (...args) => {
                try {
                    return await originalCreateOrder.apply(window.ota.api, args);
                } catch (error) {
                    this.handleError({
                        type: 'ota_api',
                        message: `Order creation error: ${error.message}`,
                        context: 'createOrder',
                        orderData: JSON.stringify(args[0], null, 2).substring(0, 200),
                        timestamp: Date.now(),
                        severity: this.config.severityLevels.CRITICAL
                    });
                    throw error;
                }
            };
        }

        // 监控多订单处理错误
        if (window.ota && window.ota.multiOrder) {
            const originalBatchCreate = window.ota.multiOrder.batchCreate;
            window.ota.multiOrder.batchCreate = async (...args) => {
                try {
                    return await originalBatchCreate.apply(window.ota.multiOrder, args);
                } catch (error) {
                    this.handleError({
                        type: 'ota_multiorder',
                        message: `Multi-order processing error: ${error.message}`,
                        context: 'batchCreate',
                        orderCount: args[0]?.length || 0,
                        timestamp: Date.now(),
                        severity: this.config.severityLevels.HIGH
                    });
                    throw error;
                }
            };
        }
    }

    // 处理错误
    handleError(errorInfo) {
        // 忽略特定错误
        if (this.shouldIgnoreError(errorInfo)) {
            return;
        }

        // 添加会话信息
        errorInfo.sessionId = this.sessionId;
        errorInfo.sessionDuration = Date.now() - this.startTime;
        
        // 添加上下文信息
        errorInfo.context = this.gatherContext();
        
        // 添加错误指纹
        errorInfo.fingerprint = this.generateFingerprint(errorInfo);

        // 存储错误
        this.storeError(errorInfo);

        // 更新错误计数
        this.updateErrorCounts(errorInfo);

        // 检查告警条件
        this.checkAlertConditions(errorInfo);

        // 立即报告严重错误
        if (errorInfo.severity >= this.config.severityLevels.HIGH) {
            this.reportError(errorInfo);
        }

        // 控制台输出
        this.logError(errorInfo);
    }

    // 计算错误严重程度
    calculateSeverity(error) {
        if (!error) return this.config.severityLevels.LOW;

        const message = error.message || String(error);
        
        // 严重错误关键词
        const criticalKeywords = [
            'system crash', 'fatal', 'critical', 'security',
            'payment', 'auth', 'login', 'token'
        ];
        
        // 高级错误关键词
        const highKeywords = [
            'api', 'network', 'database', 'timeout',
            'order', 'gemini', 'parse'
        ];

        const lowerMessage = message.toLowerCase();
        
        if (criticalKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return this.config.severityLevels.CRITICAL;
        }
        
        if (highKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return this.config.severityLevels.HIGH;
        }
        
        if (error.stack && error.stack.includes('TypeError')) {
            return this.config.severityLevels.MEDIUM;
        }
        
        return this.config.severityLevels.LOW;
    }

    // 收集上下文信息
    gatherContext() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            
            window: {
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio
            },
            
            location: {
                href: window.location.href,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash
            },
            
            performance: {
                memory: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null,
                timing: performance.timing ? {
                    loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart
                } : null
            },

            ota: {
                systemLoaded: !!window.ota,
                coreModules: window.ota ? Object.keys(window.ota) : [],
                activeUser: localStorage.getItem('user_email'),
                lastActivity: localStorage.getItem('last_activity')
            }
        };
    }

    // 生成错误指纹
    generateFingerprint(errorInfo) {
        const key = `${errorInfo.type}_${errorInfo.message}_${errorInfo.filename}_${errorInfo.lineno}`;
        return this.hash(key);
    }

    // 简单哈希函数
    hash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(16);
    }

    // 存储错误
    storeError(errorInfo) {
        this.errors.push(errorInfo);

        // 限制内存使用
        if (this.errors.length > this.config.maxErrors) {
            this.errors.splice(0, this.errors.length - this.config.maxErrors);
        }
    }

    // 更新错误计数
    updateErrorCounts(errorInfo) {
        const key = errorInfo.fingerprint;
        const current = this.errorCounts.get(key) || { count: 0, firstSeen: Date.now(), lastSeen: Date.now() };
        
        current.count++;
        current.lastSeen = Date.now();
        
        this.errorCounts.set(key, current);
    }

    // 检查告警条件
    checkAlertConditions(errorInfo) {
        const { alertThresholds } = this.config;
        const recentErrors = this.getRecentErrors(alertThresholds.timeWindow);
        
        // 错误率告警
        const totalOperations = this.estimateOperations();
        const errorRate = recentErrors.length / Math.max(totalOperations, 1);
        
        if (errorRate > alertThresholds.errorRate) {
            this.triggerAlert('high_error_rate', {
                rate: errorRate,
                threshold: alertThresholds.errorRate,
                recentErrors: recentErrors.length,
                timeWindow: alertThresholds.timeWindow
            });
        }

        // 严重错误告警
        const criticalErrors = recentErrors.filter(e => 
            e.severity >= this.config.severityLevels.CRITICAL
        );
        
        if (criticalErrors.length >= alertThresholds.criticalErrors) {
            this.triggerAlert('critical_errors', {
                count: criticalErrors.length,
                threshold: alertThresholds.criticalErrors,
                timeWindow: alertThresholds.timeWindow
            });
        }

        // 重复错误告警
        const errorCount = this.errorCounts.get(errorInfo.fingerprint);
        if (errorCount && errorCount.count >= 10) {
            this.triggerAlert('repeated_error', {
                fingerprint: errorInfo.fingerprint,
                count: errorCount.count,
                message: errorInfo.message
            });
        }
    }

    // 触发告警
    triggerAlert(type, data) {
        const alert = {
            type,
            data,
            timestamp: Date.now(),
            sessionId: this.sessionId
        };

        console.error(`🚨 错误告警 [${type}]:`, data);

        // 发送告警通知
        this.sendAlert(alert);
    }

    // 发送告警
    async sendAlert(alert) {
        try {
            await fetch('/api/alerts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(alert)
            });
        } catch (error) {
            console.error('告警发送失败:', error);
        }
    }

    // 报告错误
    async reportError(errorInfo) {
        try {
            await fetch(this.config.reportEndpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(errorInfo)
            });
        } catch (error) {
            console.error('错误报告发送失败:', error);
            // 存储到本地，稍后重试
            this.storeForRetry(errorInfo);
        }
    }

    // 控制台日志输出
    logError(errorInfo) {
        const severity = this.getSeverityName(errorInfo.severity);
        const emoji = this.getSeverityEmoji(errorInfo.severity);
        
        console.group(`${emoji} 错误监控 [${severity}] ${errorInfo.type}`);
        console.error('消息:', errorInfo.message);
        if (errorInfo.filename) {
            console.error('文件:', `${errorInfo.filename}:${errorInfo.lineno}:${errorInfo.colno}`);
        }
        if (errorInfo.stack) {
            console.error('堆栈:', errorInfo.stack);
        }
        console.error('时间:', new Date(errorInfo.timestamp).toLocaleString());
        console.error('会话:', errorInfo.sessionId);
        console.groupEnd();
    }

    // 工具方法
    shouldIgnoreError(errorInfo) {
        return this.config.ignoredErrors.some(ignored => 
            errorInfo.message.includes(ignored)
        );
    }

    getRecentErrors(timeWindow) {
        const cutoff = Date.now() - timeWindow;
        return this.errors.filter(error => error.timestamp >= cutoff);
    }

    estimateOperations() {
        // 估算操作数量（基于性能监控数据）
        if (window.performanceMonitor) {
            return window.performanceMonitor.getMetrics().length;
        }
        return Math.max(100, this.errors.length * 10); // 保守估计
    }

    getSeverityName(severity) {
        const names = ['UNKNOWN', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
        return names[severity] || 'UNKNOWN';
    }

    getSeverityEmoji(severity) {
        const emojis = ['ℹ️', '⚠️', '⚠️', '🔥', '💥'];
        return emojis[severity] || 'ℹ️';
    }

    storeForRetry(errorInfo) {
        try {
            const stored = JSON.parse(localStorage.getItem('error_retry_queue') || '[]');
            stored.push(errorInfo);
            localStorage.setItem('error_retry_queue', JSON.stringify(stored.slice(-50))); // 最多50个
        } catch (e) {
            console.error('无法存储错误重试队列:', e);
        }
    }

    generateSessionId() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    }

    // 公共API
    getErrors() {
        return this.errors;
    }

    getErrorCounts() {
        return Object.fromEntries(this.errorCounts);
    }

    getErrorSummary() {
        const summary = {
            total: this.errors.length,
            byType: {},
            bySeverity: {},
            recentCount: this.getRecentErrors(300000).length // 最近5分钟
        };

        this.errors.forEach(error => {
            // 按类型统计
            if (!summary.byType[error.type]) {
                summary.byType[error.type] = 0;
            }
            summary.byType[error.type]++;

            // 按严重程度统计
            const severity = this.getSeverityName(error.severity);
            if (!summary.bySeverity[severity]) {
                summary.bySeverity[severity] = 0;
            }
            summary.bySeverity[severity]++;
        });

        return summary;
    }
}

// 自动启动错误监控
if (typeof window !== 'undefined') {
    window.ErrorMonitor = ErrorMonitor;
    window.errorMonitor = new ErrorMonitor();
}

export default ErrorMonitor;