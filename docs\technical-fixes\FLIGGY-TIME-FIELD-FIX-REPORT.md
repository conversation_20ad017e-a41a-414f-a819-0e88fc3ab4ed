# Fliggy Time Field Fix Report

## 问题描述
**发现时间：** 2025年8月14日 22:22  
**问题类型：** 数据提取准确性问题  
**影响程度：** 中等 - 影响时间字段的正确填充

### 具体现象
在处理 Fliggy 订单时，发现以下异常：
- ✅ `arrival_time` 字段正确提取了 "19:05"
- ❌ `time` 字段返回 `null`，但应该设置为 "19:05"

### 问题订单示例
```
订单编号：4684304919901740648
买家：嗷嗷嗷婕_宝
【接机】
新加坡-新加坡
[出发]樟宜机场T4
[抵达]新加坡美丽华大酒店
CX635
[预计抵达]
2025-08-15 19:05:00
陈婕
```

### Gemini API 响应分析
```json
{
  "arrival_time": "19:05",     // ✅ 正确
  "time": null,                // ❌ 应该是 "19:05"
  "flight_type": "Arrival",    // ✅ 正确识别为接机
  "sub_category_id": 2         // ✅ 正确识别为接机服务
}
```

## 根因分析

### 1. 提示词缺失
- **Fliggy 策略**：`js/ota-strategies.js` 中的 `FliggyOTAStrategy.getFieldPromptSnippets()` 没有包含对 `time` 字段的处理指导
- **通用提示词**：`js/flow/prompt-builder.js` 中的时间处理规则不够明确

### 2. 逻辑漏洞
- Gemini 能正确提取 `arrival_time` 和 `departure_time`
- 但不知道如何根据订单类型智能设置 `time` 字段
- 缺少接机订单 `time` = `arrival_time` 的逻辑指导

## 解决方案

### 修复 1：强化 Fliggy 策略提示词

**文件：** `js/ota-strategies.js`

**修改内容：**
```javascript
static getFieldPromptSnippets(_ctx = {}) {
    return {
        // ... 其他字段 ...
        
        // ✅ 新增：时间字段逻辑
        time: 'Fliggy时间字段：对于接机订单，time字段应设置为arrival_time的值；对于送机订单，time字段应设置为departure_time的值；对于包车订单，time字段应设置为开始时间。请根据订单类型（接机/送机/包车）智能设置time字段，格式为HH:MM（24小时制）。',
        
        // ... 其他字段 ...
    };
}
```

### 修复 2：强化通用提示词

**文件：** `js/flow/prompt-builder.js`

**修改内容：**
```javascript
【时间与日期格式化】
- pickup_date 使用 YYYY-MM-DD；**必须从订单文本中提取具体日期**...
- 若文本包含如"2025-08-11"等完整日期格式，直接使用...
- pickup_time 使用 24h HH:MM；支持"上午/下午/AM/PM"等格式转换。
- **time字段重要规则：对于接机订单，time字段必须设置为arrival_time的值；对于送机订单，time字段必须设置为departure_time的值；对于包车订单，time字段设置为服务开始时间。time字段不能为null，必须根据订单类型智能设置。**
```

## 修复逻辑

### 订单类型与时间字段映射
| 订单类型 | 识别特征 | time 字段来源 | 示例 |
|---------|---------|--------------|------|
| 接机 | 【接机】、从机场、airport pickup、flight_type='Arrival' | `arrival_time` | "19:05" |
| 送机 | 【送机】、到机场、airport dropoff、flight_type='Departure' | `departure_time` | "22:30" |
| 包车 | 【包车】、charter、全天游、day tour | 服务开始时间 | "09:00" |

### 智能设置规则
1. **接机订单**：`time` = `arrival_time`
2. **送机订单**：`time` = `departure_time`  
3. **包车订单**：`time` = 服务开始时间
4. **格式统一**：24小时制 HH:MM

## 预期效果

### 修复前
```json
{
  "arrival_time": "19:05",
  "time": null,              // ❌ 问题
  "flight_type": "Arrival"
}
```

### 修复后
```json
{
  "arrival_time": "19:05",
  "time": "19:05",           // ✅ 修复
  "flight_type": "Arrival"
}
```

## 测试验证

### 测试用例 1：Fliggy 接机订单
**输入：**
```
订单编号：4684304919901740648
【接机】
CX635
[预计抵达] 2025-08-15 19:05:00
```

**期望输出：**
```json
{
  "arrival_time": "19:05",
  "time": "19:05",
  "flight_type": "Arrival",
  "sub_category_id": 2
}
```

### 测试用例 2：Fliggy 送机订单
**输入：**
```
订单编号：1234567890123456789
【送机】
MH123
[起飞时间] 2025-08-16 22:30:00
```

**期望输出：**
```json
{
  "departure_time": "22:30",
  "time": "22:30",
  "flight_type": "Departure",
  "sub_category_id": 3
}
```

### 测试用例 3：Fliggy 包车订单
**输入：**
```
订单编号：9876543210987654321
【包车】全天游
服务时间：上午9:00开始
```

**期望输出：**
```json
{
  "time": "09:00",
  "sub_category_id": 4
}
```

## 影响范围

### 受益渠道
- ✅ **Fliggy（飞猪）**：直接修复
- ✅ **其他 OTA 渠道**：通过通用提示词强化也会受益

### 兼容性
- ✅ **向后兼容**：不影响现有功能
- ✅ **其他字段**：不影响其他字段的提取
- ✅ **性能**：不增加额外计算开销

## 部署建议

### 立即部署
1. **风险等级**：低风险
2. **测试要求**：建议先在测试环境验证
3. **回滚方案**：如有问题可快速回滚到修复前版本

### 监控指标
- **time 字段非空率**：应该从当前的部分为空提升到接近100%
- **接机订单 time 字段准确性**：应该等于 arrival_time
- **送机订单 time 字段准确性**：应该等于 departure_time

## 长期改进建议

### 1. 自动化测试
- 添加针对时间字段的单元测试
- 覆盖接机、送机、包车三种场景

### 2. 提示词优化
- 定期审查各渠道策略的提示词完整性
- 建立提示词质量检查清单

### 3. 字段映射验证
- 增强字段映射验证器，检查时间字段的逻辑一致性
- 添加运行时警告，当 time 字段为空但有相关时间信息时提醒

---

**修复状态：** ✅ 已完成  
**测试文件：** `test-fliggy-time-field-fix.html`  
**下次验证：** 重新测试相同的 Fliggy 订单，观察 time 字段是否正确设置为 "19:05"
