/**
 * 兼容性桥接 - <PERSON><PERSON>ds式重构版本
 * 
 * "Never break userspace" - 保持向后兼容性
 * 
 * 这个文件提供最小化的兼容性支持，替代那些臃肿的适配器
 * 让现有代码继续工作，但不再依赖适配器废话
 */

'use strict';

// 为了兼容性，提供最小化的适配器接口
window.OTA = window.OTA || {};
window.OTA.adapters = window.OTA.adapters || {};

// 替代 BaseManagerAdapter
window.BaseManager = class BaseManager {
    constructor(name) {
        this.name = name || 'UnknownManager';
        this.initialized = false;
    }
    
    async init() {
        this.initialized = true;
        return true;
    }
    
    log(message, level = 'info') {
        console.log(`[${this.name}] ${message}`);
    }
    
    logError(message, error) {
        console.error(`[${this.name}] ${message}`, error);
    }
};

// 替代 GeminiServiceAdapter - 直接使用核心功能
window.OTA.adapters.GeminiServiceAdapter = {
    async parseOrder(text, channel = null) {
        return window.ota.gemini.parseOrder(text, channel);
    },
    
    async analyzeImage(base64Image) {
        return window.ota.gemini.analyzeImage(base64Image);
    },
    
    async detectAndSplitMultiOrdersWithVerification(text, options = {}) {
        return window.ota.gemini.detectAndSplitMultiOrdersWithVerification(text, options);
    },
    
    // 字段标准化 - 简化版本
    standardizeFields(input) {
        const mapping = {
            customerName: 'customer_name',
            customerContact: 'customer_contact', 
            customerEmail: 'customer_email',
            pickupDate: 'date',
            pickupTime: 'time',
            pickup: 'pickup',
            dropoff: 'destination',
            passengerCount: 'passenger_number',
            luggageCount: 'luggage_number',
            otaReferenceNumber: 'ota_reference_number',
            flightInfo: 'flight_info'
        };
        
        const result = {};
        
        Object.entries(input).forEach(([key, value]) => {
            const mappedKey = mapping[key] || key;
            result[mappedKey] = value;
        });
        
        return result;
    }
};

// 替代 UIManagerAdapter - 直接使用简化UI
window.OTA.adapters.UIManagerAdapter = {
    fillForm(data) {
        window.ota.ui.fillOrderForm(data);
    },
    
    showMultiOrder(orders, text) {
        window.ota.ui.showMultiOrderMode(orders, text);
    },
    
    resetForm() {
        window.ota.ui.enhanced.resetForm();
    }
};

// 替代 MultiOrderManagerAdapter
window.OTA.adapters.MultiOrderManagerAdapter = {
    async processMultiOrder(orders) {
        return window.ota.ui.batchCreateOrders(orders);
    },
    
    showMultiOrderInterface(data) {
        window.ota.ui.showMultiOrderMode(data.orders, data.originalText);
    }
};

// 为了完全兼容性，映射一些常用的全局函数
window.getLogger = function() {
    return {
        log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
        logError: (message, error) => console.error(`[ERROR] ${message}`, error)
    };
};

// 兼容旧的服务定位器调用
window.OTA.container = {
    get(serviceName) {
        const serviceMap = {
            'apiService': window.ota.api,
            'geminiService': window.ota.gemini,
            'uiManager': window.ota.ui,
            'channelDetector': window.ota.channelDetector
        };
        
        return serviceMap[serviceName] || null;
    }
};

// 兼容旧的事件系统 - 最小化实现
window.OTA.eventCoordinator = {
    listeners: new Map(),
    
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    },
    
    emit(event, data) {
        const callbacks = this.listeners.get(event) || [];
        callbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`事件处理器错误 [${event}]:`, error);
            }
        });
    }
};

console.log('✅ 兼容性桥接已加载 - 替代适配器废话');