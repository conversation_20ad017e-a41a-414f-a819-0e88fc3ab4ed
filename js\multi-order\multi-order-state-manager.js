// build-stamp: version 2.0.3 (multi-order-state-manager)
/**
 * 依赖标签（Dependency Tags）
 * 文件: js/multi-order/state-manager.js
 * 角色: 多订单状态管理（内存+localStorage；批次/订单级状态、进度、错误）
 * 上游依赖(直接使用):
 *  - <PERSON><PERSON>(getLogger)
 *  - AppState（或本地持久化工具，取决于实现）
 * 下游被依赖(常见调用方):
 *  - MultiOrderCoordinator / Renderer / Processor / BatchProcessor
 * 事件: 通知订阅者状态变化（observer/callback）；渲染器监听刷新
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_STATE_MANAGER 多订单状态管理器
 * 🏷️ 标签: @OTA_MULTI_ORDER_STATE_MANAGER
 * 📝 说明: 负责多订单系统的状态管理、持久化和同步
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderStateManager) {
    console.log('多订单状态管理器已存在，跳过重复加载');
} else {

/**
 * 多订单状态管理器类
 */
class MultiOrderStateManager {
    constructor(config = {}) {
        this.config = {
            enablePersistence: true, // 启用状态持久化
            storageKey: 'otaMultiOrderState',
            autoSave: true, // 自动保存
            saveInterval: 30000, // 自动保存间隔（毫秒）- 优化：减少频繁保存
            maxHistorySize: 10, // 最大历史记录数
            ...config
        };

        // 核心状态
        this.state = {
            // 多订单模式状态
            isMultiOrderMode: false,
            currentSession: null,
            
            // 输入和检测
            currentInput: '',
            lastAnalysisTime: null,
            detectionHistory: [],
            
            // 订单数据
            parsedOrders: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            
            // 批量处理状态
            batchProcessing: {
                isRunning: false,
                currentBatch: null,
                progress: {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    processing: 0
                },
                startTime: null,
                results: []
            },
            
            // UI状态
            ui: {
                panelVisible: false,
                selectedTab: 'orders',
                sortBy: 'index',
                filterBy: 'all'
            },

            // 批量设置状态
            batchSettings: {
                languageId: null,
                otaChannel: null,
                lastUpdated: null
            },

            // 统计信息
            statistics: {
                totalOrdersProcessed: 0,
                successfulBatches: 0,
                failedBatches: 0,
                averageProcessingTime: 0
            }
        };

        // 状态变更监听器
        this.listeners = new Map();
        this.stateHistory = [];
        this.saveTimer = null;
        
        this.logger = this.getLogger();
        this.initializeStateManager();
    }

    /**
     * 初始化状态管理器
     */
    initializeStateManager() {
        try {
            // 加载持久化状态
            if (this.config.enablePersistence) {
                this.loadPersistedState();
            }

            // 设置自动保存
            if (this.config.autoSave) {
                this.startAutoSave();
            }

            // 监听页面卸载事件
            window.addEventListener('beforeunload', () => {
                this.saveState();
            });

            this.logger.log('多订单状态管理器初始化完成', 'info');

        } catch (error) {
            this.logger.log('状态管理器初始化失败', 'error', { error: error.message });
        }
    }

    /**
     * 更新状态
     * @param {object} updates - 状态更新
     * @param {boolean} notifyListeners - 是否通知监听器
     * @returns {object} 新状态
     */
    updateState(updates, notifyListeners = true) {
        try {
            // 保存当前状态到历史
            this.saveToHistory();

            // 深度合并状态更新
            const newState = this.deepMerge(this.state, updates);
            this.state = newState;

            // 更新时间戳
            this.state.lastUpdated = Date.now();

            // 通知监听器
            if (notifyListeners) {
                this.notifyListeners('stateUpdated', { updates, newState });
            }

            // 触发自动保存
            if (this.config.autoSave) {
                this.scheduleAutoSave();
            }

            this.logger.log('状态更新完成', 'debug', { updates });
            return this.state;

        } catch (error) {
            this.logger.log('状态更新失败', 'error', { error: error.message, updates });
            throw error;
        }
    }

    /**
     * 获取当前状态
     * @param {string} path - 状态路径（可选）
     * @returns {any} 状态值
     */
    getState(path = null) {
        if (path) {
            return this.getNestedValue(this.state, path);
        }
        return { ...this.state };
    }

    /**
     * 重置状态
     * @param {boolean} keepStatistics - 是否保留统计信息
     */
    resetState(keepStatistics = true) {
        const statisticsBackup = keepStatistics ? { ...this.state.statistics } : {};
        
        this.state = {
            isMultiOrderMode: false,
            currentSession: null,
            currentInput: '',
            lastAnalysisTime: null,
            detectionHistory: [],
            parsedOrders: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            batchProcessing: {
                isRunning: false,
                currentBatch: null,
                progress: { total: 0, completed: 0, failed: 0, processing: 0 },
                startTime: null,
                results: []
            },
            ui: {
                panelVisible: false,
                selectedTab: 'orders',
                sortBy: 'index',
                filterBy: 'all'
            },
            statistics: statisticsBackup
        };

        this.notifyListeners('stateReset', { keepStatistics });
        this.logger.log('状态已重置', 'info');
    }

    /**
     * 启动新的多订单会话
     * @param {object} sessionData - 会话数据
     * @returns {string} 会话ID
     */
    startSession(sessionData = {}) {
        const sessionId = this.generateSessionId();
        const session = {
            id: sessionId,
            startTime: Date.now(),
            ...sessionData
        };

        this.updateState({
            isMultiOrderMode: true,
            currentSession: session,
            ui: { panelVisible: true }
        });

        this.logger.log('新的多订单会话已启动', 'info', { sessionId });
        return sessionId;
    }

    /**
     * 结束当前会话
     */
    endSession() {
        if (this.state.currentSession) {
            const session = this.state.currentSession;
            session.endTime = Date.now();
            session.duration = session.endTime - session.startTime;

            // 更新统计信息
            this.updateStatistics(session);

            this.updateState({
                isMultiOrderMode: false,
                currentSession: null,
                ui: { panelVisible: false }
            });

            this.logger.log('多订单会话已结束', 'info', { 
                sessionId: session.id,
                duration: session.duration 
            });
        }
    }

    /**
     * 更新订单数据
     * @param {array} orders - 订单列表
     */
    updateOrders(orders) {
        this.updateState({
            parsedOrders: orders,
            selectedOrders: new Set() // 重置选择
        });
    }

    /**
     * 更新订单选择
     * @param {Set} selectedIndexes - 选中的订单索引
     */
    updateOrderSelection(selectedIndexes) {
        this.updateState({
            selectedOrders: new Set(selectedIndexes)
        });
    }

    /**
     * 更新批量处理状态
     * @param {object} batchUpdate - 批量处理状态更新
     */
    updateBatchProcessing(batchUpdate) {
        this.updateState({
            batchProcessing: {
                ...this.state.batchProcessing,
                ...batchUpdate
            }
        });
    }

    /**
     * 更新UI状态
     * @param {object} uiUpdate - UI状态更新
     */
    updateUI(uiUpdate) {
        this.updateState({
            ui: {
                ...this.state.ui,
                ...uiUpdate
            }
        });
    }

    /**
     * 更新批量设置
     * @param {object} batchUpdate - 批量设置更新
     */
    updateBatchSettings(batchUpdate) {
        this.updateState({
            batchSettings: {
                ...this.state.batchSettings,
                ...batchUpdate,
                lastUpdated: Date.now()
            }
        });

        // 触发批量设置变更事件
        this.notifyListeners('batchSettingsChanged', {
            batchSettings: this.state.batchSettings,
            changes: batchUpdate
        });
    }

    /**
     * 获取批量设置
     * @returns {object} 批量设置
     */
    getBatchSettings() {
        return { ...this.state.batchSettings };
    }

    /**
     * 重置批量设置
     */
    resetBatchSettings() {
        this.updateState({
            batchSettings: {
                languageId: null,
                otaChannel: null,
                lastUpdated: Date.now()
            }
        });
    }

    /**
     * 添加检测历史记录
     * @param {object} detectionResult - 检测结果
     */
    addDetectionHistory(detectionResult) {
        const history = [...this.state.detectionHistory];
        history.unshift({
            ...detectionResult,
            timestamp: Date.now()
        });

        // 限制历史记录数量
        if (history.length > this.config.maxHistorySize) {
            history.splice(this.config.maxHistorySize);
        }

        this.updateState({
            detectionHistory: history,
            lastAnalysisTime: Date.now()
        });
    }

    /**
     * 更新统计信息
     * @param {object} sessionData - 会话数据
     */
    updateStatistics(sessionData) {
        const stats = { ...this.state.statistics };
        
        if (sessionData.ordersProcessed) {
            stats.totalOrdersProcessed += sessionData.ordersProcessed;
        }
        
        if (sessionData.batchSuccessful) {
            stats.successfulBatches++;
        } else if (sessionData.batchFailed) {
            stats.failedBatches++;
        }
        
        if (sessionData.duration) {
            // 计算平均处理时间
            const totalSessions = stats.successfulBatches + stats.failedBatches;
            if (totalSessions > 0) {
                stats.averageProcessingTime = 
                    (stats.averageProcessingTime * (totalSessions - 1) + sessionData.duration) / totalSessions;
            }
        }

        this.updateState({ statistics: stats });
    }

    /**
     * 获取统计信息
     * @returns {object} 统计信息
     */
    getStatistics() {
        return { ...this.state.statistics };
    }

    /**
     * 保存状态到历史
     */
    saveToHistory() {
        const snapshot = {
            state: JSON.parse(JSON.stringify(this.state)),
            timestamp: Date.now()
        };

        this.stateHistory.unshift(snapshot);
        
        // 限制历史记录数量
        if (this.stateHistory.length > this.config.maxHistorySize) {
            this.stateHistory.splice(this.config.maxHistorySize);
        }
    }

    /**
     * 恢复历史状态
     * @param {number} index - 历史记录索引
     * @returns {boolean} 是否成功
     */
    restoreFromHistory(index = 0) {
        if (index >= 0 && index < this.stateHistory.length) {
            const snapshot = this.stateHistory[index];
            this.state = snapshot.state;
            this.notifyListeners('stateRestored', { index, timestamp: snapshot.timestamp });
            this.logger.log('状态已从历史恢复', 'info', { index });
            return true;
        }
        return false;
    }

    /**
     * 保存状态到本地存储
     */
    saveState() {
        if (!this.config.enablePersistence) return;

        try {
            const stateToSave = {
                ...this.state,
                // 不保存敏感或临时数据
                selectedOrders: Array.from(this.state.selectedOrders),
                processedOrders: Array.from(this.state.processedOrders.entries())
            };

            localStorage.setItem(this.config.storageKey, JSON.stringify(stateToSave));
            // 优化：减少日志噪音，仅在开发模式下输出
            // this.logger.log('状态已保存到本地存储', 'debug');

        } catch (error) {
            this.logger.log('状态保存失败', 'error', { error: error.message });
        }
    }

    /**
     * 从本地存储加载状态
     */
    loadPersistedState() {
        try {
            const savedState = localStorage.getItem(this.config.storageKey);
            if (savedState) {
                const parsedState = JSON.parse(savedState);
                
                // 恢复特殊数据结构
                if (parsedState.selectedOrders) {
                    parsedState.selectedOrders = new Set(parsedState.selectedOrders);
                }
                if (parsedState.processedOrders) {
                    parsedState.processedOrders = new Map(parsedState.processedOrders);
                }

                this.state = { ...this.state, ...parsedState };
                this.logger.log('状态已从本地存储加载', 'info');
            }

        } catch (error) {
            this.logger.log('状态加载失败', 'error', { error: error.message });
        }
    }

    /**
     * 开始自动保存
     */
    startAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
        }

        this.saveTimer = setInterval(() => {
            this.saveState();
        }, this.config.saveInterval);
    }

    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
            this.saveTimer = null;
        }
    }

    /**
     * 计划自动保存
     */
    scheduleAutoSave() {
        if (this.scheduledSave) {
            clearTimeout(this.scheduledSave);
        }

        this.scheduledSave = setTimeout(() => {
            this.saveState();
        }, 5000); // 5秒后保存 - 优化：减少频繁保存
    }

    /**
     * 添加状态监听器
     * @param {string} event - 事件名称
     * @param {function} listener - 监听器函数
     */
    addListener(event, listener) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(listener);
    }

    /**
     * 移除状态监听器
     * @param {string} event - 事件名称
     * @param {function} listener - 监听器函数
     */
    removeListener(event, listener) {
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 通知监听器
     * @param {string} event - 事件名称
     * @param {object} data - 事件数据
     */
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    this.logger.log(`监听器执行失败: ${event}`, 'error', { error: error.message });
                }
            });
        }
    }

    /**
     * 生成会话ID
     * @returns {string} 会话ID
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 深度合并对象
     * @param {object} target - 目标对象
     * @param {object} source - 源对象
     * @returns {object} 合并后的对象
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    /**
     * 获取嵌套值
     * @param {object} obj - 对象
     * @param {string} path - 路径
     * @returns {any} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 获取Logger实例
     * @returns {object} Logger实例
     */
    getLogger() {
        if (typeof getLogger === 'function') {
            return getLogger();
        }
        return {
            log: (message, level, data) => {
                console.log(`[STATE][${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
            }
        };
    }

    /**
     * 销毁状态管理器
     */
    destroy() {
        this.stopAutoSave();
        this.saveState();
        this.listeners.clear();
        this.stateHistory = [];
        this.logger.log('状态管理器已销毁', 'info');
    }
}

// 创建全局实例
const multiOrderStateManager = new MultiOrderStateManager();

// 暴露到OTA命名空间
window.OTA = window.OTA || {};
window.OTA.MultiOrderStateManager = MultiOrderStateManager;
window.OTA.multiOrderStateManager = multiOrderStateManager;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('multiOrderStateManager', multiOrderStateManager, '@OTA_MULTI_ORDER_STATE_MANAGER');
}

}