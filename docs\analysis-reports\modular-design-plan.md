# JavaScript代码库模块化设计方案

## 🎯 设计原则

### 单一职责原则（SRP）
- 每个模块只负责一个明确的功能领域
- 文件大小控制在500行以内
- 避免功能混合和职责重叠

### 依赖倒置原则（DIP）
- 高层模块不依赖低层模块，都依赖抽象
- 使用统一的服务注册中心管理依赖
- 通过接口而非具体实现进行交互

### 开闭原则（OCP）
- 对扩展开放，对修改封闭
- 使用策略模式和工厂模式支持功能扩展
- 通过配置而非代码修改来改变行为

## 📁 新的目录结构设计

```
js/
├── core/                          # 核心基础设施（简化后）
│   ├── container.js               # 依赖注入容器
│   ├── service-registry.js        # 统一服务注册中心
│   ├── application-bootstrap.js   # 应用启动器
│   └── event-coordinator.js       # 全局事件协调
├── services/                      # 业务服务层
│   ├── api/
│   │   ├── api-client.js          # API客户端基础
│   │   ├── auth-service.js        # 认证服务
│   │   └── order-api.js           # 订单API
│   ├── ai/
│   │   ├── gemini-client.js       # Gemini API客户端
│   │   ├── order-parser.js        # 订单解析器
│   │   ├── image-analyzer.js      # 图像分析器
│   │   └── knowledge-base.js      # 知识库管理
│   ├── data/
│   │   ├── app-state.js           # 应用状态管理
│   │   ├── cache-manager.js       # 缓存管理
│   │   └── storage-service.js     # 存储服务
│   └── external/
│       ├── flight-info.js         # 航班信息服务
│       ├── currency-converter.js  # 货币转换
│       └── address-translator.js  # 地址翻译
├── managers/                      # 业务管理层（重新设计）
│   ├── ui/
│   │   ├── dom-manager.js         # DOM操作管理
│   │   ├── modal-manager.js       # 模态框管理
│   │   └── theme-manager.js       # 主题管理
│   ├── order/
│   │   ├── single-order-manager.js    # 单订单管理
│   │   ├── multi-order-coordinator.js # 多订单协调
│   │   └── order-history-manager.js   # 订单历史
│   └── system/
│       ├── language-manager.js    # 语言管理
│       ├── config-manager.js      # 配置管理
│       └── monitoring-manager.js  # 监控管理
├── components/                    # UI组件层
│   ├── multi-order/
│   │   ├── detector.js            # 多订单检测
│   │   ├── processor.js           # 订单处理
│   │   ├── renderer.js            # UI渲染
│   │   ├── state-manager.js       # 状态管理
│   │   └── batch-processor.js     # 批处理
│   ├── forms/
│   │   ├── form-validator.js      # 表单验证
│   │   ├── field-mapper.js        # 字段映射
│   │   └── auto-complete.js       # 自动完成
│   └── common/
│       ├── dropdown.js            # 下拉组件
│       ├── modal.js               # 模态框组件
│       └── notification.js        # 通知组件
├── strategies/                    # 策略模式实现
│   ├── ota/
│   │   ├── base-strategy.js       # 基础策略
│   │   ├── fliggy-strategy.js     # 飞猪策略
│   │   └── jingge-strategy.js     # 京格策略
│   └── parsing/
│       ├── text-parser.js         # 文本解析策略
│       └── image-parser.js        # 图像解析策略
├── utils/                         # 工具函数库
│   ├── common.js                  # 通用工具
│   ├── date.js                    # 日期工具
│   ├── validation.js              # 验证工具
│   └── performance.js             # 性能工具
├── config/                        # 配置文件
│   ├── constants.js               # 常量定义
│   ├── api-endpoints.js           # API端点配置
│   └── feature-flags.js           # 功能开关
└── main.js                        # 应用入口
```

## 🏷️ 统一命名规范

### 文件命名
- 使用kebab-case：`multi-order-detector.js`
- 功能描述性：`order-history-manager.js`
- 避免缩写：`configuration.js` 而非 `config.js`

### 类命名
- 使用PascalCase：`MultiOrderDetector`
- 功能后缀：`OrderParser`, `ApiClient`, `StateManager`

### 函数命名
- 使用camelCase：`parseOrderText`
- 动词开头：`createOrder`, `validateData`, `renderComponent`

### 常量命名
- 使用SCREAMING_SNAKE_CASE：`MAX_ORDER_COUNT`
- 分组前缀：`API_ENDPOINTS`, `ERROR_CODES`

## 🔗 依赖关系设计

### 层级依赖规则
```
main.js
    ↓
managers/ (业务管理层)
    ↓
components/ (UI组件层)
    ↓
services/ (业务服务层)
    ↓
utils/ + config/ (基础工具层)
    ↓
core/ (核心基础设施)
```

### 服务注册模式
```javascript
// 统一的服务注册方式
ServiceRegistry.register('orderParser', OrderParser, {
    dependencies: ['geminiClient', 'knowledgeBase'],
    singleton: true,
    tags: ['@AI_SERVICE', '@ORDER_PROCESSING']
});

// 统一的服务获取方式
const orderParser = ServiceRegistry.get('orderParser');
```

### 事件通信模式
```javascript
// 统一的事件发布订阅
EventCoordinator.publish('order.parsed', { orderId, data });
EventCoordinator.subscribe('order.parsed', handleOrderParsed);
```

## 📋 模块拆分计划

### 1. gemini-service.js (4761行) → 4个模块

#### gemini-client.js (~800行)
- API调用逻辑
- 错误处理和重试
- 速率限制管理

#### order-parser.js (~1200行)
- 订单文本解析
- 多订单检测
- 解析结果验证

#### image-analyzer.js (~600行)
- 图像识别
- OCR处理
- 图像预处理

#### knowledge-base.js (~400行)
- 酒店知识库
- 机场翻译
- 数据缓存

### 2. multi-order-manager-v2.js (2839行) → 3个模块

#### multi-order-coordinator.js (~800行)
- 业务逻辑协调
- 状态管理
- 流程控制

#### multi-order-ui-controller.js (~1000行)
- UI交互处理
- DOM操作
- 事件绑定

#### multi-order-state.js (~500行)
- 状态持久化
- 数据同步
- 缓存管理

### 3. ui-manager.js (980行) → 2个模块

#### dom-manager.js (~500行)
- DOM元素管理
- 基础UI操作
- 元素缓存

#### ui-coordinator.js (~480行)
- UI状态协调
- 组件通信
- 生命周期管理

## 🔧 重构实施策略

### 阶段1：基础设施重构
1. 创建新的服务注册中心
2. 统一依赖注入机制
3. 建立事件通信系统

### 阶段2：服务层重构
1. 拆分gemini-service.js
2. 重构API服务
3. 优化状态管理

### 阶段3：管理层重构
1. 拆分multi-order-manager-v2.js
2. 重新设计UI管理器
3. 优化组件通信

### 阶段4：清理优化
1. 删除重复代码
2. 简化core目录
3. 优化依赖关系

## ✅ 成功标准

- 单个文件不超过500行
- 消除所有重复的服务获取函数
- 依赖关系清晰，无循环依赖
- 功能职责明确，易于测试和维护
- 保持向后兼容性

---

*设计方案版本: 1.0*
*创建时间: 2025-08-09*
