/**
 * 多订单页面控制器 V2
 * 文件: js/pages/multi-order/multi-order-page-v2.js
 * 角色: 多订单页面的主控制器，管理页面生命周期和组件协调
 * 
 * @MULTI_ORDER_PAGE 多订单页面控制器
 * 🏷️ 标签: @OTA_MULTI_ORDER_PAGE_V2
 * 📝 说明: 新的独立多订单页面架构，整合所有服务和组件
 * <AUTHOR>
 * @version 2.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.Pages = window.OTA.Pages || {};

(function() {
    'use strict';

    /**
     * 多订单页面类
     * 管理多订单页面的所有功能
     */
    class MultiOrderPageV2 {
        constructor() {
            this.pageId = 'multi-order';
            this.isInitialized = false;
            this.isActive = false;
            
            // 页面元素
            this.elements = {
                container: null,
                panel: null,
                ordersList: null,
                batchControlsContainer: null,
                progressContainer: null,
                statusContainer: null
            };
            
            // 服务实例
            this.services = {
                stateManager: null,
                orderDetector: null,
                orderProcessor: null,
                batchManager: null,
                apiClient: null
            };

            // UI组件
            this.components = {
                orderCards: [],
                batchControls: null,
                progressIndicator: null,
                statusPanel: null
            };
            
            this.logger = this.getLogger();
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务实例
         */
        getLogger() {
            return window.getLogger?.() || {
                log: (message, level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),
                logError: (message, error) => console.error(`[ERROR] ${message}`, error)
            };
        }

        /**
         * 初始化页面
         */
        async initialize() {
            if (this.isInitialized) {
                this.logger.log('多订单页面已初始化，跳过重复初始化', 'warning');
                return;
            }

            try {
                this.logger.log('🚀 初始化多订单页面V2', 'info');

                // 初始化服务
                await this.initializeServices();

                // 创建页面容器
                this.createPageContainer();

                // 初始化页面内容
                this.initializePageContent();

                // 绑定事件
                this.bindEvents();

                this.isInitialized = true;
                this.logger.log('✅ 多订单页面V2初始化完成', 'success');

            } catch (error) {
                this.logger.logError('多订单页面V2初始化失败', error);
                throw error;
            }
        }

        /**
         * 初始化服务
         */
        async initializeServices() {
            try {
                // 初始化状态管理器
                this.services.stateManager = window.OTA?.Services?.stateManager ||
                                           new window.OTA.Services.StateManager();

                // 初始化其他服务
                this.services.orderDetector = window.OTA?.Services?.orderDetector;
                this.services.orderProcessor = window.OTA?.Services?.orderProcessor;
                this.services.batchManager = window.OTA?.Services?.batchManager;
                this.services.apiClient = window.OTA?.Services?.apiClient;

                // 初始化错误处理器
                this.initializeErrorHandler();

                // 绑定状态管理器事件
                this.bindStateManagerEvents();

                this.logger.log('🔧 服务初始化完成', 'success');

            } catch (error) {
                this.logger.logError('服务初始化失败', error);
                throw error;
            }
        }

        /**
         * 初始化错误处理器
         */
        initializeErrorHandler() {
            try {
                // 创建错误处理器
                this.errorHandler = {
                    // 错误计数器
                    errorCounts: new Map(),

                    // 最大错误次数
                    maxErrors: 5,

                    // 错误恢复策略
                    recoveryStrategies: new Map([
                        ['network', this.handleNetworkError.bind(this)],
                        ['api', this.handleApiError.bind(this)],
                        ['validation', this.handleValidationError.bind(this)],
                        ['timeout', this.handleTimeoutError.bind(this)],
                        ['unknown', this.handleUnknownError.bind(this)]
                    ]),

                    // 处理错误
                    handleError: (error, context = {}) => {
                        this.handleGlobalError(error, context);
                    }
                };

                this.logger.log('🛡️ 错误处理器初始化完成', 'success');

            } catch (error) {
                this.logger.logError('错误处理器初始化失败', error);
            }
        }

        /**
         * 绑定状态管理器事件
         */
        bindStateManagerEvents() {
            if (!this.services.stateManager) return;

            // 监听订单选择变化
            this.services.stateManager.addListener('orderSelectionChanged', (data) => {
                this.handleOrderSelectionChanged(data);
            });

            // 监听处理开始
            this.services.stateManager.addListener('processingStarted', (data) => {
                this.handleProcessingStarted(data);
            });

            // 监听进度更新
            this.services.stateManager.addListener('progressUpdated', (data) => {
                this.handleProgressUpdated(data);
            });

            // 监听处理完成
            this.services.stateManager.addListener('processingCompleted', (data) => {
                this.handleProcessingCompleted(data);
            });

            // 监听状态重置
            this.services.stateManager.addListener('stateReset', () => {
                this.handleStateReset();
            });
        }

        /**
         * 创建页面容器
         */
        createPageContainer() {
            // 查找或创建页面容器
            this.elements.container = document.getElementById('multi-order-page');
            
            if (!this.elements.container) {
                this.elements.container = document.createElement('div');
                this.elements.container.id = 'multi-order-page';
                this.elements.container.className = 'page-container hidden';
                document.body.appendChild(this.elements.container);
            }

            // 清空现有内容
            this.elements.container.innerHTML = '';

            // 创建页面面板
            this.elements.panel = document.createElement('div');
            this.elements.panel.className = 'multi-order-panel';
            this.elements.panel.innerHTML = this.generatePageHTML();
            
            this.elements.container.appendChild(this.elements.panel);

            // 获取关键元素引用
            this.elements.ordersList = this.elements.container.querySelector('#ordersList');
            this.elements.batchControlsContainer = this.elements.container.querySelector('#batchControlsContainer');
            this.elements.progressContainer = this.elements.container.querySelector('#progressContainer');
            this.elements.statusContainer = this.elements.container.querySelector('#statusContainer');
        }

        /**
         * 生成页面HTML
         * @returns {string} 页面HTML
         */
        generatePageHTML() {
            return `
                <div class="multi-order-header">
                    <h2>📋 多订单处理 V2</h2>
                    <p>智能识别和批量创建多个订单</p>
                </div>
                
                <div class="multi-order-content">
                    <div class="step-indicator">
                        <div class="step active" data-step="preview">
                            <span class="step-number">1</span>
                            <span class="step-label">预览订单</span>
                        </div>
                        <div class="step" data-step="processing">
                            <span class="step-number">2</span>
                            <span class="step-label">处理中</span>
                        </div>
                        <div class="step" data-step="completed">
                            <span class="step-number">3</span>
                            <span class="step-label">完成</span>
                        </div>
                    </div>
                    
                    <div class="step-content">
                        <div class="step-panel active" data-step="preview">
                            <div class="orders-preview">
                                <div class="orders-list" id="ordersList">
                                    <!-- 订单列表将在这里显示 -->
                                </div>
                                <div class="batch-controls-container" id="batchControlsContainer">
                                    <!-- 批量控件将在这里显示 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="step-panel" data-step="processing">
                            <div class="processing-view">
                                <div class="progress-container" id="progressContainer">
                                    <!-- 进度指示器将在这里显示 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="step-panel" data-step="completed">
                            <div class="results-view">
                                <div class="status-container" id="statusContainer">
                                    <!-- 状态面板将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="multi-order-footer">
                    <button type="button" class="btn btn-secondary" id="backToMainBtn">
                        🏠 返回主页
                    </button>
                </div>
            `;
        }

        /**
         * 初始化页面内容
         */
        initializePageContent() {
            // 初始化组件容器
            this.logger.log('📄 页面内容已初始化', 'info');
        }

        /**
         * 绑定事件
         */
        bindEvents() {
            // 返回主页按钮
            const backBtn = this.elements.container.querySelector('#backToMainBtn');
            if (backBtn) {
                backBtn.addEventListener('click', () => {
                    this.handleBackToMain();
                });
            }
        }

        /**
         * 显示页面
         * @param {Object} data - 页面数据
         */
        async show(data = {}) {
            if (!this.isInitialized) {
                await this.initialize();
            }

            try {
                this.logger.log('📱 显示多订单页面V2', 'info');

                // 显示页面容器
                this.elements.container.classList.remove('hidden');
                this.isActive = true;

                // 获取数据（优先级：参数 > 路由数据 > sessionStorage）
                const initialData = this.getInitialData(data);

                // 如果有订单数据，显示订单
                if (initialData?.orders && Array.isArray(initialData.orders)) {
                    await this.showOrders(initialData.orders, initialData);
                } else if (data.orders && Array.isArray(data.orders)) {
                    await this.showOrders(data.orders, data);
                }

                this.logger.log('✅ 多订单页面V2已显示', 'success');

            } catch (error) {
                this.logger.logError('显示多订单页面V2失败', error);
                throw error;
            }
        }

        /**
         * 获取初始数据
         * @param {Object} paramData - 参数传递的数据
         * @returns {Object} 初始数据
         */
        getInitialData(paramData) {
            try {
                // 优先级1：直接传递的参数（且包含订单数据）
                if (paramData?.orders && Array.isArray(paramData.orders)) {
                    this.logger.log('📦 使用参数传递的数据', 'info');
                    return paramData;
                }

                // 优先级2：路由传递的数据
                if (window.OTA?.router) {
                    const routeData = window.OTA.router.getRouteData();
                    if (routeData?.orders && Array.isArray(routeData.orders)) {
                        this.logger.log('📦 使用路由传递的数据', 'info');
                        // 清除路由数据，避免重复使用
                        window.OTA.router.clearRouteData();
                        return routeData;
                    }
                }

                // 优先级3：sessionStorage中的数据（向后兼容）
                const storedData = sessionStorage.getItem('multiOrderData');
                if (storedData) {
                    try {
                        const parsedData = JSON.parse(storedData);
                        if (parsedData?.orders && Array.isArray(parsedData.orders)) {
                            this.logger.log('📦 使用sessionStorage中的数据', 'info');
                            // 清除sessionStorage数据，避免重复使用
                            sessionStorage.removeItem('multiOrderData');
                            return parsedData;
                        }
                    } catch (parseError) {
                        this.logger.log('解析sessionStorage数据失败', 'warning', parseError);
                    }
                }

                this.logger.log('📦 未找到有效的初始数据', 'info');
                return null;

            } catch (error) {
                this.logger.logError('获取初始数据失败', error);
                return null;
            }
        }

        /**
         * 隐藏页面
         */
        hide() {
            if (this.elements.container) {
                this.elements.container.classList.add('hidden');
            }
            this.isActive = false;

            // 清理自动返回定时器
            this.cancelAutoReturn();

            this.logger.log('📱 多订单页面V2已隐藏', 'info');
        }

        /**
         * 显示订单列表
         * @param {Array} orders - 订单数组
         * @param {Object} metadata - 元数据
         */
        async showOrders(orders, metadata = {}) {
            this.logger.log(`📋 显示 ${orders.length} 个订单`, 'info');

            // 初始化状态管理器
            this.services.stateManager.initialize({
                orders,
                originalText: metadata.originalText,
                detectionMethod: metadata.detectionMethod,
                confidence: metadata.confidence
            });

            // 切换到预览步骤
            this.switchToStep('preview');

            // 创建订单卡片组件
            await this.createOrderCards(orders);

            // 创建批量控件组件
            await this.createBatchControls();

            // 创建进度指示器组件
            await this.createProgressIndicator();

            // 创建状态面板组件
            await this.createStatusPanel();
        }

        /**
         * 切换到指定步骤
         * @param {string} step - 步骤名称
         */
        switchToStep(step) {
            // 更新步骤指示器
            const stepIndicators = this.elements.container.querySelectorAll('.step');
            stepIndicators.forEach(indicator => {
                const stepName = indicator.getAttribute('data-step');
                indicator.classList.toggle('active', stepName === step);
            });

            // 更新步骤面板
            const stepPanels = this.elements.container.querySelectorAll('.step-panel');
            stepPanels.forEach(panel => {
                const stepName = panel.getAttribute('data-step');
                panel.classList.toggle('active', stepName === step);
            });

            this.logger.log(`📍 切换到步骤: ${step}`, 'info');
        }

        /**
         * 创建订单卡片组件
         * @param {Array} orders - 订单数组
         */
        async createOrderCards(orders) {
            if (!this.elements.ordersList) return;

            // 清空现有卡片
            this.elements.ordersList.innerHTML = '';
            this.components.orderCards = [];

            // 性能优化：批量渲染
            if (orders.length > 20) {
                await this.createOrderCardsWithBatching(orders);
            } else {
                await this.createOrderCardsDirectly(orders);
            }

            this.logger.log(`📋 已创建 ${orders.length} 个订单卡片`, 'success');
        }

        /**
         * 直接创建订单卡片（小批量）
         * @param {Array} orders - 订单数组
         */
        async createOrderCardsDirectly(orders) {
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                await this.createSingleOrderCard(order, i);
            }
        }

        /**
         * 批量创建订单卡片（大批量，性能优化）
         * @param {Array} orders - 订单数组
         */
        async createOrderCardsWithBatching(orders) {
            const batchSize = 10; // 每批处理10个
            const totalBatches = Math.ceil(orders.length / batchSize);

            this.logger.log(`🚀 使用批量渲染模式: ${orders.length} 个订单，分 ${totalBatches} 批处理`, 'info');

            // 显示加载指示器
            this.showLoadingIndicator(`正在加载 ${orders.length} 个订单...`);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * batchSize;
                const endIndex = Math.min(startIndex + batchSize, orders.length);
                const batch = orders.slice(startIndex, endIndex);

                // 更新加载进度
                const progress = Math.round(((batchIndex + 1) / totalBatches) * 100);
                this.updateLoadingProgress(progress, `正在加载第 ${batchIndex + 1}/${totalBatches} 批订单...`);

                // 处理当前批次
                for (let i = 0; i < batch.length; i++) {
                    const order = batch[i];
                    const globalIndex = startIndex + i;
                    await this.createSingleOrderCard(order, globalIndex);
                }

                // 让出控制权，避免阻塞UI
                await this.delay(10);
            }

            // 隐藏加载指示器
            this.hideLoadingIndicator();
        }

        /**
         * 创建单个订单卡片
         * @param {Object} order - 订单数据
         * @param {number} index - 订单索引
         */
        async createSingleOrderCard(order, index) {
            // 创建卡片容器
            const cardContainer = document.createElement('div');
            cardContainer.className = 'order-card-container';
            this.elements.ordersList.appendChild(cardContainer);

            // 创建订单卡片组件
            const orderCard = new window.OTA.Components.OrderCard({
                container: cardContainer,
                orderData: order,
                orderIndex: index,
                isSelected: true, // 默认选中
                showCheckbox: true,
                enableSelection: true,
                onSelectionChange: (index, selected) => {
                    this.services.stateManager.updateOrderSelection(index, selected);
                },
                onCardClick: (index, orderData) => {
                    this.logger.log(`点击订单卡片 ${index + 1}`, 'info');
                }
            });

            // 渲染卡片
            orderCard.render();
            this.components.orderCards.push(orderCard);
        }

        /**
         * 显示加载指示器
         * @param {string} message - 加载消息
         */
        showLoadingIndicator(message) {
            if (!this.loadingIndicator) {
                this.loadingIndicator = document.createElement('div');
                this.loadingIndicator.className = 'loading-indicator';
                this.loadingIndicator.innerHTML = `
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message}</div>
                    <div class="loading-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text">0%</div>
                    </div>
                `;
                this.elements.ordersList.appendChild(this.loadingIndicator);
            }
        }

        /**
         * 更新加载进度
         * @param {number} progress - 进度百分比
         * @param {string} message - 进度消息
         */
        updateLoadingProgress(progress, message) {
            if (this.loadingIndicator) {
                const progressFill = this.loadingIndicator.querySelector('.progress-fill');
                const progressText = this.loadingIndicator.querySelector('.progress-text');
                const loadingMessage = this.loadingIndicator.querySelector('.loading-message');

                if (progressFill) progressFill.style.width = `${progress}%`;
                if (progressText) progressText.textContent = `${progress}%`;
                if (loadingMessage) loadingMessage.textContent = message;
            }
        }

        /**
         * 隐藏加载指示器
         */
        hideLoadingIndicator() {
            if (this.loadingIndicator) {
                this.loadingIndicator.remove();
                this.loadingIndicator = null;
            }
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 创建批量控件组件
         */
        async createBatchControls() {
            if (!this.elements.batchControlsContainer) return;

            // 清空现有控件
            this.elements.batchControlsContainer.innerHTML = '';

            // 获取状态信息
            const state = this.services.stateManager.getState();
            const orderState = state.orders || {};

            // 创建批量控件组件
            this.components.batchControls = new window.OTA.Components.BatchControls({
                container: this.elements.batchControlsContainer,
                totalCount: orderState.list?.length || 0,
                selectedCount: orderState.selectedIndices?.size || 0,
                showSelectAll: true,
                showDeselectAll: true,
                showBatchCreate: true,
                showSelectedCount: true,
                onSelectAll: () => {
                    this.services.stateManager.selectAllOrders(true);
                    this.updateOrderCardsSelection();
                },
                onDeselectAll: () => {
                    this.services.stateManager.selectAllOrders(false);
                    this.updateOrderCardsSelection();
                },
                onBatchCreate: (selectedCount) => {
                    this.handleBatchCreate(selectedCount);
                }
            });

            // 渲染控件
            this.components.batchControls.render();

            this.logger.log('⚙️ 批量控件组件已创建', 'success');
        }

        /**
         * 创建进度指示器组件
         */
        async createProgressIndicator() {
            if (!this.elements.progressContainer) return;

            // 清空现有指示器
            this.elements.progressContainer.innerHTML = '';

            // 创建进度指示器组件
            this.components.progressIndicator = new window.OTA.Components.ProgressIndicator({
                container: this.elements.progressContainer,
                showOverallProgress: true,
                showStats: true,
                showOrderList: true,
                enableAnimations: true
            });

            // 渲染指示器
            this.components.progressIndicator.render();

            this.logger.log('📊 进度指示器组件已创建', 'success');
        }

        /**
         * 创建状态面板组件
         */
        async createStatusPanel() {
            if (!this.elements.statusContainer) return;

            // 清空现有面板
            this.elements.statusContainer.innerHTML = '';

            // 创建状态面板组件
            this.components.statusPanel = new window.OTA.Components.StatusPanel({
                container: this.elements.statusContainer,
                showSummary: true,
                showSuccessOrders: true,
                showFailedOrders: true,
                showActions: true,
                enableExport: true,
                enableRetry: true,
                onRetryFailed: (failedOrders) => {
                    this.handleRetryFailed(failedOrders);
                },
                onExportResults: (exportData) => {
                    this.handleExportResults(exportData);
                },
                onBackToMain: () => {
                    this.handleBackToMain();
                }
            });

            // 渲染面板
            this.components.statusPanel.render();

            this.logger.log('📈 状态面板组件已创建', 'success');
        }

        /**
         * 更新订单卡片选择状态
         */
        updateOrderCardsSelection() {
            const selectedIndices = this.services.stateManager.getSelectedIndices();

            this.components.orderCards.forEach((card, index) => {
                const isSelected = selectedIndices.includes(index);
                card.setSelected(isSelected);
            });
        }

        /**
         * 处理批量创建
         * @param {number} selectedCount - 选中数量
         */
        async handleBatchCreate(selectedCount) {
            try {
                this.logger.log(`🚀 开始批量创建 ${selectedCount} 个订单`, 'info');

                // 获取选中的订单
                const selectedOrders = this.services.stateManager.getSelectedOrders();

                if (selectedOrders.length === 0) {
                    this.logger.log('❌ 没有选中的订单', 'warning');
                    return;
                }

                // 开始处理
                this.services.stateManager.startProcessing();
                this.switchToStep('processing');

                // 启动进度跟踪
                this.components.progressIndicator.startProgress(selectedOrders);

                // 执行批量处理
                const results = await this.services.batchManager.processBatch(selectedOrders, {
                    onProgress: (progress, task) => {
                        this.services.stateManager.updateProcessingProgress(
                            task?.index || -1,
                            'processing'
                        );
                        this.components.progressIndicator.updateOrderStatus(
                            task?.index || -1,
                            'processing',
                            '正在创建订单...'
                        );
                    },
                    onTaskComplete: (index, result) => {
                        const status = result.success ? 'success' : 'failed';
                        const message = result.success
                            ? `订单创建成功: ${result.result?.orderId || '未知ID'}`
                            : `创建失败: ${result.error || '未知错误'}`;

                        this.services.stateManager.updateProcessingProgress(index, status === 'success' ? 'completed' : 'failed');
                        this.components.progressIndicator.updateOrderStatus(index, status, message);
                    }
                });

                // 完成处理
                this.components.progressIndicator.completeProgress();
                this.services.stateManager.completeProcessing(results.results);
                this.switchToStep('completed');

                // 显示结果
                this.components.statusPanel.showResults(results);

                this.logger.log('✅ 批量创建完成', 'success');

            } catch (error) {
                this.logger.logError('批量创建失败', error);

                // 显示错误状态
                this.components.progressIndicator.completeProgress();
                this.switchToStep('completed');

                // 显示错误结果
                this.components.statusPanel.showResults({
                    summary: {
                        total: selectedCount,
                        successful: 0,
                        failed: selectedCount,
                        successRate: 0
                    },
                    successful: [],
                    failed: [{ error: error.message }],
                    totalTime: 0
                });
            }
        }

        /**
         * 处理返回主页
         */
        handleBackToMain() {
            this.logger.log('🏠 返回主页', 'info');

            // 隐藏当前页面
            this.hide();

            // 导航到主页
            if (window.OTA?.router) {
                window.OTA.router.navigate('/');
            } else {
                window.location.hash = '#/';
            }
        }

        /**
         * 处理订单选择变化
         * @param {Object} data - 选择变化数据
         */
        handleOrderSelectionChanged(data) {
            // 更新批量控件状态
            if (this.components.batchControls) {
                this.components.batchControls.setSelectedCount(data.selectedCount, data.totalCount);
            }
        }

        /**
         * 处理处理开始
         * @param {Object} data - 处理开始数据
         */
        handleProcessingStarted(data) {
            // 禁用批量控件
            if (this.components.batchControls) {
                this.components.batchControls.setProcessing(true);
            }
        }

        /**
         * 处理进度更新
         * @param {Object} data - 进度数据
         */
        handleProgressUpdated(data) {
            // 进度指示器会自动更新，这里可以添加额外的处理
        }

        /**
         * 处理处理完成
         * @param {Object} data - 完成数据
         */
        handleProcessingCompleted(data) {
            this.logger.log('🎉 多订单处理完成', 'success', data);

            // 启用批量控件
            if (this.components.batchControls) {
                this.components.batchControls.setProcessing(false);
            }

            // 显示完成提示
            this.showCompletionMessage(data);

            // 自动返回主页面（延迟3秒）
            this.scheduleAutoReturn(3000);
        }

        /**
         * 显示完成提示
         * @param {Object} data - 完成数据
         */
        showCompletionMessage(data) {
            try {
                const { successCount = 0, failedCount = 0, totalCount = 0 } = data;

                let message = `✅ 处理完成！`;
                if (totalCount > 0) {
                    message += ` 成功: ${successCount}/${totalCount}`;
                    if (failedCount > 0) {
                        message += `, 失败: ${failedCount}`;
                    }
                }

                // 显示状态消息
                if (this.components.statusPanel) {
                    this.components.statusPanel.showMessage(message, 'success');
                }

                this.logger.log(message, 'success');

            } catch (error) {
                this.logger.logError('显示完成提示失败', error);
            }
        }

        /**
         * 安排自动返回
         * @param {number} delay - 延迟时间（毫秒）
         */
        scheduleAutoReturn(delay = 3000) {
            try {
                this.logger.log(`⏰ ${delay/1000}秒后自动返回主页`, 'info');

                // 清除之前的定时器
                if (this.autoReturnTimer) {
                    clearTimeout(this.autoReturnTimer);
                }

                // 设置新的定时器
                this.autoReturnTimer = setTimeout(() => {
                    this.logger.log('🏠 自动返回主页', 'info');
                    this.handleBackToMain();
                }, delay);

                // 显示倒计时提示
                this.showAutoReturnCountdown(delay);

            } catch (error) {
                this.logger.logError('安排自动返回失败', error);
            }
        }

        /**
         * 显示自动返回倒计时
         * @param {number} totalDelay - 总延迟时间
         */
        showAutoReturnCountdown(totalDelay) {
            try {
                let remainingSeconds = Math.ceil(totalDelay / 1000);

                const updateCountdown = () => {
                    if (remainingSeconds > 0) {
                        const message = `🏠 ${remainingSeconds}秒后自动返回主页 (点击取消)`;

                        // 更新状态面板消息
                        if (this.components.statusPanel) {
                            this.components.statusPanel.showMessage(message, 'info', {
                                onClick: () => this.cancelAutoReturn()
                            });
                        }

                        remainingSeconds--;
                        setTimeout(updateCountdown, 1000);
                    }
                };

                updateCountdown();

            } catch (error) {
                this.logger.logError('显示自动返回倒计时失败', error);
            }
        }

        /**
         * 取消自动返回
         */
        cancelAutoReturn() {
            try {
                if (this.autoReturnTimer) {
                    clearTimeout(this.autoReturnTimer);
                    this.autoReturnTimer = null;
                    this.logger.log('❌ 自动返回已取消', 'info');

                    // 更新状态面板消息
                    if (this.components.statusPanel) {
                        this.components.statusPanel.showMessage('自动返回已取消', 'info');
                    }
                }
            } catch (error) {
                this.logger.logError('取消自动返回失败', error);
            }
        }

        /**
         * 处理状态重置
         */
        handleStateReset() {
            // 重置所有组件状态
            this.switchToStep('preview');

            if (this.components.progressIndicator) {
                this.components.progressIndicator.resetProgress();
            }

            if (this.components.statusPanel) {
                this.components.statusPanel.clearResults();
            }
        }

        /**
         * 处理重试失败订单
         * @param {Array} failedOrders - 失败的订单
         */
        async handleRetryFailed(failedOrders) {
            try {
                this.logger.log(`🔄 重试 ${failedOrders.length} 个失败订单`, 'info');

                // 提取订单数据
                const ordersToRetry = failedOrders.map(failed => failed.order || failed.orderData);

                // 重新开始处理
                this.switchToStep('processing');
                this.components.progressIndicator.startProgress(ordersToRetry);

                // 执行重试
                const results = await this.services.batchManager.processBatch(ordersToRetry, {
                    onProgress: (progress, task) => {
                        this.components.progressIndicator.updateOrderStatus(
                            task?.index || -1,
                            'processing',
                            '正在重试创建订单...'
                        );
                    },
                    onTaskComplete: (index, result) => {
                        const status = result.success ? 'success' : 'failed';
                        const message = result.success
                            ? `重试成功: ${result.result?.orderId || '未知ID'}`
                            : `重试失败: ${result.error || '未知错误'}`;

                        this.components.progressIndicator.updateOrderStatus(index, status, message);
                    }
                });

                // 完成重试
                this.components.progressIndicator.completeProgress();
                this.switchToStep('completed');
                this.components.statusPanel.showResults(results);

                this.logger.log('✅ 重试完成', 'success');

            } catch (error) {
                this.logger.logError('重试失败', error);
            }
        }

        /**
         * 处理导出结果
         * @param {Object} exportData - 导出数据
         */
        handleExportResults(exportData) {
            try {
                // 生成文件名
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const filename = `multi-order-results-${timestamp}.json`;

                // 创建下载链接
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = filename;
                link.click();

                this.logger.log(`📄 结果已导出: ${filename}`, 'success');

            } catch (error) {
                this.logger.logError('导出结果失败', error);
            }
        }

        /**
         * 销毁页面
         */
        destroy() {
            this.logger.log('🗑️ 销毁多订单页面V2', 'info');

            // 销毁所有组件
            this.components.orderCards.forEach(card => card.destroy());
            if (this.components.batchControls) this.components.batchControls.destroy();
            if (this.components.progressIndicator) this.components.progressIndicator.destroy();
            if (this.components.statusPanel) this.components.statusPanel.destroy();

            // 移除页面容器
            if (this.elements.container && this.elements.container.parentNode) {
                this.elements.container.parentNode.removeChild(this.elements.container);
            }

            // 清理状态
            this.elements = { container: null, panel: null };
            this.components = { orderCards: [], batchControls: null, progressIndicator: null, statusPanel: null };
            this.services = { stateManager: null, orderDetector: null, orderProcessor: null, batchManager: null, apiClient: null };
            this.isInitialized = false;
            this.isActive = false;
        }

        /**
         * 处理全局错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleGlobalError(error, context = {}) {
            try {
                this.logger.logError('全局错误', error);

                // 确定错误类型
                const errorType = this.determineErrorType(error, context);

                // 增加错误计数
                if (this.errorHandler) {
                    const currentCount = this.errorHandler.errorCounts.get(errorType) || 0;
                    this.errorHandler.errorCounts.set(errorType, currentCount + 1);

                    // 检查是否超过最大错误次数
                    if (currentCount >= this.errorHandler.maxErrors) {
                        this.handleCriticalError(error, errorType);
                        return;
                    }

                    // 执行恢复策略
                    const recoveryStrategy = this.errorHandler.recoveryStrategies.get(errorType);
                    if (recoveryStrategy) {
                        recoveryStrategy(error, context);
                    } else {
                        this.handleUnknownError(error, context);
                    }
                }

            } catch (handlerError) {
                console.error('错误处理器本身出错:', handlerError);
                this.showCriticalErrorMessage();
            }
        }

        /**
         * 确定错误类型
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         * @returns {string} 错误类型
         */
        determineErrorType(error, context) {
            const message = error.message?.toLowerCase() || '';

            if (message.includes('network') || message.includes('fetch')) {
                return 'network';
            }
            if (message.includes('timeout') || message.includes('超时')) {
                return 'timeout';
            }
            if (message.includes('api') || message.includes('服务')) {
                return 'api';
            }
            if (message.includes('validation') || message.includes('验证') || message.includes('必填')) {
                return 'validation';
            }

            return 'unknown';
        }

        /**
         * 处理网络错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleNetworkError(error, context) {
            this.logger.log('🌐 处理网络错误', 'warning');

            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    '网络连接异常，请检查网络设置后重试',
                    'error'
                );
            }
        }

        /**
         * 处理API错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleApiError(error, context) {
            this.logger.log('🔌 处理API错误', 'warning');

            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    'API服务异常，正在尝试恢复...',
                    'error'
                );
            }
        }

        /**
         * 处理验证错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleValidationError(error, context) {
            this.logger.log('✅ 处理验证错误', 'warning');

            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    `数据验证失败: ${error.message}`,
                    'warning'
                );
            }
        }

        /**
         * 处理超时错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleTimeoutError(error, context) {
            this.logger.log('⏰ 处理超时错误', 'warning');

            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    '操作超时，请稍后重试',
                    'warning'
                );
            }
        }

        /**
         * 处理未知错误
         * @param {Error} error - 错误对象
         * @param {Object} context - 错误上下文
         */
        handleUnknownError(error, context) {
            this.logger.log('❓ 处理未知错误', 'error');

            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    `发生未知错误: ${error.message}`,
                    'error'
                );
            }
        }

        /**
         * 处理严重错误
         * @param {Error} error - 错误对象
         * @param {string} errorType - 错误类型
         */
        handleCriticalError(error, errorType) {
            this.logger.logError(`严重错误 (${errorType})`, error);

            this.showCriticalErrorMessage();

            // 自动返回主页
            setTimeout(() => {
                this.handleBackToMain();
            }, 5000);
        }

        /**
         * 显示严重错误消息
         */
        showCriticalErrorMessage() {
            if (this.components.statusPanel) {
                this.components.statusPanel.showMessage(
                    '系统遇到严重错误，5秒后自动返回主页',
                    'error'
                );
            }
        }

        /**
         * 获取页面信息
         * @returns {Object} 页面信息
         */
        getPageInfo() {
            return {
                id: this.pageId,
                title: '多订单处理 V2',
                isInitialized: this.isInitialized,
                isActive: this.isActive,
                version: '2.0.0'
            };
        }
    }

    // Linus 的"好品味"原则：消除不必要的复杂性，直接解决问题

    // 创建页面实例
    const multiOrderPageV2 = new MultiOrderPageV2();

    // 暴露到OTA命名空间 - 确保命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.Pages = window.OTA.Pages || {};
    window.OTA.Pages.MultiOrderPageV2 = MultiOrderPageV2;
    window.OTA.Pages.multiOrderPageV2 = multiOrderPageV2;

    // 页面管理器期望的命名 - 确保兼容性
    window.OTA.multiOrderPage = multiOrderPageV2;

    // 向后兼容
    window.multiOrderPageV2 = multiOrderPageV2;

    console.log('✅ 多订单页面控制器V2已加载');

})();
