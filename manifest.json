{"name": "OTA订单处理系统 - <PERSON><PERSON>重构版", "short_name": "OTA系统", "description": "高效的OTA订单处理和管理系统，采用Linus Torvalds式重构，简洁高效。", "version": "2.1.0", "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#2196F3", "background_color": "#ffffff", "scope": "/", "lang": "zh-CN", "dir": "ltr", "categories": ["business", "productivity", "travel"], "icons": [{"src": "icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "screenshots/desktop.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "桌面版主界面"}, {"src": "screenshots/mobile.png", "sizes": "375x667", "type": "image/png", "platform": "narrow", "label": "移动版主界面"}], "shortcuts": [{"name": "创建新订单", "short_name": "新订单", "description": "快速创建新的OTA订单", "url": "/?action=new-order", "icons": [{"src": "icons/shortcut-new-order.png", "sizes": "96x96"}]}, {"name": "订单历史", "short_name": "历史", "description": "查看订单历史记录", "url": "/?action=history", "icons": [{"src": "icons/shortcut-history.png", "sizes": "96x96"}]}, {"name": "多订单处理", "short_name": "批量", "description": "批量处理多个订单", "url": "/?action=multi-order", "icons": [{"src": "icons/shortcut-multi-order.png", "sizes": "96x96"}]}], "protocol_handlers": [{"protocol": "mailto", "url": "/?action=contact&email=%s"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}