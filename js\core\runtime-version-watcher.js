// Runtime Version Watcher - 无感知刷新版本
// 周期性获取 /.well-known/build.json 比对 buildHash，发现新版本智能无感知刷新
(function(){
  'use strict';
  const POLL_INTERVAL = 60 * 1000; // 1分钟
  const ENDPOINT = '/.well-known/build.json?_=';
  const LS_KEY = 'ota_last_build_hash';
  function log(msg){ try { console.log('[VersionWatcher]', msg); } catch(_){} }

  // 无感知更新配置
  const SILENT_UPDATE_CONFIG = {
    enabled: true,                    // 启用无感知更新
    updateOnHidden: true,             // 页面隐藏时更新
    updateOnIdle: true,               // 用户空闲时更新
    idleTimeout: 300000,              // 5分钟空闲阈值
    maxWaitTime: 1800000,             // 30分钟最大等待时间
    preserveFormData: true,           // 保护表单数据
    showMinimalNotification: true,    // 显示最小化通知
    notificationDuration: 2000        // 通知显示时间
  };

  // 用户活动监测变量
  let lastActivity = Date.now();
  let isUserIdle = false;
  let pendingUpdate = null;
  const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
  // 初始化用户活动监测
  function initActivityMonitoring() {
    // 监听用户活动事件
    activityEvents.forEach(event => {
      document.addEventListener(event, () => {
        lastActivity = Date.now();
        isUserIdle = false;
      }, { passive: true });
    });

    // 定期检测用户空闲状态
    setInterval(() => {
      const idleTime = Date.now() - lastActivity;
      isUserIdle = idleTime > SILENT_UPDATE_CONFIG.idleTimeout;
    }, 30000); // 每30秒检查一次

    log('用户活动监测已初始化');
  }

  // 如果在 file:// 或 非 http(s) 协议下，直接禁用版本轮询（浏览器禁止跨源 file fetch）
  const isFileProtocol = typeof location !== 'undefined' && location.protocol !== 'http:' && location.protocol !== 'https:';
  if (isFileProtocol) {
    log('检测到本地 file 协议，禁用远程版本轮询 (需要通过本地静态服务器运行以启用热版本检测)');
    // 若存在内嵌 BUILD_INFO，则初始化一次本地存储，避免后续逻辑误判
    if (window.BUILD_INFO && window.BUILD_INFO.buildHash) {
      try { localStorage.setItem(LS_KEY, window.BUILD_INFO.buildHash); } catch(_){}
    }
    return; // 直接退出 watcher 脚本
  }
  async function fetchBuildInfo() {
    try {
      const res = await fetch(ENDPOINT + Date.now(), { cache: 'no-store' });
      if (!res.ok) throw new Error(res.status+'');
      return await res.json();
    } catch (e) { log('获取失败: '+ e.message); return null; }
  }

  // 检查是否有未保存的表单数据
  function hasUnsavedFormData() {
    if (!SILENT_UPDATE_CONFIG.preserveFormData) return false;

    const form = document.getElementById('orderForm');
    if (!form) return false;

    const formData = new FormData(form);
    for (let [key, value] of formData.entries()) {
      if (value && value.toString().trim()) {
        return true;
      }
    }
    return false;
  }

  // 保存表单数据
  function saveFormDataBeforeUpdate() {
    const form = document.getElementById('orderForm');
    if (!form) return;

    const formData = new FormData(form);
    const data = {};
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }

    localStorage.setItem('ota_auto_save_before_update', JSON.stringify({
      data: data,
      timestamp: Date.now(),
      reason: 'silent_update'
    }));

    log('表单数据已自动保存');
  }

  // 显示最小化通知
  function showMinimalNotification() {
    if (!SILENT_UPDATE_CONFIG.showMinimalNotification) return;

    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed; top: 20px; right: 20px; z-index: 99999;
      background: rgba(33, 150, 243, 0.9); color: white;
      padding: 8px 16px; border-radius: 4px; font-size: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      transition: opacity 0.3s ease;
    `;
    notification.textContent = '🔄 正在更新到最新版本...';
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => notification.remove(), 300);
    }, SILENT_UPDATE_CONFIG.notificationDuration);
  }

  // 执行静默更新
  async function performSilentUpdate() {
    try {
      log('开始执行无感知更新');

      // 保存表单数据
      saveFormDataBeforeUpdate();

      // 显示最小化通知
      showMinimalNotification();

      // 短暂延迟确保通知显示
      await new Promise(resolve => setTimeout(resolve, 500));

      // 执行硬刷新
      await hardRefresh();
    } catch (error) {
      log('静默更新失败: ' + error.message);
      // 降级到手动模式
      createBanner({ version: 'unknown' });
    }
  }

  function createBanner(newInfo) {
    if (document.getElementById('version-update-banner')) return; // 已存在
    const div = document.createElement('div');
    div.id = 'version-update-banner';
    div.style.cssText = 'position:fixed;bottom:0;left:0;right:0;padding:8px 14px;background:#1976d2;color:#fff;font:14px/1.4 system-ui;display:flex;gap:12px;align-items:center;z-index:99999;';
    div.innerHTML = `<strong>🔄 检测到新版本</strong><span>v${newInfo.version}</span>`;
    const btnRefresh = document.createElement('button');
    btnRefresh.textContent = '刷新';
    btnRefresh.style.cssText = 'background:#fff;color:#1976d2;border:none;padding:4px 10px;border-radius:4px;cursor:pointer;font-weight:600;';
    btnRefresh.onclick = async () => { await hardRefresh(); };
    const btnDismiss = document.createElement('button');
    btnDismiss.textContent = '稍后';
    btnDismiss.style.cssText = 'background:rgba(255,255,255,0.2);color:#fff;border:none;padding:4px 10px;border-radius:4px;cursor:pointer;';
    btnDismiss.onclick = () => div.remove();
    div.appendChild(btnRefresh); div.appendChild(btnDismiss);
    document.body.appendChild(div);
  }
  // 智能更新调度
  function scheduleSmartUpdate(newInfo) {
    const startTime = Date.now();
    pendingUpdate = { info: newInfo, scheduledAt: startTime };

    log('开始智能更新调度');

    const checkUpdate = () => {
      if (!pendingUpdate) return;

      const waitTime = Date.now() - pendingUpdate.scheduledAt;

      // 强制更新：超过最大等待时间
      if (waitTime > SILENT_UPDATE_CONFIG.maxWaitTime) {
        log('达到最大等待时间，强制执行更新');
        performSilentUpdate();
        pendingUpdate = null;
        return;
      }

      // 理想时机：页面隐藏且用户空闲
      if (document.hidden && isUserIdle) {
        log('检测到理想更新时机：页面隐藏且用户空闲');
        performSilentUpdate();
        pendingUpdate = null;
        return;
      }

      // 次优时机：仅用户空闲（等待至少1分钟）
      if (isUserIdle && waitTime > 60000) {
        log('检测到次优更新时机：用户空闲');
        performSilentUpdate();
        pendingUpdate = null;
        return;
      }
    };

    // 立即检查一次
    checkUpdate();

    // 定期检查
    const intervalId = setInterval(checkUpdate, 30000);

    // 页面可见性变化时检查
    const visibilityHandler = () => {
      if (document.hidden) {
        setTimeout(checkUpdate, 1000); // 延迟1秒确保页面完全隐藏
      }
    };

    document.addEventListener('visibilitychange', visibilityHandler);

    // 清理和降级处理
    setTimeout(() => {
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', visibilityHandler);

      // 如果还没更新，显示手动提示
      if (pendingUpdate) {
        log('智能更新超时，降级到手动模式');
        createBanner(pendingUpdate.info);
        pendingUpdate = null;
      }
    }, SILENT_UPDATE_CONFIG.maxWaitTime + 60000);
  }

  async function hardRefresh(){
    try {
      if ('serviceWorker' in navigator) {
        const regs = await navigator.serviceWorker.getRegistrations();
        for (const r of regs) await r.unregister();
      }
      if (window.caches) {
        const keys = await caches.keys();
        for (const k of keys) await caches.delete(k);
      }
    } catch(_){}
    location.reload(true);
  }
  // 处理版本更新
  function handleVersionUpdate(newInfo) {
    if (!SILENT_UPDATE_CONFIG.enabled) {
      log('无感知更新已禁用，使用手动模式');
      createBanner(newInfo);
      return;
    }

    // 检查表单数据
    if (hasUnsavedFormData()) {
      log('检测到未保存的表单数据，延迟更新');
      scheduleSmartUpdate(newInfo);
      return;
    }

    // 立即更新条件：页面隐藏
    if (document.hidden) {
      log('页面隐藏，立即执行无感知更新');
      performSilentUpdate();
      return;
    }

    // 延迟更新：等待合适时机
    log('等待合适时机执行无感知更新');
    scheduleSmartUpdate(newInfo);
  }

  async function check(){
    const info = await fetchBuildInfo();
    if (!info || !info.buildHash) return;
    const prev = localStorage.getItem(LS_KEY);
    if (!prev) {
      localStorage.setItem(LS_KEY, info.buildHash);
      return;
    }
    if (prev !== info.buildHash) {
      log('发现新版本: '+ info.buildHash);
      handleVersionUpdate(info); // 使用新的处理函数
      localStorage.setItem(LS_KEY, info.buildHash);
    }
  }
  // 初始化
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initActivityMonitoring(); // 初始化用户活动监测
    setTimeout(check, 3000);
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      initActivityMonitoring(); // 初始化用户活动监测
      setTimeout(check, 3000);
    });
  }
  setInterval(check, POLL_INTERVAL);
  log('无感知版本监控已初始化');
})();
