
// ❌ Deprecated: ota-channel-config.js 已废弃，渠道数据改由 user-permissions-config.js 统一提供
// 保留文件以避免旧脚本引用报错，但不再导出任何可用数据。
(function() {
    'use strict';
    window.OTA = window.OTA || {};
    const otaChannelMapping = {
        // JR Coach (ID: 2666) - 专属渠道 (限制访问)
            2666: {
                default: 'JR Coach Credit',
                options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' },
                { value: 'JR COACH SERVICES - C1', text: 'JR COACH SERVICES - C1' },
                { value: 'JR COACH SERVICES - HTP - C1', text: 'JR COACH SERVICES - HTP - C1' },
                { value: 'JR COACH SERVICES - GTV - C1', text: 'JR COACH SERVICES - GTV - C1' },
                { value: 'JR COACH SERVICES - JRV - C1', text: 'JR COACH SERVICES - JRV - C1' },
                { value: 'JR COACH SERVICES - WYNN - C1', text: 'JR COACH SERVICES - WYNN - C1' },
                { value: 'JR COACH SERVICES - EJH - C1', text: 'JR COACH SERVICES - EJH - C1' }
                ]
            },
            '<EMAIL>': { // 修复大小写问题 (限制访问)
                default: 'JR Coach Credit',
                options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' },
                { value: 'JR COACH SERVICES - C1', text: 'JR COACH SERVICES - C1' },
                { value: 'JR COACH SERVICES - HTP - C1', text: 'JR COACH SERVICES - HTP - C1' },
                { value: 'JR COACH SERVICES - GTV - C1', text: 'JR COACH SERVICES - GTV - C1' },
                { value: 'JR COACH SERVICES - JRV - C1', text: 'JR COACH SERVICES - JRV - C1' },
                { value: 'JR COACH SERVICES - WYNN - C1', text: 'JR COACH SERVICES - WYNN - C1' },
                { value: 'JR COACH SERVICES - EJH - C1', text: 'JR COACH SERVICES - EJH - C1' }
                ]
            },


        // UCSI - Cheras (ID: 2446) - 教育机构渠道 (限制访问)
        2446: {
            default: 'UCSI - Cheras',
            options: [
                { value: 'UCSI - Cheras', text: 'UCSI - Cheras' },
                { value: 'UCSI - Port Dickson', text: 'UCSI - Port Dickson' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'UCSI - Cheras',
            options: [
                { value: 'UCSI - Cheras', text: 'UCSI - Cheras' },
                { value: 'UCSI - Port Dickson', text: 'UCSI - Port Dickson' },
                { value: 'Student Travel', text: 'Student Travel' }
            ]
        },

        // Eramaz Travel (ID: 2793) - 新增渠道 (限制访问)
        2793: {
            default: 'Eramaz Travel C1',
            options: [
                { value: 'Eramaz Travel C1', text: 'Eramaz Travel C1' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'Eramaz Travel C1',
            options: [
                { value: 'Eramaz Travel C1', text: 'Eramaz Travel C1' }
            ]
        },

        // Kai (ID: 2788) - JB区域用户 (限制访问)
        2788: {
            default: 'Kai - TC1',
            options: [
                { value: 'Kai - TC1', text: 'Kai - TC1' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'Kai - TC1',
            options: [
                { value: 'Kai - TC1', text: 'Kai - TC1' }
            ]
        },

        // Ocean Blue (ID: 2732) - TC2渠道 (限制访问)
        2732: {
            default: 'Ocean Blue - JC TRAVEL SDN BHD - TC2',
            options: [
                { value: 'Ocean Blue - JC TRAVEL SDN BHD - TC2', text: 'Ocean Blue - JC TRAVEL SDN BHD - TC2' },
                { value: 'Ocean Blue - JC TRAVEL SDN BHD QR', text: 'Ocean Blue - JC TRAVEL SDN BHD QR' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'Ocean Blue - JC TRAVEL SDN BHD - TC2',
            options: [
                { value: 'Ocean Blue - JC TRAVEL SDN BHD - TC2', text: 'Ocean Blue - JC TRAVEL SDN BHD - TC2' },
                { value: 'Ocean Blue - JC TRAVEL SDN BHD QR', text: 'Ocean Blue - JC TRAVEL SDN BHD QR' }
            ]
        },

        // Mytravelexpert (ID: 2765) - 专业旅游顾问 (限制访问)
        2765: {
            default: 'Mytravelexpert - TC1',
            options: [
                { value: 'Mytravelexpert - TC1', text: 'Mytravelexpert - TC1' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'Mytravelexpert - TC1',
            options: [
                { value: 'Mytravelexpert - TC1', text: 'Mytravelexpert - TC1' }
            ]
        },

        // Demo (ID: 2766) - 演示用户 (限制访问)
        2766: {
            default: 'Demo',
            options: [
                { value: 'Demo', text: 'Demo' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'Demo',
            options: [
                { value: 'Demo', text: 'Demo' }
            ]
        },

        // Kelvin Lim (ID: 2847) - 新增专属渠道 (限制访问)
        2847: {
            default: 'KelvinLim - D1',
            options: [
                { value: 'KelvinLim - D1', text: 'KelvinLim - D1' }
            ]
        },
        '<EMAIL>': { // 限制访问
            default: 'KelvinLim - D1',
            options: [
                { value: 'KelvinLim - D1', text: 'KelvinLim - D1' }
            ]
        }
    };

    /**
     * 通用OTA渠道列表 (当没有特定用户配置时使用)
     * 基于 https://gomyhire.com/s/ota 的最新数据 (更新日期: 2025-08-04)
     * 包含ID 101-134的所有活跃OTA渠道
     */
    const commonChannels = [
        // 核心OTA平台
        { value: 'Klook West Malaysia', text: 'Klook West Malaysia' },
        { value: 'Klook Singapore', text: 'Klook Singapore' },
        { value: 'Kkday', text: 'Kkday' },
        { value: 'Ctrip West Malaysia', text: 'Ctrip West Malaysia' },
        { value: 'Ctrip API', text: 'Ctrip API' },
        { value: '携程专车', text: '携程专车' },
        { value: '携程商铺 - CN', text: '携程商铺 - CN' },
        { value: 'Fliggy', text: 'Fliggy' },
        { value: 'Traveloka', text: 'Traveloka' },
        { value: 'Heycar', text: 'Heycar' },
        { value: 'Mozio', text: 'Mozio' },

        // SMW相关渠道
        { value: 'SMW Eric', text: 'SMW Eric' },
        { value: 'Smw Wilson', text: 'Smw Wilson' },
        { value: 'Smw Josua', text: 'Smw Josua' },
        { value: 'Smw Jcyap', text: 'Smw Jcyap' },
        { value: 'Smw Vivian Lim', text: 'Smw Vivian Lim' },
        { value: 'Smw Wendy', text: 'Smw Wendy' },
        { value: 'Smw Annie', text: 'Smw Annie' },
        { value: 'SMW Xiaohongshu', text: 'SMW Xiaohongshu' },
        { value: 'SMW Whatsapp', text: 'SMW Whatsapp' },
        { value: 'SMW Agent', text: 'SMW Agent' },
        { value: 'SMW Walk In', text: 'SMW Walk In' },
        { value: 'SMW Driver Walk-In Com', text: 'SMW Driver Walk-In Com' },

        // GMH团队渠道
        { value: 'GMH Sabah', text: 'GMH Sabah' },
        { value: '随程-GMH Sabah', text: '随程-GMH Sabah' },
        { value: 'GMH Terry', text: 'GMH Terry' },
        { value: 'GMH Ms Yong', text: 'GMH Ms Yong' },
        { value: 'GMH Ashley', text: 'GMH Ashley' },
        { value: 'GMH Calvin', text: 'GMH Calvin' },
        { value: 'GMH May', text: 'GMH May' },
        { value: 'GMH Daniel Fong', text: 'GMH Daniel Fong' },
        { value: 'GMH BNI', text: 'GMH BNI' },
        { value: 'GMH SQ', text: 'GMH SQ' },
        { value: 'GMH Jiahui', text: 'GMH Jiahui' },
        { value: 'GMH Vikki', text: 'GMH Vikki' },
        { value: 'GMH Qijun', text: 'GMH Qijun' },
        { value: 'GMH Venus', text: 'GMH Venus' },
        { value: 'GMH Karen', text: 'GMH Karen' },
        { value: 'GMH Cynthia B10', text: 'GMH Cynthia B10' },
        { value: 'GMH Cynthia', text: 'GMH Cynthia' },
        { value: 'GMH Jing Soon', text: 'GMH Jing Soon' },
        { value: 'GMH Driver', text: 'GMH Driver' },
        { value: 'GMH Xiaoxuan', text: 'GMH Xiaoxuan' },
        { value: 'GMH Vivian B2B', text: 'GMH Vivian B2B' },
        { value: 'GMH Ads', text: 'GMH Ads' },
        { value: 'GoMyHire - KL', text: 'GoMyHire - KL' },
        { value: 'GoMyHire Webpage', text: 'GoMyHire Webpage' },
        { value: 'Gomyhire Pohchengfatt', text: 'Gomyhire Pohchengfatt' },

        // JR Coach Services系列
        { value: 'JR COACH SERVICES - C1', text: 'JR COACH SERVICES - C1' },
        { value: 'JR COACH SERVICES - HTP - C1', text: 'JR COACH SERVICES - HTP - C1' },
        { value: 'JR COACH SERVICES - GTV - C1', text: 'JR COACH SERVICES - GTV - C1' },
        { value: 'JR COACH SERVICES - JRV - C1', text: 'JR COACH SERVICES - JRV - C1' },
        { value: 'JR COACH SERVICES - WYNN - C1', text: 'JR COACH SERVICES - WYNN - C1' },
        { value: 'JR COACH SERVICES - EJH - C1', text: 'JR COACH SERVICES - EJH - C1' },

        // 酒店合作伙伴
        { value: 'Hotel - Padibox Homestay', text: 'Hotel - Padibox Homestay' },
        { value: 'Hotel - Padi Sentral Homestay', text: 'Hotel - Padi Sentral Homestay' },
        { value: 'Hotel - Secret Garden Homestay', text: 'Hotel - Secret Garden Homestay' },
        { value: 'Hotel - Leshore Hotel', text: 'Hotel - Leshore Hotel' },
        { value: 'Hotel - VI Boutique', text: 'Hotel - VI Boutique' },
        { value: 'Hotel - East Sun Hotel', text: 'Hotel - East Sun Hotel' },
        { value: 'The Pearl Kuala Lumpur Hotel', text: 'The Pearl Kuala Lumpur Hotel' },
        { value: 'Le Méridien Putrajaya', text: 'Le Méridien Putrajaya' },
        { value: 'ONE18 Boutique Hotel', text: 'ONE18 Boutique Hotel' },
        { value: 'Bintang Collectionz Hotel', text: 'Bintang Collectionz Hotel' },

        // MapleHome系列
        { value: 'MapleHome - The Robertson KL', text: 'MapleHome - The Robertson KL' },
        { value: 'MapleHome - Swiss Garden Kuala Lumpur', text: 'MapleHome - Swiss Garden Kuala Lumpur' },
        { value: 'MapleHome - D\'Majestic Premier Suites Kuala Lumpur', text: 'MapleHome - D\'Majestic Premier Suites Kuala Lumpur' },
        { value: 'MapleHome- Chambers Premier Suites Kuala Lumpur', text: 'MapleHome- Chambers Premier Suites Kuala Lumpur' },
        { value: 'MapleHome - Geo38 Premier Suites Kuala Lumpur', text: 'MapleHome - Geo38 Premier Suites Kuala Lumpur' },
        { value: 'MapleHome - The Apple Premier Suites Melaka', text: 'MapleHome - The Apple Premier Suites Melaka' },
        { value: 'MapleHome - Amber Cove Premier Suites Melaka', text: 'MapleHome - Amber Cove Premier Suites Melaka' },
        { value: 'The Maple Suite - Bukit Bintang', text: 'The Maple Suite - Bukit Bintang' },

        // Ocean Blue系列
        { value: 'Ocean Blue - JC TRAVEL SDN BHD - TC2', text: 'Ocean Blue - JC TRAVEL SDN BHD - TC2' },
        { value: 'Ocean Blue - JC TRAVEL SDN BHD QR', text: 'Ocean Blue - JC TRAVEL SDN BHD QR' },

        // 旅行社和代理商
        { value: 'B2B Lewis', text: 'B2B Lewis' },
        { value: 'B TN Holiday Sdn Bhd-Eunice', text: 'B TN Holiday Sdn Bhd-Eunice' },
        { value: 'Chong Dealer', text: 'Chong Dealer' },
        { value: 'Jing Ge', text: 'Jing Ge' },
        { value: 'Jing Ge Htp', text: 'Jing Ge Htp' },
        { value: 'YenNei', text: 'YenNei' },
        { value: 'EHTT 徐杰', text: 'EHTT 徐杰' },
        { value: 'Joydeer', text: 'Joydeer' },
        { value: 'KL Eric', text: 'KL Eric' },
        { value: 'Co-operate Stan', text: 'Co-operate Stan' },
        { value: '7deer Travel', text: '7deer Travel' },
        { value: 'Columbia', text: 'Columbia' },
        { value: 'Asia Trail', text: 'Asia Trail' },
        { value: 'Good Earth Travel', text: 'Good Earth Travel' },
        { value: 'Thousand Travel', text: 'Thousand Travel' },
        { value: 'Sabah Adventure', text: 'Sabah Adventure' },
        { value: '全景旅游', text: '全景旅游' },
                // M.I.C.E Tour
        { value: 'M.I.C.E Tour', text: 'M.I.C.E Tour' },
        { value: 'Mytravelexpert - TC1', text: 'Mytravelexpert - TC1' },
        { value: 'Eramaz Travel C1', text: 'Eramaz Travel C1' },
        { value: '上海佳禾', text: '上海佳禾' },
        { value: '云南昆果教育', text: '云南昆果教育' },

        // WelcomePickups
        { value: 'WelcomePickups Sabah', text: 'WelcomePickups Sabah' },
        { value: 'WelcomePickups West Malaysia', text: 'WelcomePickups West Malaysia' },

        // 教育机构
        { value: 'UCSI - Cheras', text: 'UCSI - Cheras' },
        { value: 'UCSI - Port Dickson', text: 'UCSI - Port Dickson' },
        { value: 'ReSkill', text: 'ReSkill' },

        // 特殊服务
        { value: 'Want To Eat Restaurant', text: 'Want To Eat Restaurant' },
        { value: 'Driver Own Job', text: 'Driver Own Job' },
        { value: 'Smartryde HTP', text: 'Smartryde HTP' },
        { value: 'KTMB', text: 'KTMB' },
        { value: 'HTP - 空港嘉华', text: 'HTP - 空港嘉华' },

        // 个人代理和其他
        { value: 'Reward', text: 'Reward' },
        { value: 'Bob', text: 'Bob' },
        { value: 'Pg Sue', text: 'Pg Sue' },
        { value: 'Pg Afzan', text: 'Pg Afzan' },
        { value: 'KK Lucas', text: 'KK Lucas' },
        { value: 'Agent Victor', text: 'Agent Victor' },
        { value: 'Kai - TC1', text: 'Kai - TC1' },
        { value: 'JC666', text: 'JC666' },
        { value: 'Wiracle Vincent', text: 'Wiracle Vincent' },
    { value: 'KelvinLim - D1', text: 'KelvinLim - D1' },

        // 会员和团体
        { value: 'BNI Member', text: 'BNI Member' },
        { value: 'PS Member', text: 'PS Member' },
        { value: 'PS Badminton Team & Family', text: 'PS Badminton Team & Family' },

        // 特殊服务类别
        { value: 'Sim Card', text: 'Sim Card' },
        { value: 'SIM Card + Paging', text: 'SIM Card + Paging' },
        { value: 'Paging', text: 'Paging' },
        { value: 'Rental', text: 'Rental' },
        { value: 'Rent To Own', text: 'Rent To Own' },
        { value: 'Penalty', text: 'Penalty' },

        // 其他商业合作
        { value: '789 Genting', text: '789 Genting' },
        { value: 'The Little Series', text: 'The Little Series' },
        { value: 'Syn', text: 'Syn' },
        { value: 'CEO Chaffer Premium', text: 'CEO Chaffer Premium' },
        { value: 'Link Center (SBH)', text: 'Link Center (SBH)' },
        { value: 'ATV Borneo Sabah', text: 'ATV Borneo Sabah' },

        // 测试和开发环境 (保留用于开发)
        { value: 'diagnosis-test', text: 'diagnosis-test' },
        { value: 'Demo', text: 'Demo' },

        // 通用选项
        { value: 'Other', text: '其他' }
    ];

    /**
     * 获取指定用户ID或邮箱的OTA配置
     * @param {number|string} identifier - 用户ID或邮箱
     * @returns {object|null} 对应的OTA配置，未找到则返回null
     */
    function getConfig(identifier) {
        if (!identifier) return null;
        // 统一将标识符转为小写字符串，以匹配邮箱
        const key = typeof identifier === 'string' ? identifier.toLowerCase() : identifier;
        return otaChannelMapping[key] || null;
    }

    // 暴露到OTA命名空间
    // 不再暴露 getConfig / commonChannels，若旧代码访问则给出警告
    if (!window.OTA.otaChannelMapping) {
        window.OTA.otaChannelMapping = {
            getConfig: () => { console.warn('[DEPRECATED] otaChannelMapping.getConfig 已废弃，请使用 user-permissions-config 提供的 API'); return null; },
            commonChannels: []
        };
    }

})();